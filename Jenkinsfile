#!groovy
@Library('send-notification') _
pipeline {

    agent { label "linux-slave" }
    options {
        buildDiscarder(logRotator(numToKeepStr:'20'))
        timeout(time:30, unit: 'MINUTES')
        disableConcurrentBuilds()
    }

    environment {
        DEVOPS_ALERTS_SLACK_WEBHOOK = credentials('DEVOPS_ALERTS_SLACK_WEBHOOK')
        ENVIRONMENT                 = "nonprod"
        APP_NAME                    = "aggregator-gateway"
    }

    stages {
        stage('Start') {
            steps {
                sendNotifications 'STARTED'
            }
        }

        stage('Docker Login') {
            steps {
                script {
                       sh('aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_NAME')
                }
            }
        }

        stage('Download git repos') {
            steps {
                parallel (
                    a: { sh('<NAME_EMAIL>:paytmteam/merchant-devops-toolkit.git')},
                )
            }
        }

        stage('Build and Upload Docker image to Artifactory') {
               steps {
                   script {
                       if (env.GIT_BRANCH == "origin/production") {
                            def status = sh(returnStatus: true, script: "make test")
                            if (status == 0) {
                                echo '########################################'
                                echo '##### PRD DOCKER IMAGE ALREADY EXISTS ##'
                                echo '########################################'
                                currentBuild.result = 'UNSTABLE'
                                return
                            }
                            else {
                                sh('make build')
                                sh('make s3push')
                                sh('make dockerbuild')
                                sh('make tag')
                                def statusimagemaster = sh(returnStatus: true, script: "make master-scan-image")
                                if (statusimagemaster == 0) {
                                    sh('make push')
                                    sh('make release')
                                    sh('make jenkins-to-slack')
                                } else {
                                    echo '########################################'
                                    echo '##### IMAGE SCANNING FAILED ############'
                                    echo '########################################'
                                    currentBuild.result = 'FAILED'
                                    return
                                }
                            }
                        }
                    }
                }
            }

        stage('Build and Upload Docker image to Artifactory for Non-Master Branch') {
               steps {
                   script {
                       if (env.GIT_BRANCH != "origin/production") {
                            def status = sh(returnStatus: true, script: "make test-non-master")
                            if (status == 0) {
                                echo '########################################'
                                echo '##### DEV DOCKER IMAGE ALREADY EXISTS ##'
                                echo '########################################'
                                currentBuild.result = 'UNSTABLE'
                                return
                            }
                            else {
                                sh('make build')
                                sh('make s3push')
                                sh('make dockerbuild')
                                def statusimage = sh(returnStatus: true, script: "make non-master-scan-image")
                                if (statusimage == 0) {
                                    sh('make push')
                                }
                                else {
                                    echo '########################################'
                                    echo '##### IMAGE SCANNING FAILED ############'
                                    echo '########################################'
                                    currentBuild.result = 'FAILED'
                                    return
                                }
                            }
                       }
                   }
               }
           }
    }

    post {
        always {
            sendNotifications currentBuild.result
        }
        cleanup {
            cleanWs()
        }
    }
}
