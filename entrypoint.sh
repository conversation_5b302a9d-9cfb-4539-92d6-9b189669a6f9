#!/bin/sh
#Downloading the application properties file from s3 bucket

export DATE=$(date "+%d-%m-%Y")

aws s3 cp s3://${BUCKET_NAME}/${ENVIRONMENT}/merchant-aggregator/${SERVICE_TYPE}/properties/project-${SPRING_PROFILES_ACTIVE}.properties ./project-${SPRING_PROFILES_ACTIVE}.properties

if [[ ${ENABLE_PINPOINT:-false} == true ]]; then
  PINPOINT_COLLECTOR_IP="${PINPOINT_COLLECTOR_IP:-apm-merchant.paytmdgt.io}"
  aws s3 cp s3://${BUCKET_NAME}/packages/pinpoint/pinpoint-agent-${PINPOINT_VERSION:-2.3.3}.tar.gz .
  tar -xf pinpoint-agent-${PINPOINT_VERSION:-2.3.3}.tar.gz
  sed -i "/profiler.collector.ip=/ s/=.*/=${PINPOINT_COLLECTOR_IP}/" ./pinpoint-agent-${PINPOINT_VERSION:-2.3.3}/profiles/release/pinpoint.config
  sed -i "/profiler.transport.grpc.collector.ip=/ s/=.*/=${PINPOINT_COLLECTOR_IP}/" ./pinpoint-agent-${PINPOINT_VERSION:-2.3.3}/profiles/release/pinpoint.config

  if [[ -f ./pinpoint-agent-${PINPOINT_VERSION:-2.3.3}/pinpoint-bootstrap-${PINPOINT_VERSION:-2.3.3}.jar ]]; then
    export HOST_NAME=$(echo $HOSTNAME | awk -F"-" '{print $(NF-1)"-"$NF}')
    export SERVICE_NAME="AGG-${ENVIRONMENT}-bffv2"
    export JAVA_AGENT="${JAVA_AGENT} -javaagent:./pinpoint-agent-${PINPOINT_VERSION:-2.3.3}/pinpoint-bootstrap-${PINPOINT_VERSION:-2.3.3}.jar -Dpinpoint.applicationName=${SERVICE_NAME} -Dpinpoint.agentId=${HOST_NAME} "
  fi
fi

if [[ $ENABLE_NEW_RELIC == "true" ]]; then
   aws s3 cp s3://${BUCKET_NAME}/newrlic/newrelic.jar ./newrelic.jar
   export JAVA_AGENT="${JAVA_AGENT} -javaagent:./newrelic.jar "
fi

if [[ ! -z $JVM_MIN  &&  ! -z $JVM_MAX ]]; then
  export JAVA_AGENT="${JAVA_AGENT} -Xms${JVM_MIN}m -Xmx${JVM_MAX}m"
fi

java ${JAVA_AGENT} -jar ./aggregator-gateway-v2.jar
