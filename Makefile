MAINTAINER="<PERSON><PERSON> <<EMAIL>>"
ORGANIZATION="Paytm"
NAMESPACE="merchant-aggregator-gateway"

NAME="merchant-aggregator-gateway"
DESCRIPTION="This image for the aggregator-gateway application"
VERSION="0.88.0"

BUILD_DATE=$(shell date -u +"%y-%m-%dt%h:%m:%sz")
DATE=$(shell date +"%d%m%Y%H%M")
VCS_REF=$(shell git rev-parse HEAD)
FULL_PATH=$(REPOSITORY)/$(NAMESPACE)
FULL_PATH_DEV=$(REPOSITORY_DEV)/$(NAMESPACE)

.PHONY: build
build:
	mvn clean package -am -f pom.xml -DskipTests

.PHONY: s3push
s3push:
	aws s3 cp target/aggregator-gateway-v2.jar s3://$(ARTIFACT_BUCKET_NAME)/$(ENVIRONMENT)/merchant-aggregator/artifacts/aggregator-gateway-v2/aggregator-gateway-v2.$(DATE).jar

.PHONY: dockerbuild
dockerbuild:
	docker build \
		-t $(FULL_PATH_DEV):$(VCS_REF) \
		--build-arg ORGANIZATION=$(ORGANIZATION) \
		--build-arg ENVIRONMENT=$(ENVIRONMENT) \
		--build-arg BUCKET_NAME=$(BUCKET_NAME) \
		--build-arg DATE=$(DATE) \
		.

.PHONY: test
test:
	docker pull $(FULL_PATH):$(VERSION)

.PHONY: test-non-master
test-non-master:
	docker pull $(FULL_PATH_DEV):$(VCS_REF)

.PHONY: tag
tag:
	docker tag $(FULL_PATH_DEV):$(VCS_REF) $(FULL_PATH):$(VERSION)

.PHONY: release
release:
	docker push $(FULL_PATH):$(VERSION)

.PHONY: push
push:
	docker push $(FULL_PATH_DEV):$(VCS_REF)

.PHONY: master-scan-image
master-scan-image:
	./merchant-devops-toolkit/scripts/image_scan.sh HIGH,CRITICAL $(FULL_PATH):$(VERSION)

.PHONY: non-master-scan-image
non-master-scan-image:
	./merchant-devops-toolkit/scripts/image_scan.sh HIGH,CRITICAL $(FULL_PATH_DEV):$(VCS_REF)

.PHONY: jenkins-to-slack
jenkins-to-slack:
	./merchant-devops-toolkit/scripts/slack/notify.sh "paytm-payouts-alerts" "New Docker image was released: *${NAME}:${VERSION}* & uploaded to ECR"

