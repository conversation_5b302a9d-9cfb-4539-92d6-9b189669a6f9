FROM 901553615594.dkr.ecr.ap-south-1.amazonaws.com/openjdk21:v1
MAINTAINER "Paytm <<EMAIL>>"

#Directory for application
RUN mkdir -p /var/www/aggregator-gateway

#Setup WEB_ROOT, TZ variables
ENV TZ=Asia/Kolkata
ENV WEB_ROOT /var/www/aggregator-gateway
ENV BUCKET_NAME=""
ENV ENVIRONMENT=""
ENV SPRING_PROFILES_ACTIVE=""
ENV SERVICE_TYPE=""

#Port expose to access containter from host/outside
WORKDIR /var/www/aggregator-gateway
expose 8080

#Copy jar file from mutistage.
COPY target/aggregator-gateway-v2.jar $WEB_ROOT/aggregator-gateway-v2.jar
COPY entrypoint.sh $WEB_ROOT/entrypoint.sh

ENTRYPOINT ["./entrypoint.sh"]
