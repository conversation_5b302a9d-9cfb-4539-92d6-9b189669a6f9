package com.paytm.pgdashboard.commons.dto;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ViewVOTest {

    @Test
    void testDefaultConstructor() {
        ViewVO viewVO = new ViewVO();
        assertNull(viewVO.getViewId());
        assertNull(viewVO.getViewName());
        assertNull(viewVO.getAction());
        assertNull(viewVO.getRequiredViews());
        assertEquals(0, viewVO.getIsEnabled());
        assertNull(viewVO.getType());
        assertNull(viewVO.getSubType());
    }

    @Test
    void testConstructorWithParameters() {
        String[] requiredViews = {"view1", "view2"};
        ViewVO viewVO = new ViewVO(1L, "Test View", "Test Action", 1, requiredViews, "Test Type", "Test SubType");

        assertEquals(1L, viewVO.getViewId());
        assertEquals("Test View", viewVO.getViewName());
        assertEquals("Test Action", viewVO.getAction());
        assertArrayEquals(requiredViews, viewVO.getRequiredViews());
        assertEquals(1, viewVO.getIsEnabled());
        assertEquals("Test Type", viewVO.getType());
        assertEquals("Test SubType", viewVO.getSubType());
    }

    @Test
    void testConstructorWithRequiredViewsAsString() {
        ViewVO viewVO = new ViewVO(1L, "Test View", "Test Action", 1, "view1,view2", "Test Type", "Test SubType");

        assertEquals(1L, viewVO.getViewId());
        assertEquals("Test View", viewVO.getViewName());
        assertEquals("Test Action", viewVO.getAction());
        assertArrayEquals(new String[]{"view1", "view2"}, viewVO.getRequiredViews());
        assertEquals(1, viewVO.getIsEnabled());
        assertEquals("Test Type", viewVO.getType());
        assertEquals("Test SubType", viewVO.getSubType());
    }

    @Test
    void testGettersAndSetters() {
        ViewVO viewVO = new ViewVO();
        String[] requiredViews = {"view1", "view2"};

        viewVO.setViewId(1L);
        viewVO.setViewName("Test View");
        viewVO.setAction("Test Action");
        viewVO.setIsEnabled(1);
        viewVO.setRequiredViews(requiredViews);
        viewVO.setType("Test Type");
        viewVO.setSubType("Test SubType");

        assertEquals(1L, viewVO.getViewId());
        assertEquals("Test View", viewVO.getViewName());
        assertEquals("Test Action", viewVO.getAction());
        assertArrayEquals(requiredViews, viewVO.getRequiredViews());
        assertEquals(1, viewVO.getIsEnabled());
        assertEquals("Test Type", viewVO.getType());
        assertEquals("Test SubType", viewVO.getSubType());
    }

    @Test
    void testHashCode() {
        ViewVO viewVO1 = new ViewVO(1L, "Test View", "Test Action", 1, "view1,view2", "Test Type", "Test SubType");
        ViewVO viewVO2 = new ViewVO(1L, "Test View", "Test Action", 1, "view1,view2", "Test Type", "Test SubType");

        assertEquals(viewVO1.hashCode(), viewVO2.hashCode());
    }

    @Test
    void testEquals() {
        ViewVO viewVO1 = new ViewVO(1L, "Test View", "Test Action", 1, "view1,view2", "Test Type", "Test SubType");
        ViewVO viewVO2 = new ViewVO(1L, "Test View", "Test Action", 1, "view1,view2", "Test Type", "Test SubType");

        assertEquals(viewVO1, viewVO2);
    }
}