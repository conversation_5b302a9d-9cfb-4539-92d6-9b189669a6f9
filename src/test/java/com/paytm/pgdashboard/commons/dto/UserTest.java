package com.paytm.pgdashboard.commons.dto;

import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

public class UserTest {

    @Test
    public void testGettersAndSetters() {
        User user = getUser();

        assertEquals("1", user.getId());
        assertEquals("Test User", user.getUsername());
        assertEquals("Test", user.getFirstName());
        assertEquals("User", user.getLastName());
        assertEquals("<EMAIL>", user.getEmail());
        assertEquals("**********", user.getMobile());
        assertEquals(1, user.getIsMerchant());
        assertEquals("UID", user.getUid());
        assertEquals("EID", user.getEid());
        assertEquals("GUID", user.getGuid());
        assertEquals(Long.valueOf(1), user.getCurrentMerchant());
        assertEquals(1, user.getIsEditable());
        assertEquals(Long.valueOf(1), user.getAggregatorId());
        assertTrue(user.getAccountPrimary());
        assertEquals("PaytmSSOToken", user.getPaytmSSOToken());
        assertEquals("WalletSSOToken", user.getWalletSSOToken());
        assertEquals("AccessToken", user.getAccessToken());
        assertEquals("2022-01-01 00:00:00.0", user.getUpdateTimestamp());
        assertEquals("Test User", user.getFullName());
        assertEquals("en_US", user.getCurrentLocale());
        assertTrue(user.getIsDemoUser());
        assertTrue(user.getQuickUpdate());
        assertEquals("**********", user.getPhone());
        assertEquals("Test User", user.getDisplayName());
        assertEquals("US", user.getCountryCode());
        assertEquals("UserToken", user.getUserToken());
    }

    @Test
    public void testUserConstructor() {
        User originalUser = getUser();

        User copiedUser = new User(originalUser);

        assertEquals(originalUser.getId(), copiedUser.getId());
        assertEquals(originalUser.getUsername(), copiedUser.getUsername());
        assertEquals(originalUser.getFirstName(), copiedUser.getFirstName());
        assertEquals(originalUser.getMiddleName(), copiedUser.getMiddleName());
        assertEquals(originalUser.getLastName(), copiedUser.getLastName());
        assertEquals(originalUser.getStatus(), copiedUser.getStatus());
        assertEquals(originalUser.getType(), copiedUser.getType());
        assertEquals(originalUser.getEmail(), copiedUser.getEmail());
        assertEquals(originalUser.getMobile(), copiedUser.getMobile());
        assertEquals(originalUser.getIsMerchant(), copiedUser.getIsMerchant());
        assertEquals(originalUser.getUid(), copiedUser.getUid());
        assertEquals(originalUser.getEid(), copiedUser.getEid());
        assertEquals(originalUser.getGuid(), copiedUser.getGuid());
        assertEquals(originalUser.getCurrentMerchant(), copiedUser.getCurrentMerchant());
        assertEquals(originalUser.getIsEditable(), copiedUser.getIsEditable());
        assertEquals(originalUser.getAggregatorId(), copiedUser.getAggregatorId());
        assertEquals(originalUser.getAccountPrimary(), copiedUser.getAccountPrimary());
        assertEquals(originalUser.getPaytmSSOToken(), copiedUser.getPaytmSSOToken());
        assertEquals(originalUser.getWalletSSOToken(), copiedUser.getWalletSSOToken());
        assertEquals(originalUser.getAccessToken(), copiedUser.getAccessToken());
        assertEquals(originalUser.getUpdateTimestamp(), copiedUser.getUpdateTimestamp());
        assertEquals(originalUser.getFullName(), copiedUser.getFullName());
        assertEquals(originalUser.getIsDemoUser(), copiedUser.getIsDemoUser());
        assertEquals(originalUser.getQuickUpdate(), copiedUser.getQuickUpdate());
        assertEquals(originalUser.getPhone(), copiedUser.getPhone());
        assertEquals(originalUser.getDisplayName(), copiedUser.getDisplayName());
        assertEquals(originalUser.getCountryCode(), copiedUser.getCountryCode());
        assertEquals(originalUser.getIsDemoUser(), copiedUser.getIsDemoUser());
    }

    private static User getUser() {
        User user = new User();
        user.setId("1");
        user.setUsername("Test User");
        user.setFirstName("Test");
        user.setMiddleName("Test");
        user.setLastName("User");
        user.setStatus("Active");
        user.setType("User");
        user.setEmail("<EMAIL>");
        user.setMobile("**********");
        user.setIsMerchant(1);
        user.setUid("UID");
        user.setEid("EID");
        user.setGuid("GUID");
        user.setCurrentMerchant(1L);
        user.setIsEditable(1);
        user.setAggregatorId(1L);
        user.setAccountPrimary(true);
        user.setPaytmSSOToken("PaytmSSOToken");
        user.setWalletSSOToken("WalletSSOToken");
        user.setAccessToken("AccessToken");
        user.setUpdateTimestamp("2022-01-01 00:00:00.0");
        user.setFullName("Test User");
        user.setIsDemoUser(true);
        user.setQuickUpdate(true);
        user.setPhone("**********");
        user.setDisplayName("Test User");
        user.setCountryCode("US");
        user.setIsDemoUser(true);
        user.setCurrentLocale("en_US");
        user.setUserToken("UserToken");
        return user;
    }

    @Test
    public void testAddMerchant() {
        User user = new User();
        Merchant merchant = new Merchant();
        merchant.setId(1L);
        merchant.setName("Test Merchant");

        user.addMerchant(merchant);

        assertEquals(1, user.getMerchants().size());
        assertEquals(Long.valueOf(1L), Long.valueOf(user.getMerchants().get(0).getId()));
        assertEquals("Test Merchant", user.getMerchants().get(0).getName());
    }

    @Test
    public void testGetPrimaryMerchant() {
        User user = new User();
        Merchant merchant = new Merchant();
        merchant.setId(1L);
        merchant.setName("Test Merchant");

        user.addMerchant(merchant);

        Optional<Merchant> primaryMerchant = user.getPrimaryMerchant();

        assertTrue(primaryMerchant.isPresent());
        assertEquals(Long.valueOf(1), Long.valueOf(primaryMerchant.get().getId()));
        assertEquals("Test Merchant", primaryMerchant.get().getName());
    }

    @Test
    public void testSetValuesFromNewKeys() {
        User user = new User();
        user.setPhone("**********");
        user.setDisplayName("Test User");

        user.setValuesFromNewKeys("1");

        assertEquals("1", user.getId());
        assertEquals("**********", user.getMobile());
        assertEquals("Test User", user.getUsername());
    }
}
