package com.paytm.pgdashboard.commons.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class MerchantTest {

    @InjectMocks
    private Merchant merchant;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGettersAndSetters() {
        merchant.setId(1L);
        merchant.setName("Test Merchant");
        merchant.setGuid("GUID");
        merchant.setAggregator(true);
        merchant.setMid("MID");
        merchant.setType("Type");
        merchant.setEmail("<EMAIL>");
        merchant.setMobile("**********");
        merchant.setMigrated(true);
        merchant.setPgonly(true);
        merchant.setMerchantType("Merchant Type");
        merchant.setAccountPrimary("Account Primary");
        merchant.setWalletOnly(true);
        merchant.setIsActive(true);
        merchant.setPgpOnly(true);
        merchant.setBetaAccess(true);
        merchant.setBetaViewOnly(true);
        merchant.setCreatedOn("2022-01-01 00:00:00.0");
        merchant.setForceEnabled(true);
        merchant.setCustomSettlemntEnabled(true);
        merchant.setIsReseller(true);
        merchant.setResellerType("Reseller Type");
        merchant.setBankEditAllowed(true);
        merchant.setPosProvider(true);
        merchant.setIsDelayedSettlement(true);
        merchant.setSettlementType("Settlement Type");
        merchant.setInactiveState("Inactive State");
        merchant.setInactiveDate("2022-01-01");
        merchant.setEntityType("Entity Type");
        merchant.setnLevelHierarchyEnabled(true);
        merchant.setDummyAggregator(true);
        merchant.setIsBwReconEnabled(true);
        merchant.seteRupiEnabled(true);
        merchant.setIsPreAuthEnabled(true);
        merchant.setStoreCashCloneMid("Store Cash Clone MID");
        merchant.setPpslMigrated(true);
        merchant.setPpslCandidate(true);
        merchant.setResellerId("Reseller ID");
        merchant.setDummyForceEnabled(false);
        merchant.setIsMerchant(1);
        merchant.setIsSdMerchant(false);
        merchant.setHash("hash");
        merchant.setStatus("status");
        merchant.setIsChild(false);
        merchant.setKybid("kybid");

        assertEquals(1L, merchant.getId());
        assertEquals("Test Merchant", merchant.getName());
        assertEquals("GUID", merchant.getGuid());
        assertTrue(merchant.getAggregator());
        assertEquals("MID", merchant.getMid());
        assertEquals("Type", merchant.getType());
        assertEquals("<EMAIL>", merchant.getEmail());
        assertEquals("**********", merchant.getMobile());
        assertTrue(merchant.isMigrated());
        assertTrue(merchant.getMigrated());
        assertTrue(merchant.isPgonly());
        assertTrue(merchant.getPgonly());
        assertEquals("Merchant Type", merchant.getMerchantType());
        assertEquals("Account Primary", merchant.getAccountPrimary());
        assertTrue(merchant.isWalletOnly());
        assertTrue(merchant.getWalletOnly());
        assertTrue(merchant.getIsActive());
        assertTrue(merchant.getPgpOnly());
        assertTrue(merchant.isBetaAccess());
        assertTrue(merchant.getBetaAccess());
        assertTrue(merchant.isBetaViewOnly());
        assertTrue(merchant.getBetaViewOnly());
        assertEquals("2022-01-01 00:00:00.0", merchant.getCreatedOn());
        assertTrue(merchant.getForceEnabled());
        assertTrue(merchant.getCustomSettlemntEnabled());
        assertTrue(merchant.getIsReseller());
        assertEquals("Reseller Type", merchant.getResellerType());
        assertTrue(merchant.isBankEditAllowed());
        assertTrue(merchant.getBankEditAllowed());
        assertTrue(merchant.getIsPosProvider());
        assertTrue(merchant.getIsDelayedSettlement());
        assertEquals("Settlement Type", merchant.getSettlementType());
        assertEquals("Inactive State", merchant.getInactiveState());
        assertEquals("2022-01-01", merchant.getInactiveDate());
        assertEquals("Entity Type", merchant.getEntityType());
        assertTrue(merchant.getnLevelHierarchyEnabled());
        assertTrue(merchant.getDummyAggregator());
        assertTrue(merchant.getIsBwReconEnabled());
        assertTrue(merchant.geteRupiEnabled());
        assertTrue(merchant.getIsPreAuthEnabled());
        assertEquals("Store Cash Clone MID", merchant.getStoreCashCloneMid());
        assertTrue(merchant.getPpslMigrated());
        assertTrue(merchant.getPpslCandidate());
        assertEquals("Reseller ID", merchant.getResellerId());
        assertFalse(merchant.getDummyForceEnabled());
        assertEquals(1, merchant.getIsMerchant());
        assertFalse(merchant.getIsSdMerchant());
        assertEquals("hash", merchant.getHash());
        assertEquals("status", merchant.getStatus());
        assertFalse(merchant.getIsChild());
        assertEquals("kybid", merchant.getKybid());
    }

    @Test
    void testToString() {
        String expected = "Merchant [id=0, name=null, guid=null, roles=[], permissions=[], aggregator=false, mid=null, " +
                "type=null, isMerchant=0, email=null, mobile=null, migrated=false, pgonly=false, merchantType=null, " +
                "accountPrimary=null, walletOnly=null, isActive=true, pgpOnly=null, betaAccess=null, betaViewOnly=null, " +
                "createdOn=null, group=null, roleList=null, status=null, isSdMerchant=null, hash=null, adminUserId=null, " +
                "isChild=null, kybid=null, forceEnabled=null, customSettlemntEnabled=null, dummyForceEnabled=null, " +
                "isReseller=false, resellerType=null, solutionType=null, obChannel=null, categoryLabel=null, bankEditAllowed=null, " +
                "isPosProvider=null, isDelayedSettlement=null, settlementType=null, inactiveState=null, inactiveDate=null, " +
                "entityType=null, nLevelHierarchyEnabled=null, dummyAggregatornull, isBwReconEnabled=null, eRupiEnabled=null, " +
                "isPreAuthEnabled=null]";
        assertEquals(expected, merchant.toString());
    }

    @Test
    void testGetCreatedOnConverted1() {
        merchant.setCreatedOn("2022-01-01 00:00:00.0");
        Optional<LocalDateTime> expected = Optional.of(LocalDateTime.of(2022, 1, 1, 0, 0, 0));
        assertEquals(expected, merchant.getCreatedOnConverted());
    }

    @Test
    void testGetCreatedOnConverted2() {
        merchant.setCreatedOn("2022-01-01 00:00:00.000");
        Optional<LocalDateTime> expected = Optional.of(LocalDateTime.of(2022, 1, 1, 0, 0, 0));
        assertEquals(expected, merchant.getCreatedOnConverted());
    }

    @Test
    void testGetCreatedOnConvertedNull() {
        assertEquals(Optional.empty(), merchant.getCreatedOnConverted());
    }

    @Test
    void testMerchantConstructor1() {
        Merchant merchant = new Merchant(1L, "Test Merchant", "GUID", true, "MID", "Type", "<EMAIL>", "**********", true,
                true, "Merchant Type", true, "Account Primary", true, true, "2022-01-01 00:00:00.0",
                true, true, true, true, "hash", "adminUserIds", true,
                "kybid", true, true, "Reseller Type", true, "Reseller ID", "Solution Type",
                "ObChannel","Category Label",true, "Settlement Type","Inactive State","2022-01-01","Entity Type",
                true, true, true);

        assertEquals(1L, merchant.getId());
        assertEquals("Test Merchant", merchant.getName());
        assertEquals("GUID", merchant.getGuid());
        assertTrue(merchant.getAggregator());
        assertEquals("MID", merchant.getMid());
        assertEquals("Type", merchant.getType());
        assertEquals("<EMAIL>", merchant.getEmail());
        assertEquals("**********", merchant.getMobile());
        assertTrue(merchant.isMigrated());
        assertTrue(merchant.isPgonly());
        assertEquals("Merchant Type", merchant.getMerchantType());
        assertEquals("Account Primary", merchant.getAccountPrimary());
        assertTrue(merchant.isWalletOnly());
        assertTrue(merchant.getIsActive());
        assertTrue(merchant.getPgpOnly());
        assertTrue(merchant.isBetaAccess());
        assertTrue(merchant.isBetaViewOnly());
        assertEquals("2022-01-01 00:00:00.0", merchant.getCreatedOn());
        assertTrue(merchant.getForceEnabled());
        assertTrue(merchant.getCustomSettlemntEnabled());
        assertTrue(merchant.getIsReseller());
        assertEquals("Reseller Type", merchant.getResellerType());
        assertTrue(merchant.isBankEditAllowed());
        assertEquals("Solution Type", merchant.getSolutionType());
        assertEquals("ObChannel", merchant.getObChannel());
        assertEquals("Category Label", merchant.getCategoryLabel());
        assertTrue(merchant.getIsDelayedSettlement());
        assertEquals("Settlement Type", merchant.getSettlementType());
        assertEquals("Inactive State", merchant.getInactiveState());
        assertEquals("2022-01-01", merchant.getInactiveDate());
        assertEquals("Entity Type", merchant.getEntityType());
        assertTrue(merchant.getIsPreAuthEnabled());
        assertTrue(merchant.getPpslCandidate());
        assertTrue(merchant.getPpslMigrated());
    }

    @Test
    void testMerchantConstructor2() {
        Merchant merchant = new Merchant(1L, "Test Merchant", "GUID", true, "MID", "Type", "<EMAIL>", "**********", true,
                true, "Merchant Type", true, "Account Primary", true, true, "2022-01-01 00:00:00.0",
                true, true, true, true, "hash", "adminUserIds", true,
                "kybid", true, true, "Reseller Type", true, "Reseller ID", true, "Solution Type",
                "ObChannel","Category Label",true, "Settlement Type","Inactive State","2022-01-01","Entity Type",
                true, true, true, true, true, true, true);

        assertEquals(1L, merchant.getId());
        assertEquals("Test Merchant", merchant.getName());
        assertEquals("GUID", merchant.getGuid());
        assertTrue(merchant.getAggregator());
        assertEquals("MID", merchant.getMid());
        assertEquals("Type", merchant.getType());
        assertEquals("<EMAIL>", merchant.getEmail());
        assertEquals("**********", merchant.getMobile());
        assertTrue(merchant.isMigrated());
        assertTrue(merchant.isPgonly());
        assertEquals("Merchant Type", merchant.getMerchantType());
        assertEquals("Account Primary", merchant.getAccountPrimary());
        assertTrue(merchant.isWalletOnly());
        assertTrue(merchant.getIsActive());
        assertTrue(merchant.getPgpOnly());
        assertTrue(merchant.isBetaAccess());
        assertTrue(merchant.isBetaViewOnly());
        assertEquals("2022-01-01 00:00:00.0", merchant.getCreatedOn());
        assertTrue(merchant.getForceEnabled());
        assertTrue(merchant.getCustomSettlemntEnabled());
        assertTrue(merchant.getIsReseller());
        assertEquals("Reseller Type", merchant.getResellerType());
        assertTrue(merchant.isBankEditAllowed());
        assertEquals("Solution Type", merchant.getSolutionType());
        assertEquals("ObChannel", merchant.getObChannel());
        assertEquals("Category Label", merchant.getCategoryLabel());
        assertTrue(merchant.getIsDelayedSettlement());
        assertEquals("Settlement Type", merchant.getSettlementType());
        assertEquals("Inactive State", merchant.getInactiveState());
        assertEquals("2022-01-01", merchant.getInactiveDate());
        assertEquals("Entity Type", merchant.getEntityType());
        assertTrue(merchant.getnLevelHierarchyEnabled());
        assertTrue(merchant.getDummyAggregator());
        assertTrue(merchant.getIsBwReconEnabled());
        assertTrue(merchant.geteRupiEnabled());
        assertTrue(merchant.getIsPreAuthEnabled());
        assertTrue(merchant.getPpslCandidate());
        assertTrue(merchant.getPpslMigrated());
    }

    @Test
    void testMerchantConstructor3() {
        Merchant originalMerchant = getMerchant();

        Merchant copiedMerchant = new Merchant(originalMerchant);

        assertEquals(originalMerchant.getId(), copiedMerchant.getId());
        assertEquals(originalMerchant.getName(), copiedMerchant.getName());
        assertEquals(originalMerchant.getGuid(), copiedMerchant.getGuid());
        assertEquals(originalMerchant.getAggregator(), copiedMerchant.getAggregator());
        assertEquals(originalMerchant.getMid(), copiedMerchant.getMid());
        assertEquals(originalMerchant.getType(), copiedMerchant.getType());
        assertEquals(originalMerchant.getEmail(), copiedMerchant.getEmail());
        assertEquals(originalMerchant.getMobile(), copiedMerchant.getMobile());
        assertEquals(originalMerchant.isMigrated(), copiedMerchant.isMigrated());
        assertEquals(originalMerchant.isPgonly(), copiedMerchant.isPgonly());
        assertEquals(originalMerchant.getMerchantType(), copiedMerchant.getMerchantType());
        assertEquals(originalMerchant.getAccountPrimary(), copiedMerchant.getAccountPrimary());
        assertEquals(originalMerchant.isWalletOnly(), copiedMerchant.isWalletOnly());
        assertEquals(originalMerchant.getIsActive(), copiedMerchant.getIsActive());
        assertEquals(originalMerchant.getPgpOnly(), copiedMerchant.getPgpOnly());
        assertEquals(originalMerchant.isBetaAccess(), copiedMerchant.isBetaAccess());
        assertEquals(originalMerchant.isBetaViewOnly(), copiedMerchant.isBetaViewOnly());
        assertEquals(originalMerchant.getCreatedOn(), copiedMerchant.getCreatedOn());
        assertEquals(originalMerchant.getForceEnabled(), copiedMerchant.getForceEnabled());
        assertEquals(originalMerchant.getCustomSettlemntEnabled(), copiedMerchant.getCustomSettlemntEnabled());
        assertEquals(originalMerchant.getIsReseller(), copiedMerchant.getIsReseller());
        assertEquals(originalMerchant.getResellerType(), copiedMerchant.getResellerType());
        assertEquals(originalMerchant.isBankEditAllowed(), copiedMerchant.isBankEditAllowed());
        assertEquals(originalMerchant.getIsPosProvider(), copiedMerchant.getIsPosProvider());
        assertEquals(originalMerchant.getSolutionType(), copiedMerchant.getSolutionType());
        assertEquals(originalMerchant.getObChannel(), copiedMerchant.getObChannel());
        assertEquals(originalMerchant.getCategoryLabel(), copiedMerchant.getCategoryLabel());
        assertEquals(originalMerchant.getIsDelayedSettlement(), copiedMerchant.getIsDelayedSettlement());
        assertEquals(originalMerchant.getSettlementType(), copiedMerchant.getSettlementType());
        assertEquals(originalMerchant.getInactiveState(), copiedMerchant.getInactiveState());
        assertEquals(originalMerchant.getInactiveDate(), copiedMerchant.getInactiveDate());
        assertEquals(originalMerchant.getEntityType(), copiedMerchant.getEntityType());
        assertEquals(originalMerchant.getAdminUserId(), copiedMerchant.getAdminUserId());
        assertEquals(originalMerchant.getIsBwReconEnabled(), copiedMerchant.getIsBwReconEnabled());
        assertEquals(originalMerchant.getResellerId(), copiedMerchant.getResellerId());
        assertEquals(originalMerchant.getnLevelHierarchyEnabled(), copiedMerchant.getnLevelHierarchyEnabled());
        assertEquals(originalMerchant.getDummyAggregator(), copiedMerchant.getDummyAggregator());
        assertEquals(originalMerchant.geteRupiEnabled(), copiedMerchant.geteRupiEnabled());
        assertEquals(originalMerchant.getIsPreAuthEnabled(), copiedMerchant.getIsPreAuthEnabled());
        assertEquals(originalMerchant.getPpslMigrated(), copiedMerchant.getPpslMigrated());
        assertEquals(originalMerchant.getPpslCandidate(), copiedMerchant.getPpslCandidate());
    }

    @Test
    void testMerchantConstructor4() {
        Merchant merchant = new Merchant(1L, "Test Merchant");

        assertEquals(1L, merchant.getId());
        assertEquals("Test Merchant", merchant.getName());
    }

    private static Merchant getMerchant() {
        Merchant originalMerchant = new Merchant();
        originalMerchant.setId(1L);
        originalMerchant.setName("Test Merchant");
        originalMerchant.setGuid("GUID");
        originalMerchant.setAggregator(true);
        originalMerchant.setMid("MID");
        originalMerchant.setType("Type");
        originalMerchant.setEmail("<EMAIL>");
        originalMerchant.setMobile("**********");
        originalMerchant.setMigrated(true);
        originalMerchant.setPgonly(true);
        originalMerchant.setMerchantType("Merchant Type");
        originalMerchant.setAccountPrimary("Account Primary");
        originalMerchant.setWalletOnly(true);
        originalMerchant.setIsActive(true);
        originalMerchant.setPgpOnly(true);
        originalMerchant.setBetaAccess(true);
        originalMerchant.setBetaViewOnly(true);
        originalMerchant.setCreatedOn("2022-01-01 00:00:00.0");
        originalMerchant.setForceEnabled(true);
        originalMerchant.setCustomSettlemntEnabled(true);
        originalMerchant.setIsReseller(true);
        originalMerchant.setResellerType("Reseller Type");
        originalMerchant.setBankEditAllowed(true);
        originalMerchant.setPosProvider(true);
        originalMerchant.setSolutionType("Solution Type");
        originalMerchant.setObChannel("ObChannel");
        originalMerchant.setCategoryLabel("Category Label");
        originalMerchant.setIsDelayedSettlement(true);
        originalMerchant.setSettlementType("Settlement Type");
        originalMerchant.setInactiveState("Inactive State");
        originalMerchant.setInactiveDate("2022-01-01");
        originalMerchant.setEntityType("Entity Type");
        originalMerchant.setAdminUserId("AdminUserId");
        originalMerchant.setIsBwReconEnabled(true);
        originalMerchant.setResellerId("ResellerId");
        originalMerchant.setnLevelHierarchyEnabled(true);
        originalMerchant.setDummyAggregator(true);
        originalMerchant.seteRupiEnabled(true);
        originalMerchant.setIsPreAuthEnabled(true);
        originalMerchant.setPpslMigrated(true);
        originalMerchant.setPpslCandidate(true);
        return originalMerchant;
    }

}
