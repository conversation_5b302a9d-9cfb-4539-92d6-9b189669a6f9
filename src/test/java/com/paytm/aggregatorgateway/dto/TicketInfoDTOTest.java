package com.paytm.aggregatorgateway.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;

class TicketInfoDTOTest {

    private TicketInfoDTO ticketInfoDTO;

    @BeforeEach
    public void setup() {
        ticketInfoDTO = new TicketInfoDTO();
    }

    @Test
    void testSetAndGetSource() {
        String source = "testSource";
        ticketInfoDTO.setSource(source);
        assertEquals(source, ticketInfoDTO.getSource());
    }

    @Test
    void testSetAndGetCreatedAt() {
        String createdAt = "testCreatedAt";
        ticketInfoDTO.setCreatedAt(createdAt);
        assertEquals(createdAt, ticketInfoDTO.getCreatedAt());
    }

    @Test
    void testSetAndGetUpdatedAt() {
        String updatedAt = "testUpdatedAt";
        ticketInfoDTO.setUpdatedAt(updatedAt);
        assertEquals(updatedAt, ticketInfoDTO.getUpdatedAt());
    }

    @Test
    void testSetAndGetCustomFields() {
        HashMap<String, Object> customFields = new HashMap<>();
        customFields.put("testKey", "testValue");
        ticketInfoDTO.setCustom_fields(customFields);
        assertEquals(customFields, ticketInfoDTO.getCustom_fields());
    }

    @Test
    void testSetAndGetTags() {
        ticketInfoDTO.setTags(Collections.singletonList("testTag"));
        assertEquals(Collections.singletonList("testTag"), ticketInfoDTO.getTags());
    }

    @Test
    void testSetAndGetRiskComment() {
        String riskComment = "testRiskComment";
        ticketInfoDTO.setRiskComment(riskComment);
        assertEquals(riskComment, ticketInfoDTO.getRiskComment());
    }

    @Test
    void testSetAndGetSubStatus() {
        String subStatus = "testSubStatus";
        ticketInfoDTO.setSubStatus(subStatus);
        assertEquals(subStatus, ticketInfoDTO.getSubStatus());
    }

    @Test
    void testSetAndGetOrigin() {
        int origin = 1;
        ticketInfoDTO.setOrigin(origin);
        assertEquals(origin, ticketInfoDTO.getOrigin());
    }

    @Test
    void testSetAndGetStatus() {
        String status = "testStatus";
        ticketInfoDTO.setStatus(status);
        assertEquals(status, ticketInfoDTO.getStatus());
    }

    @Test
    void testSetAndGetRequesterId() {
        Long requesterId = 1L;
        ticketInfoDTO.setRequesterId(requesterId);
        assertEquals(requesterId, ticketInfoDTO.getRequesterId());
    }

    @Test
    void testCstentity() {
        String cstentity = "testCstentity";
        ticketInfoDTO.setCstentity(cstentity);
        assertEquals(cstentity, ticketInfoDTO.getCstentity());
    }

    @Test
    void testBotParams() {
        Object botParams = new Object();
        ticketInfoDTO.setBotParams(botParams);
        assertEquals(botParams, ticketInfoDTO.getBotParams());
    }

    @Test
    void testTicketIcon() {
        String ticketIcon = "testTicketIcon";
        ticketInfoDTO.setTicketIcon(ticketIcon);
        assertEquals(ticketIcon, ticketInfoDTO.getTicketIcon());
    }

    @Test
    void testGetItemName() {
        String itemName = "testItemName";
        ticketInfoDTO.setItemName(itemName);
        assertEquals(itemName, ticketInfoDTO.getItemName());
    }

    @Test
    void testGetCaseCreationDate() {
        String caseCreationDate = "testCaseCreationDate";
        ticketInfoDTO.setCaseCreationDate(caseCreationDate);
        assertEquals(caseCreationDate, ticketInfoDTO.getCaseCreationDate());
    }

    @Test
    void testGetTicketNumber() {
        String ticketNumber = "testTicketNumber";
        ticketInfoDTO.setTicketNumber(ticketNumber);
        assertEquals(ticketNumber, ticketInfoDTO.getTicketNumber());
    }

    @Test
    void testGetFormattedCreatedDate() {
        String formattedCreatedDate = "testFormattedCreatedDate";
        ticketInfoDTO.setFormattedCreatedDate(formattedCreatedDate);
        assertEquals(formattedCreatedDate, ticketInfoDTO.getFormattedCreatedDate());
    }

    @Test
    void testGetUmpMid() {
        String umpMid = "testUmpMid";
        ticketInfoDTO.setUmpMid(umpMid);
        assertEquals(umpMid, ticketInfoDTO.getUmpMid());
    }

    /*@Test
    public void testToString() {
        TicketInfoDTO ticketInfoDTO = getTicketInfoDTO();

        String expected = "TicketInfoDTO{" +
                "source='testSource'" +
                ", createdAt='testCreatedAt'" +
                ", updatedAt='testUpdatedAt'" +
                ", custom_fields={}" +
                ", tags=[testTag]" +
                ", riskComment='testRiskComment'" +
                ", subStatus='testSubStatus'" +
                ", origin=1" +
                ", status='testStatus'" +
                ", requesterId=1" +
                ", cstentity='testCstentity'" +
                ", botParams={}" +
                ", ticketIcon='testTicketIcon'" +
                '}';
        assertEquals(expected, ticketInfoDTO.toString());
    }

    private static TicketInfoDTO getTicketInfoDTO() {
        TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();
        ticketInfoDTO.setSource("testSource");
        ticketInfoDTO.setCreatedAt("testCreatedAt");
        ticketInfoDTO.setUpdatedAt("testUpdatedAt");
        ticketInfoDTO.setCustom_fields(new HashMap<>());
        ticketInfoDTO.setTags(Collections.singletonList("testTag"));
        ticketInfoDTO.setRiskComment("testRiskComment");
        ticketInfoDTO.setSubStatus("testSubStatus");
        ticketInfoDTO.setOrigin(1);
        ticketInfoDTO.setStatus("testStatus");
        ticketInfoDTO.setRequesterId(1L);
        ticketInfoDTO.setCstentity("testCstentity");
        ticketInfoDTO.setBotParams(new Object());
        ticketInfoDTO.setTicketIcon("testTicketIcon");
        return ticketInfoDTO;
    }*/

}