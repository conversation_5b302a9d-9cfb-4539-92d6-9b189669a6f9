package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DeductionInitiateRequestTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testDeductionInitiateRequest_DefaultConstructor() {
        // Execute
        DeductionInitiateRequest request = new DeductionInitiateRequest();

        // Verify
        assertNull(request.getClientRequestId());
        assertNull(request.getCustomerId());
        assertNull(request.getImages());
        assertNull(request.getCallbackUrl());
    }

    @Test
    void testDeductionInitiateRequest_GettersAndSetters() {
        // Setup
        DeductionInitiateRequest request = new DeductionInitiateRequest();
        request.setClientRequestId("test-request-id");
        request.setCustomerId("test-customer-id");
        request.setCallbackUrl("https://test-callback-url.com");

        List<DeductionInitiateRequest.ImageInfo> images = Arrays.asList(
            new DeductionInitiateRequest.ImageInfo("DM1012489886194796", "cancelledChequePhoto"),
            new DeductionInitiateRequest.ImageInfo("DM1012489886194797", "BankStatement")
        );
        request.setImages(images);

        // Verify
        assertEquals("test-request-id", request.getClientRequestId());
        assertEquals("test-customer-id", request.getCustomerId());
        assertEquals("https://test-callback-url.com", request.getCallbackUrl());
        assertNotNull(request.getImages());
        assertEquals(2, request.getImages().size());
        assertEquals("DM1012489886194796", request.getImages().get(0).getDms_id());
        assertEquals("cancelledChequePhoto", request.getImages().get(0).getDocument_type());
        assertEquals("DM1012489886194797", request.getImages().get(1).getDms_id());
        assertEquals("BankStatement", request.getImages().get(1).getDocument_type());
    }

    @Test
    void testImageInfo_Constructor() {
        // Execute
        DeductionInitiateRequest.ImageInfo imageInfo = new DeductionInitiateRequest.ImageInfo("DM1012489886194796", "cancelledChequePhoto");

        // Verify
        assertEquals("DM1012489886194796", imageInfo.getDms_id());
        assertEquals("cancelledChequePhoto", imageInfo.getDocument_type());
    }

    @Test
    void testImageInfo_DefaultConstructor() {
        // Execute
        DeductionInitiateRequest.ImageInfo imageInfo = new DeductionInitiateRequest.ImageInfo();

        // Verify
        assertNull(imageInfo.getDms_id());
        assertNull(imageInfo.getDocument_type());
    }

    @Test
    void testImageInfo_GettersAndSetters() {
        // Setup
        DeductionInitiateRequest.ImageInfo imageInfo = new DeductionInitiateRequest.ImageInfo();
        imageInfo.setDms_id("DM1012489886194796");
        imageInfo.setDocument_type("cancelledChequePhoto");

        // Verify
        assertEquals("DM1012489886194796", imageInfo.getDms_id());
        assertEquals("cancelledChequePhoto", imageInfo.getDocument_type());
    }

    @Test
    void testDeductionInitiateRequest_Serialization() throws Exception {
        // Setup
        DeductionInitiateRequest request = new DeductionInitiateRequest();
        request.setClientRequestId("test-request-id");
        request.setCustomerId("test-customer-id");
        request.setCallbackUrl("https://test-callback-url.com");

        List<DeductionInitiateRequest.ImageInfo> images = Arrays.asList(
            new DeductionInitiateRequest.ImageInfo("DM1012489886194796", "cancelledChequePhoto")
        );
        request.setImages(images);

        // Execute
        String json = objectMapper.writeValueAsString(request);
        DeductionInitiateRequest deserialized = objectMapper.readValue(json, DeductionInitiateRequest.class);

        // Verify
        assertNotNull(json);
        assertNotNull(deserialized);
        assertEquals("test-request-id", deserialized.getClientRequestId());
        assertEquals("test-customer-id", deserialized.getCustomerId());
        assertEquals("https://test-callback-url.com", deserialized.getCallbackUrl());
        assertNotNull(deserialized.getImages());
        assertEquals(1, deserialized.getImages().size());
        assertEquals("DM1012489886194796", deserialized.getImages().get(0).getDms_id());
        assertEquals("cancelledChequePhoto", deserialized.getImages().get(0).getDocument_type());
    }

    @Test
    void testImageInfo_Serialization() throws Exception {
        // Setup
        DeductionInitiateRequest.ImageInfo imageInfo = new DeductionInitiateRequest.ImageInfo("DM1012489886194796", "cancelledChequePhoto");

        // Execute
        String json = objectMapper.writeValueAsString(imageInfo);
        DeductionInitiateRequest.ImageInfo deserialized = objectMapper.readValue(json, DeductionInitiateRequest.ImageInfo.class);

        // Verify
        assertNotNull(json);
        assertNotNull(deserialized);
        assertEquals("DM1012489886194796", deserialized.getDms_id());
        assertEquals("cancelledChequePhoto", deserialized.getDocument_type());
    }

    @Test
    void testToString() {
        // Setup
        DeductionInitiateRequest request = new DeductionInitiateRequest();
        request.setClientRequestId("test-request-id");
        request.setCustomerId("test-customer-id");
        request.setCallbackUrl("https://test-callback-url.com");

        // Execute
        String toString = request.toString();

        // Verify
        assertNotNull(toString);
        assertTrue(toString.contains("test-request-id"));
        assertTrue(toString.contains("test-customer-id"));
        assertTrue(toString.contains("https://test-callback-url.com"));
    }

    @Test
    void testImageInfo_ToString() {
        // Setup
        DeductionInitiateRequest.ImageInfo imageInfo = new DeductionInitiateRequest.ImageInfo("DM1012489886194796", "cancelledChequePhoto");

        // Execute
        String toString = imageInfo.toString();

        // Verify
        assertNotNull(toString);
        assertTrue(toString.contains("DM1012489886194796"));
        assertTrue(toString.contains("cancelledChequePhoto"));
    }
}
