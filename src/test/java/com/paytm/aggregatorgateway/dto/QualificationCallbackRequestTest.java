package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class QualificationCallbackRequestTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testQualificationCallbackRequest_Serialization() throws Exception {
        // Setup
        QualificationCallbackRequest request = new QualificationCallbackRequest();
        request.setStatus("SUCCESS");
        request.setStatusCode("200");
        request.setStatusMessage("OK");
        request.setRequestId("test-request-id");

        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        response.setProcessedImageFileIds(java.util.Arrays.asList("DM1012489886194796"));

        Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("cancelledChequePhoto");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);

        QualificationCallbackRequest.ParameterValue liveliness = new QualificationCallbackRequest.ParameterValue();
        liveliness.setValue("original");
        liveliness.setConfidence(0.9999);
        params.setLiveliness(liveliness);

        QualificationCallbackRequest.ParameterValue completeness = new QualificationCallbackRequest.ParameterValue();
        completeness.setValue("complete_image");
        completeness.setConfidence(0.95);
        params.setCompleteness(completeness);

        QualificationCallbackRequest.ParameterValue valid = new QualificationCallbackRequest.ParameterValue();
        valid.setValue("valid_image");
        valid.setConfidence(0.999);
        params.setValid(valid);

        imageParams.put("DM1012530130187387", params);
        response.setImageWiseParameters(imageParams);
        request.setResponse(response);

        // Execute
        String json = objectMapper.writeValueAsString(request);
        QualificationCallbackRequest deserialized = objectMapper.readValue(json, QualificationCallbackRequest.class);

        // Verify
        assertNotNull(json);
        assertNotNull(deserialized);
        assertEquals("SUCCESS", deserialized.getStatus());
        assertEquals("200", deserialized.getStatusCode());
        assertEquals("OK", deserialized.getStatusMessage());
        assertEquals("test-request-id", deserialized.getRequestId());
        assertNotNull(deserialized.getResponse());
        assertEquals(1, deserialized.getResponse().getProcessedImageFileIds().size());
        assertEquals("DM1012489886194796", deserialized.getResponse().getProcessedImageFileIds().get(0));
        assertNotNull(deserialized.getResponse().getImageWiseParameters());
        assertEquals(1, deserialized.getResponse().getImageWiseParameters().size());
    }

    @Test
    void testParameterValue_GettersAndSetters() {
        // Setup
        QualificationCallbackRequest.ParameterValue param = new QualificationCallbackRequest.ParameterValue();
        param.setValue("test-value");
        param.setConfidence(0.95);

        // Verify
        assertEquals("test-value", param.getValue());
        assertEquals(0.95, param.getConfidence());
    }

    @Test
    void testImageParameters_GettersAndSetters() {
        // Setup
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("cancelledChequePhoto");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);

        QualificationCallbackRequest.ParameterValue liveliness = new QualificationCallbackRequest.ParameterValue();
        liveliness.setValue("original");
        liveliness.setConfidence(0.9999);
        params.setLiveliness(liveliness);

        QualificationCallbackRequest.ParameterValue completeness = new QualificationCallbackRequest.ParameterValue();
        completeness.setValue("complete_image");
        completeness.setConfidence(0.95);
        params.setCompleteness(completeness);

        QualificationCallbackRequest.ParameterValue valid = new QualificationCallbackRequest.ParameterValue();
        valid.setValue("valid_image");
        valid.setConfidence(0.999);
        params.setValid(valid);

        // Verify
        assertNotNull(params.getDocument_type());
        assertEquals("cancelledChequePhoto", params.getDocument_type().getValue());
        assertEquals(0.9999, params.getDocument_type().getConfidence());

        assertNotNull(params.getLiveliness());
        assertEquals("original", params.getLiveliness().getValue());
        assertEquals(0.9999, params.getLiveliness().getConfidence());

        assertNotNull(params.getCompleteness());
        assertEquals("complete_image", params.getCompleteness().getValue());
        assertEquals(0.95, params.getCompleteness().getConfidence());

        assertNotNull(params.getValid());
        assertEquals("valid_image", params.getValid().getValue());
        assertEquals(0.999, params.getValid().getConfidence());
    }

    @Test
    void testQualificationResponse_GettersAndSetters() {
        // Setup
        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        response.setProcessedImageFileIds(java.util.Arrays.asList("DM1012489886194796", "DM1012489886194797"));

        Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        imageParams.put("DM1012530130187387", params);
        response.setImageWiseParameters(imageParams);

        // Verify
        assertNotNull(response.getProcessedImageFileIds());
        assertEquals(2, response.getProcessedImageFileIds().size());
        assertEquals("DM1012489886194796", response.getProcessedImageFileIds().get(0));
        assertEquals("DM1012489886194797", response.getProcessedImageFileIds().get(1));

        assertNotNull(response.getImageWiseParameters());
        assertEquals(1, response.getImageWiseParameters().size());
        assertTrue(response.getImageWiseParameters().containsKey("DM1012530130187387"));
    }

    @Test
    void testQualificationCallbackRequest_GettersAndSetters() {
        // Setup
        QualificationCallbackRequest request = new QualificationCallbackRequest();
        request.setStatus("SUCCESS");
        request.setStatusCode("200");
        request.setStatusMessage("OK");
        request.setRequestId("test-request-id");

        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        request.setResponse(response);

        // Verify
        assertEquals("SUCCESS", request.getStatus());
        assertEquals("200", request.getStatusCode());
        assertEquals("OK", request.getStatusMessage());
        assertEquals("test-request-id", request.getRequestId());
        assertNotNull(request.getResponse());
    }

    @Test
    void testToString() {
        // Setup
        QualificationCallbackRequest request = new QualificationCallbackRequest();
        request.setStatus("SUCCESS");
        request.setStatusCode("200");
        request.setStatusMessage("OK");
        request.setRequestId("test-request-id");

        // Execute
        String toString = request.toString();

        // Verify
        assertNotNull(toString);
        assertTrue(toString.contains("SUCCESS"));
        assertTrue(toString.contains("200"));
        assertTrue(toString.contains("OK"));
        assertTrue(toString.contains("test-request-id"));
    }
}
