package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class QualificationCallbackResponseTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testQualificationCallbackResponse_Constructor() {
        // Setup
        Map<String, Object> result = new HashMap<>();
        result.put("status", "SUCCESS");
        result.put("statusCode", "200");

        // Execute
        QualificationCallbackResponse response = new QualificationCallbackResponse("QUALIFICATION_PASSED", result);

        // Verify
        assertEquals("QUALIFICATION_PASSED", response.getRequestStatus());
        assertEquals(result, response.getResult());
    }

    @Test
    void testQualificationCallbackResponse_DefaultConstructor() {
        // Execute
        QualificationCallbackResponse response = new QualificationCallbackResponse();

        // Verify
        assertNull(response.getRequestStatus());
        assertNull(response.getResult());
    }

    @Test
    void testQualificationCallbackResponse_GettersAndSetters() {
        // Setup
        QualificationCallbackResponse response = new QualificationCallbackResponse();
        Map<String, Object> result = new HashMap<>();
        result.put("status", "SUCCESS");
        result.put("statusCode", "200");

        // Execute
        response.setRequestStatus("DEDUCTION_AWAITING");
        response.setResult(result);

        // Verify
        assertEquals("DEDUCTION_AWAITING", response.getRequestStatus());
        assertEquals(result, response.getResult());
    }

    @Test
    void testQualificationCallbackResponse_Serialization() throws Exception {
        // Setup
        Map<String, Object> result = new HashMap<>();
        result.put("status", "SUCCESS");
        result.put("statusCode", "200");
        result.put("statusMessage", "Request successful");
        result.put("requestId", "test-request-id");

        QualificationCallbackResponse response = new QualificationCallbackResponse("DEDUCTION_AWAITING", result);

        // Execute
        String json = objectMapper.writeValueAsString(response);
        QualificationCallbackResponse deserialized = objectMapper.readValue(json, QualificationCallbackResponse.class);

        // Verify
        assertNotNull(json);
        assertNotNull(deserialized);
        assertEquals("DEDUCTION_AWAITING", deserialized.getRequestStatus());
        assertNotNull(deserialized.getResult());
    }

    @Test
    void testQualificationCallbackResponse_WithNullResult() throws Exception {
        // Setup
        QualificationCallbackResponse response = new QualificationCallbackResponse("QUALIFICATION_PASSED", null);

        // Execute
        String json = objectMapper.writeValueAsString(response);
        QualificationCallbackResponse deserialized = objectMapper.readValue(json, QualificationCallbackResponse.class);

        // Verify
        assertNotNull(json);
        assertNotNull(deserialized);
        assertEquals("QUALIFICATION_PASSED", deserialized.getRequestStatus());
        assertNull(deserialized.getResult());
    }

    @Test
    void testToString() {
        // Setup
        Map<String, Object> result = new HashMap<>();
        result.put("status", "SUCCESS");
        result.put("statusCode", "200");

        QualificationCallbackResponse response = new QualificationCallbackResponse("DEDUCTION_PASSED", result);

        // Execute
        String toString = response.toString();

        // Verify
        assertNotNull(toString);
        assertTrue(toString.contains("DEDUCTION_PASSED"));
        assertTrue(toString.contains("SUCCESS"));
        assertTrue(toString.contains("200"));
    }
}
