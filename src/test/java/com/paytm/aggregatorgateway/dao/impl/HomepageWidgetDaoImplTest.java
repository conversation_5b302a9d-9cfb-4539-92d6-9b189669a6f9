package com.paytm.aggregatorgateway.dao.impl;

import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class HomepageWidgetDaoImplTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private JdbcTemplate jdbcTemplateSlave;

    @Mock
    private RedisHelper redisHelper;

    @InjectMocks
    private HomepageWidgetDaoImpl homepageWidgetDao;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testAddWidget() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        homepageWidgetDao.addWidget("mid", "identifierKey", 1L, "status", "featureType", "metaData", new Date(), "identifierValue", 1, "type");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testAddWidgetException() {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenThrow(RuntimeException.class);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        Date date = new Date();
        assertThrows(RuntimeException.class, () -> {
            homepageWidgetDao.addWidget("mid", "identifierKey", 1L, "status", "featureType", "metaData", date, "identifierValue", 1, "type");
        });
    }

    @Test
    void testAddWidgetNudgesException() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doThrow(RuntimeException.class).when(redisHelper).evictNudges(anyString(), anyString());

        homepageWidgetDao.addWidget("mid", "identifierKey", 1L, "status", "featureType", "metaData", new Date(), "identifierValue", 1, "type");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testFetchWidgetDetails() throws Exception {
        List<WidgetInfoDTO> widgetInfoDTOs = Collections.singletonList(new WidgetInfoDTO());
        when(jdbcTemplateSlave.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(widgetInfoDTOs);

        List<WidgetInfoDTO> result = homepageWidgetDao.fetchWidgetDetails("mid", "identifierValue", 1L,
                "featureType", "status", "type");

        assertEquals(widgetInfoDTOs, result);
        verify(jdbcTemplateSlave, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    void testFetchWidgetDetailsException() {
        when(jdbcTemplateSlave.query(anyString(), any(Object[].class), any(RowMapper.class))).thenThrow(RuntimeException.class);

        assertThrows(RuntimeException.class, () -> {
            homepageWidgetDao.fetchWidgetDetails("mid", "identifierValue", 1L, "featureType", "status", "type");
        });
    }

    @Test
    void testFetchWidgetDetailsWithoutCustId() throws Exception {
        List<WidgetInfoDTO> widgetInfoDTOs = Collections.singletonList(new WidgetInfoDTO());
        when(jdbcTemplateSlave.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(widgetInfoDTOs);

        List<WidgetInfoDTO> result = homepageWidgetDao.fetchWidgetDetails("mid", "identifierValue", null,
                "featureType", "status", "type");

        assertEquals(widgetInfoDTOs, result);
        verify(jdbcTemplateSlave, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    void testFetchWidgetDetailsForPaymentHold() throws Exception {
        List<WidgetInfoDTO> widgetInfoDTOs = Collections.singletonList(new WidgetInfoDTO());
        when(jdbcTemplateSlave.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(widgetInfoDTOs);

        List<WidgetInfoDTO> result = homepageWidgetDao.fetchWidgetDetailsForPaymentHold("mid", "identifierValue", "featureType");

        assertEquals(widgetInfoDTOs, result);
        verify(jdbcTemplateSlave, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
    }

    @Test
    void testFetchWidgetDetailsForPaymentHoldException() {
        when(jdbcTemplateSlave.query(anyString(), any(Object[].class), any(RowMapper.class))).thenThrow(RuntimeException.class);

        assertThrows(RuntimeException.class, () -> {
            homepageWidgetDao.fetchWidgetDetailsForPaymentHold("mid", "identifierValue", "featureType");
        });
    }

    @Test
    void testUpdatePaymentHoldCard() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        homepageWidgetDao.updatePaymentHoldCard("mid", "identifierValue",
                "featureType", new Date(), "type", "metadata");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testUpdatePaymentHoldCardWithoutCardExpiryTime() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        homepageWidgetDao.updatePaymentHoldCard("mid", "identifierValue",
                "featureType", null, "type", "metadata");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testUpdatePaymentHoldCardException() {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenThrow(RuntimeException.class);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        assertThrows(RuntimeException.class, () -> {
            homepageWidgetDao.updatePaymentHoldCard("mid", "identifierValue",
                    "featureType", null, "type", "metadata");
        });
    }

    @Test
    void testUpdateStatusAndFlag() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        homepageWidgetDao.updateStatusAndFlag("mid", "identifierValue", 1L, "featureType",
                "status", new Date(), "statusToCheck", "type", "metadata");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testUpdateStatusAndFlagWithoutCustId() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        homepageWidgetDao.updateStatusAndFlag("mid", "identifierValue", null, "featureType",
                "status", new Date(), "statusToCheck", "type", "metadata");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testUpdateStatusAndFlagException() {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenThrow(RuntimeException.class);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        assertThrows(RuntimeException.class, () -> {
            homepageWidgetDao.updateStatusAndFlag("mid", "identifierValue", null, "featureType",
                    "status", null, "statusToCheck", "type", "metadata");
        });
    }

    @Test
    void testSetCardInactive() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);

        homepageWidgetDao.setCardInactive("mid", "identifierValue", 1L, "status",
                new Date(), "statusToCheck");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
    }

    @Test
    void testSetCardInactiveWithoutCustId() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);

        homepageWidgetDao.setCardInactive("mid", "identifierValue", null, "status",
                new Date(), "statusToCheck");

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
    }

    @Test
    void testUpdateMultipleCardType() throws Exception {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        homepageWidgetDao.updateMultipleCardType("mid", "identifierValue", "status", new Date(),
                "statusToCheck", "type", new ArrayList<>(Collections.singletonList("featureType")));

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

}
