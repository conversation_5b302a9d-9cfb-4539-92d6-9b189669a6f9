package com.paytm.aggregatorgateway.dao.impl;

import com.paytm.aggregatorgateway.dto.P4bNudges;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class P4bNudgesDaoImplTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private JdbcTemplate jdbcTemplateSlave;

    @Mock
    private RedisHelper redisHelper;

    private Authentication authentication;

    @InjectMocks
    private P4bNudgesDaoImpl p4bNudgesDao;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void testGetP4BNudge() {
        List<P4bNudges> p4bNudges = Collections.singletonList(new P4bNudges());
        when(jdbcTemplateSlave.query(anyString(), any(Object[].class), any(RowMapper.class))).thenReturn(p4bNudges);

        P4bNudges result = p4bNudgesDao.getP4BNudge("mid", "type", "featureType");

        verify(jdbcTemplateSlave, times(1)).query(anyString(), any(Object[].class), any(RowMapper.class));
        assertEquals(p4bNudges.get(0), result);
    }

    @Test
    void testUpdateP4BNudgeExpiry() {
        UtsHelper.mockUserAuthentication(authentication);
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        p4bNudgesDao.updateP4BNudgeExpiry(1L, LocalDateTime.now());

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testUpdateP4BNudgeExpiryException() {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenThrow(RuntimeException.class);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        LocalDateTime expiryTime = LocalDateTime.now();
        assertThrows(RuntimeException.class, () -> {
            p4bNudgesDao.updateP4BNudgeExpiry(1L, expiryTime);
        });
    }

    @Test
    void testUpdateP4BNudgeExpiryRemoveNudgeCacheException() {
        UtsHelper.mockUserAuthentication(authentication);
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doThrow(RuntimeException.class).when(redisHelper).evictNudges(anyString(), anyString());

        p4bNudgesDao.updateP4BNudgeExpiry(1L, LocalDateTime.now());

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testAddP4BNudge() {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenReturn(1);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        p4bNudgesDao.addP4BNudge("mid", "type", "featureType", 1L, 1, "status", "identifierKey", "identifierValue", "metaData", LocalDateTime.now());

        verify(jdbcTemplate, times(1)).update(anyString(), Optional.ofNullable(any()));
        verify(redisHelper, times(1)).evictNudges(anyString(), anyString());
    }

    @Test
    void testAddP4BNudgeException() {
        when(jdbcTemplate.update(anyString(), Optional.ofNullable(any()))).thenThrow(RuntimeException.class);
        doNothing().when(redisHelper).evictNudges(anyString(), anyString());

        LocalDateTime expiryTime = LocalDateTime.now();
        assertThrows(RuntimeException.class, () -> {
            p4bNudgesDao.addP4BNudge("mid", "type", "featureType", 1L, 1, "status", "identifierKey", "identifierValue", "metaData", expiryTime);
        });
    }
}
