package com.paytm.aggregatorgateway.config.security;

import com.paytm.aggregatorgateway.controller.healthcheck.HealthCheckController;
import com.paytm.aggregatorgateway.exceptions.UnauthorizedException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.security.IUserFacade;
import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.pgdashboard.commons.dto.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.access.AccessDeniedException;

import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class InternalAPIFilterTest {

    @Mock
    private IUserFacade userFacade;

    @Mock
    private Environment environment;

    @InjectMocks
    private InternalAPIFilter internalAPIFilter;

    private MockHttpServletRequest request;

    private MockHttpServletResponse response;

    private FilterChain filterChain;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        filterChain = Mockito.mock(FilterChain.class);
    }

    @Test
    void testDoFilterInternal_WhenJwtTokenIsNotBlankSuccess() throws Exception {
        request.addHeader("x-jwt-token", "jwtToken");
        request.addHeader("client-id", "clientId");
        HealthCheckController.warmUpCompleted = true;
        doNothing().when(userFacade).validateJWT(anyString(), anyString());

        internalAPIFilter.doFilterInternal(request, response, filterChain);

        assertEquals(200, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenJwtTokenIsNotBlankFailure() throws Exception {
        request.addHeader("x-jwt-token", "jwtToken");
        request.addHeader("client-id", "clientId");
        HealthCheckController.warmUpCompleted = true;
        doThrow(new Exception()).when(userFacade).validateJWT(anyString(), anyString());

        assertThrows(ValidationException.class, () -> {
            internalAPIFilter.doFilterInternal(request, response, filterChain);
        });
    }

    @Test
    void testDoFilterInternal_WhenCookieIsNotBlankSuccess() throws Exception {
        request.addHeader("Cookie", "cookie");
        request.addParameter("version", "9.2.0");
        request.addParameter("source", "P4B_IOS");
        HealthCheckController.warmUpCompleted = true;
        doNothing().when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));
        when(userFacade.getUserByXAuthUMPOrCookie(any(HttpServletRequest.class))).thenReturn(new User());

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(200, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenWhoIsOrCookieIsNotBlankUnauthorizedException() throws Exception {
        request.addHeader("x-auth-ump", "whoIs");
        request.addHeader("Cookie", "cookie");
        HealthCheckController.warmUpCompleted = true;
        doThrow(new UnauthorizedException()).when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(HttpServletResponse.SC_UNAUTHORIZED, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenWhoIsOrCookieIsNotBlankValidationException() throws Exception {
        request.addHeader("x-auth-ump", "whoIs");
        request.addHeader("Cookie", "cookie");
        HealthCheckController.warmUpCompleted = true;
        doThrow(new ValidationException()).when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(HttpServletResponse.SC_BAD_REQUEST, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenWhoIsIsNotBlankValidationExceptionWithForceupdate() throws Exception {
        request.addHeader("x-auth-ump", "whoIs");
        request.addHeader("x-user-token", "xusertoken");
        request.addHeader("x-user-mid", "xusermid");
        request.setRequestURI("/bffv2/api/v1/app/forceupdate");
        request.addParameter("version", "9.3.0");
        request.addParameter("source", "P4B_IOS");
        HealthCheckController.warmUpCompleted = true;
        doThrow(new ValidationException()).when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(440, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenWhoIsOrCookieIsNotBlankAccessDeniedException() throws Exception {
        request.addHeader("x-auth-ump", "whoIs");
        request.addHeader("Cookie", "cookie");
        HealthCheckController.warmUpCompleted = true;
        doThrow(new AccessDeniedException("")).when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(HttpServletResponse.SC_FORBIDDEN, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenWhoIsOrCookieIsNotBlankGenericException() throws Exception {
        request.addHeader("x-auth-ump", "whoIs");
        request.addHeader("Cookie", "cookie");
        HealthCheckController.warmUpCompleted = true;
        doThrow(new RuntimeException()).when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenWhoIsIsNotBlankSuccess() throws Exception {
        request.addHeader("x-auth-ump", "whoIs");
        request.addHeader("x-user-token", "xusertoken");
        request.addHeader("x-user-mid", "xusermid");
        request.addParameter("version", "9.2.0");
        request.addParameter("source", "P4B_IOS");
        HealthCheckController.warmUpCompleted = true;
        doNothing().when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));
        when(userFacade.getUser(any(HttpServletRequest.class), anyString())).thenReturn(mockUser());

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(200, response.getStatus());
    }

    @Test
    void testDoFilterInternal_WhenWhoIsIsNotBlankValidationException() throws Exception {
        request.addHeader("x-auth-ump", "whoIs");
        request.addHeader("x-user-token", "xusertoken");
        request.addHeader("x-user-mid", "mid123");
        request.addParameter("version", "9.2.0");
        request.addParameter("source", "P4B_IOS");
        HealthCheckController.warmUpCompleted = true;
        doNothing().when(userFacade).checkXAuthUMPValidity(anyString(), any(HttpServletRequest.class));
        when(userFacade.getUser(any(HttpServletRequest.class), anyString())).thenReturn(mockUser());

        internalAPIFilter.doFilterInternal(request, response, filterChain);
        assertEquals(400, response.getStatus());
    }

    private User mockUser(){
        Merchant merchant = new Merchant();
        merchant.setId(123L);
        merchant.setKybid("kybid123");
        merchant.setMid("xusermid");
        merchant.setStoreCashCloneMid("mid12345");
        merchant.setName("TK");
        merchant.setAdminUserId("11074141");
        List<Merchant> merchants = new ArrayList<>();
        merchants.add(merchant);
        merchant.setAggregator(true);
        User user = new User();
        user.setId("11074141");
        user.setCurrentMerchant(123L);
        user.setMerchants(merchants);
        user.setPaytmSSOToken("jf89gsjk-89sdf-89usdf-8sdf");
        return user;
    }

}
