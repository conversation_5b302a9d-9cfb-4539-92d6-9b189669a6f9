package com.paytm.aggregatorgateway.metrics;

import com.paytm.aggregatorgateway.utils.metrics.MetricUtils;
import com.timgroup.statsd.StatsDClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class MetricUtilsTest {

    @InjectMocks
    private MetricUtils metricUtils;

    @Mock
    private Environment environment;

    @Mock
    private StatsDClient statsDClient;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testPushCounterMetrics() {
        String metricName = "testMetric";
        String[] counterMetricTags = new String[]{"tag1", "tag2"};
        when(environment.getProperty("HOSTNAME")).thenReturn("localhost");
        metricUtils.pushCounterMetrics(metricName, counterMetricTags);
        verify(statsDClient, times(1)).incrementCounter(anyString(), any(String[].class));
    }

    @Test
    void testPushHistogramValues() {
        String metricName = "testMetric";
        double elapsedTime = 100.0;
        String[] histogramTags = new String[]{"tag1", "tag2"};
        when(environment.getProperty("HOSTNAME")).thenReturn("localhost");
        //metricUtils.pushHistogramValues(metricName, elapsedTime, histogramTags);
        verify(statsDClient, times(1)).recordHistogramValue(anyString(), anyDouble(), any(String[].class));
    }

    @Test
    void testPushGaugeValue() {
        String metricName = "testMetric";
        Integer value = 10;
        String[] gaugeMetricTags = new String[]{"tag1", "tag2"};
        when(environment.getProperty("HOSTNAME")).thenReturn("localhost");
        //metricUtils.pushGaugeValue(metricName, value, gaugeMetricTags);
        verify(statsDClient, times(1)).recordGaugeValue(anyString(), anyInt(), any(String[].class));
    }

    @Test
    void testIncrementExtApiHitsCounter() {
        String domain = "testDomain";
        String url = "testUrl";
        HttpMethod httpMethod = HttpMethod.GET;
        String upstreamAPI = "testUpstreamAPI";
        when(environment.getProperty("HOSTNAME")).thenReturn("localhost");
        metricUtils.incrementExtApiHitsCounter(domain, url, httpMethod, upstreamAPI);
        verify(statsDClient, times(1)).incrementCounter(anyString(), any(String[].class));
    }

}