package com.paytm.aggregatorgateway.helper;

import com.paytm.dashboard.security.UserDetailConfig;
import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.pgdashboard.commons.dto.User;
import org.springframework.security.core.Authentication;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

public class UtsHelper {

    public static void mockUserAuthentication(Authentication authentication){
        Merchant merchant = new Merchant();
        merchant.setId(123L);
        merchant.setKybid("kybid123");
        merchant.setMid("mid1234");
        merchant.setStoreCashCloneMid("mid12345");
        merchant.setName("TK");
        merchant.setAdminUserId("11074141");
        List<Merchant> merchants = new ArrayList<>();
        merchants.add(merchant);
        merchant.setAggregator(true);
        User user = new User();
        user.setId("11074141");
        user.setCurrentMerchant(123L);
        user.setMerchants(merchants);
        user.setPaytmSSOToken("jf89gsjk-89sdf-89usdf-8sdf");
        UserDetailConfig userConfig = new UserDetailConfig(user, false);
        when(authentication.getDetails()).thenReturn(userConfig);
    }

    public static String getMockContext() {
        return "{\n" +
                "\"fname\": \"\",\n" +
                "\"uname\": \"919990100097\",\n" +
                "\"currentLocale\": \"en-GB\",\n" +
                "\"mobile\": \"9900009597\",\n" +
                "\"groups\": [],\n" +
                "\"isMerchant\": 1,\n" +
                "\"userToken\": \"c4b2ce54-17ed-4474-9406-8626600\",\n" +
                "\"lname\": \"\",\n" +
                "\"currentMerchant\": 81618693,\n" +
                "\"id\": \"**********\",\n" +
                "\"merchants\": [\n" +
                "{\n" +
                "\"id\": 81618693,\n" +
                "\"name\": \"abcs qwer\",\n" +
                "\"guid\": \"6fa4dd48-5a02-42ab-9bc2-3a411fe\",\n" +
                "\"roles\": [\n" +
                "{\n" +
                "\"role\": \"ONLINE_CHARGEBACK_REPORT\",\n" +
                "\"permissions\": [\n" +
                "\"CHRGBACKREPORT_SETTINGS\",\n" +
                "\"ADVANCEANALYTICS_VIEW\"\n" +
                "]\n" +
                "},\n" +
                "{\n" +
                "\"role\": \"LOYALTYPOINTS\",\n" +
                "\"permissions\": [\n" +
                "\"LOYALTYPOINTS_REDEEM\",\n" +
                "\"LOYALTYPOINTS_VIEW\"\n" +
                "]\n" +
                "},\n" +
                "{\n" +
                "\"role\": \"ROLE_DOWNTIME\",\n" +
                "\"permissions\": [\n" +
                "\"DOWNTIME_VIEW\"\n" +
                "]\n" +
                "}\n" +
                "],\n" +
                "\"aggregator\": false,\n" +
                "\"mid\": \"jBkskF82072000115725\",\n" +
                "\"type\": \"OTHER\",\n" +
                "\"isMerchant\": 1,\n" +
                "\"mobile\": \"**********\",\n" +
                "\"migrated\": true,\n" +
                "\"pgonly\": true,\n" +
                "\"merchantType\": \"50K\",\n" +
                "\"accountPrimary\": \"1\",\n" +
                "\"walletOnly\": false,\n" +
                "\"isActive\": true,\n" +
                "\"pgpOnly\": false,\n" +
                "\"betaAccess\": true,\n" +
                "\"betaViewOnly\": true,\n" +
                "\"createdOn\": \"2023-10-20 17:22:10.0\",\n" +
                "\"isSdMerchant\": true,\n" +
                "\"adminUserId\": \"**********\",\n" +
                "\"isChild\": false,\n" +
                "\"kybid\": \"B06teq3rvgp397\",\n" +
                "\"forceEnabled\": true,\n" +
                "\"customSettlemntEnabled\": false,\n" +
                "\"isReseller\": false,\n" +
                "\"solutionType\": \"OFFLINE\",\n" +
                "\"obChannel\": \"DIY_P4B_APP\",\n" +
                "\"categoryLabel\": \"Dashboard for transactions on QR\",\n" +
                "\"bankEditAllowed\": true,\n" +
                "\"isPosProvider\": false,\n" +
                "\"isPaymentButtonsDisabled\": false,\n" +
                "\"isDelayedSettlement\": false,\n" +
                "\"settlementType\": \"Business Wallet\",\n" +
                "\"inactiveState\": \"\",\n" +
                "\"inactiveDate\": \"\",\n" +
                "\"entityType\": \"INDIVIDUAL\",\n" +
                "\"nLevelHierarchyEnabled\": false,\n" +
                "\"dummyAggregator\": false,\n" +
                "\"isBwReconEnabled\": false,\n" +
                "\"eRupiEnabled\": false,\n" +
                "\"pg2Enabled\": true,\n" +
                "\"hasShopifyToken\": false,\n" +
                "\"pg2PartiallyEnabled\": false,\n" +
                "\"migrationToTws\": \"2023-10-20 17:22:58.8\",\n" +
                "\"pg2MigratedDate\": \"2023-10-20 17:22:11.0\",\n" +
                "\"pricingUpdateLongtail\": false,\n" +
                "\"pricingUpdateExisting\": false,\n" +
                "\"isLimitedBW\": true,\n" +
                "\"vasPlatformFeeSsm\": false,\n" +
                "\"reconView\": true,\n" +
                "\"pcfEnabled\": false,\n" +
                "\"aggregatorMid\": \"\",\n" +
                "\"dealsEnabledMerchant\": false,\n" +
                "\"bwReconDate\": \"2022-10-20 00:00:00.0\",\n" +
                "\"customPayout\": false,\n" +
                "\"onboardedAsTws\": false,\n" +
                "\"industryType\": \"SMALL\",\n" +
                "\"dealsClonedMid\": \"\",\n" +
                "\"callMeBackEnabled\": false,\n" +
                "\"refundForceDisable\": false,\n" +
                "\"isSettleFreeze\": false,\n" +
                "\"isTransferHold\": false,\n" +
                "\"isPreAuthEnabled\": false,\n" +
                "\"refundWhiteListOnus\": false,\n" +
                "\"isEmiContestEnabled\": false,\n" +
                "\"refundRoundRobinEnabled\": false,\n" +
                "\"settlementFactorsEnabled\": false,\n" +
                "\"paytmEmiEnabled\": false,\n" +
                "\"aggregatorPayout\": false,\n" +
                "\"createdOnConverted\": {\n" +
                "\"present\": true\n" +
                "},\n" +
                "\"recentDealsEnabled\": false\n" +
                "}\n" +
                "],\n" +
                "\"email\": null,\n" +
                "\"isDemoUser\": false\n" +
                "}";
    }
}
