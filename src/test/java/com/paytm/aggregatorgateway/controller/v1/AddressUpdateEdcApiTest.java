package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.AddressUpdateEdcService;
import com.paytm.aggregatorgateway.service.MaquetteService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.*;

import static com.paytm.aggregatorgateway.constants.PromoConstants.HTTP_200;
import static com.paytm.aggregatorgateway.constants.UPSIntegrationConstants.SUCCESS;
import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class AddressUpdateEdcApiTest {

	@InjectMocks
	private AddressUpdateEdcAPI addressUpdateEdcAPI;

	@Mock
	private AddressUpdateEdcService addressUpdateEdcService;

	@Mock
	private MaquetteService maquetteService;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception{
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
	}

	@Test
	void updateEdcAddressTest1() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		ResponseUmp responseUmp = new ResponseUmp(SUCCESS, HTTP_200,"Ticket creation, beat creation, and disabling tip was successful", new ArrayList<>());

		when(addressUpdateEdcService.updateEdcAddress(eq(requestBody), anyString())).thenReturn(responseUmp);
		ResponseUmp response = addressUpdateEdcAPI.updateEdcAddress(requestBody);

		assertEquals(SUCCESS, response.getStatus());
		assertEquals(HTTP_200, response.getStatusCode());
		assertEquals("Ticket creation, beat creation, and disabling tip was successful", response.getStatusMessage());
	}

	@Test
	void updateEdcAddressTest2() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		ResponseUmp responseUmp = new ResponseUmp(SUCCESS, HTTP_200,"Ticket creation, beat creation, and disabling tip was successful", new ArrayList<>());

		when(addressUpdateEdcService.updateEdcAddress(eq(requestBody), anyString())).thenReturn(responseUmp);

		try {
			addressUpdateEdcAPI.updateEdcAddress(requestBody);
		} catch (ValidationException validationException) {
			assertNotNull(validationException.getErrors());
			assertEquals(UMPErrorCodeEnums.MISSING_PARAM.getErrorMsg() + " deviceId missing", validationException.getErrors().get(UMPErrorCodeEnums.MISSING_PARAM.getErrorCode()));
		}
	}

	@Test
	void updateEdcAddressTest3() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		try {
			addressUpdateEdcAPI.updateEdcAddress(requestBody);
		} catch (ValidationException validationException) {
			assertNotNull(validationException.getErrors());
			assertEquals(UMPErrorCodeEnums.MISSING_PARAM.getErrorMsg() + " Incomplete address", validationException.getErrors().get(UMPErrorCodeEnums.MISSING_PARAM.getErrorCode()));
		}
	}

	@Test
	void updateEdcAddressTest4() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		assertThrows(Exception.class, () -> {
			addressUpdateEdcAPI.updateEdcAddress(requestBody);
		});
	}

	@Test
	void verifyLatLongTest1() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		ResponseUmp responseUmp = new ResponseUmp(SUCCESS, HTTP_200,"Successfully fetched verification status", new ArrayList<>());

		when(maquetteService.verifyLatLong(requestBody)).thenReturn(responseUmp);
		ResponseUmp response = addressUpdateEdcAPI.verifyLatLong(requestBody);

		assertEquals(SUCCESS, response.getStatus());
		assertEquals(HTTP_200, response.getStatusCode());
		assertEquals("Successfully fetched verification status", response.getStatusMessage());
	}

	@Test
	void getEdcAddressTest1() throws Exception {
		mockUserAuthentication(authentication);

		ResponseUmp responseUmp = new ResponseUmp(SUCCESS, HTTP_200,"Successfully fetched verification status", new ArrayList<>());

		when(addressUpdateEdcService.getEdcAddress(anyString(), anyBoolean(), anyBoolean())).thenReturn(responseUmp);
		ResponseUmp response = addressUpdateEdcAPI.getEdcAddress("12345", Boolean.TRUE, Boolean.TRUE);

		assertEquals(SUCCESS, response.getStatus());
		assertEquals(HTTP_200, response.getStatusCode());
		assertEquals("Successfully fetched verification status", response.getStatusMessage());
	}

	@Test
	void getEdcAddressTest2() throws Exception {
		mockUserAuthentication(authentication);

		ResponseUmp responseUmp = new ResponseUmp(SUCCESS, HTTP_200,"Successfully fetched verification status", new ArrayList<>());

		when(addressUpdateEdcService.getEdcAddress(anyString(), anyBoolean(), anyBoolean())).thenReturn(responseUmp);
		ResponseUmp response = addressUpdateEdcAPI.getEdcAddress("12345", Boolean.FALSE, Boolean.TRUE);

		assertEquals(SUCCESS, response.getStatus());
		assertEquals(HTTP_200, response.getStatusCode());
		assertEquals("Successfully fetched verification status", response.getStatusMessage());
	}

	@Test
	void getEdcAddressTest3() throws Exception {
		mockUserAuthentication(authentication);

		ResponseUmp responseUmp = new ResponseUmp(SUCCESS, HTTP_200,"Successfully fetched verification status", new ArrayList<>());

		when(addressUpdateEdcService.getEdcAddress(anyString(), anyBoolean(), anyBoolean())).thenReturn(responseUmp);
		ResponseUmp response = addressUpdateEdcAPI.getEdcAddress("12345", Boolean.FALSE, Boolean.FALSE);

		assertEquals(SUCCESS, response.getStatus());
		assertEquals(HTTP_200, response.getStatusCode());
		assertEquals("Successfully fetched verification status", response.getStatusMessage());
	}

}
