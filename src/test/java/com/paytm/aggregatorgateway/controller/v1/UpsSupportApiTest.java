package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateNFCStatus;
import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateStatus;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.List;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class UpsSupportApiTest {

    @Mock
    private UPSService upsService;

    @InjectMocks
    private UPSSupportApi upsSupportApi;
    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void updateNFCStatusTestFailure() throws Exception{
        mockUserAuthentication(authentication);
        ReqParamsToUpdateNFCStatus reqParamsToUpdateNFCStatus = new ReqParamsToUpdateNFCStatus();
        when(upsService.updateNFCStatus(reqParamsToUpdateNFCStatus))
                .thenReturn(new ResponseUmp("FAILURE","UMP-601",null,null));
        ResponseUmp response = upsSupportApi.updateNFCStatus(reqParamsToUpdateNFCStatus);
        assertEquals("FAILURE", response.getStatus());
        assertEquals("UMP-601", response.getStatusCode());
    }

    @Test
    void updateNFCStatusTestSuccess() throws Exception{
        mockUserAuthentication(authentication);
        ReqParamsToUpdateNFCStatus reqParamsToUpdateNFCStatus = new ReqParamsToUpdateNFCStatus();
        reqParamsToUpdateNFCStatus.setEntityId("mockEntityId");
        reqParamsToUpdateNFCStatus.setEntityType("mockEntityName");
        reqParamsToUpdateNFCStatus.setPreferenceKey("mockPreferenceKey");
        List<ReqParamsToUpdateNFCStatus.PreferenceValue> preferenceValues = new ArrayList<>();
        ReqParamsToUpdateNFCStatus.PreferenceValue preferenceValue = new ReqParamsToUpdateNFCStatus.PreferenceValue();
        preferenceValue.setCustId("mockCustId");
        preferenceValue.setDeviceId("mockDeviceId");
        preferenceValue.setIsSubuser("false");
        preferenceValue.setNfcEnabled("false");
        preferenceValues.add(preferenceValue);
        reqParamsToUpdateNFCStatus.setPreferenceValue(preferenceValues);
        when(upsService.updateNFCStatus(reqParamsToUpdateNFCStatus))
                .thenReturn(new ResponseUmp("SUCCESS","200",null,null));
        ResponseUmp response = upsSupportApi.updateNFCStatus(reqParamsToUpdateNFCStatus);
        assertEquals("SUCCESS", response.getStatus());

    }

    @Test
    void updateStatusTestFailure() throws Exception{
        mockUserAuthentication(authentication);
        ReqParamsToUpdateStatus reqParamsToUpdateStatus = new ReqParamsToUpdateStatus();
        when(upsService.updateStatus(reqParamsToUpdateStatus))
                .thenReturn(new ResponseUmp("FAILURE","UMP-601",null,null));
        ResponseUmp response = upsSupportApi.updateStatus(reqParamsToUpdateStatus);
        assertEquals("FAILURE", response.getStatus());
        assertEquals("UMP-601", response.getStatusCode());
    }

    @Test
    void updateStatusSuccess() throws Exception{
        mockUserAuthentication(authentication);
        ReqParamsToUpdateStatus reqParamsToUpdateStatus = new ReqParamsToUpdateStatus();
        reqParamsToUpdateStatus.setEntityId("mockEntityId");
        reqParamsToUpdateStatus.setEntityType("mockEntityName");
        reqParamsToUpdateStatus.setPreferenceKey("mockPreferenceKey");
        reqParamsToUpdateStatus.setPreferenceValue(getPreferenceValueList());
        when(upsService.updateStatus(reqParamsToUpdateStatus))
                .thenReturn(new ResponseUmp("SUCCESS","200",null,null));
        ResponseUmp response = upsSupportApi.updateStatus(reqParamsToUpdateStatus);
        assertEquals("SUCCESS", response.getStatus());
    }

    private static List<ReqParamsToUpdateStatus.PreferenceValue> getPreferenceValueList() {
        List<ReqParamsToUpdateStatus.PreferenceValue> preferenceValues = new ArrayList<>();
        ReqParamsToUpdateStatus.PreferenceValue preferenceValue = new ReqParamsToUpdateStatus.PreferenceValue();
        preferenceValue.setCustId("mockCustId");
        preferenceValue.setDeviceId("mockDeviceId");
        preferenceValue.setIsSubuser("false");
        preferenceValue.setNfcEnabled("false");
        preferenceValue.setIsSecurityShieldEnabled("false");
        preferenceValues.add(preferenceValue);
        return preferenceValues;
    }

}
