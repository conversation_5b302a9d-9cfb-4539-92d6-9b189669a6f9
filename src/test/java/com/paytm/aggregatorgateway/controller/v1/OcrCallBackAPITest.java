package com.paytm.aggregatorgateway.controller.v1;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.service.OcrService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class OcrCallBackAPITest {

    @Mock
    private OcrService ocrService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private OcrCallBackAPI ocrCallBackAPI;

    private MockMvc mockMvc;
    private ObjectMapper testObjectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(ocrCallBackAPI).build();
        testObjectMapper = new ObjectMapper();
    }

    @Test
    void testQualificationCallBack_Success() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        request.put("status", "SUCCESS");
        request.put("statusCode", "200");
        request.put("statusMessage", "OK");
        request.put("requestId", "test-request-id");

        ResponseUmp expectedResponse = new ResponseUmp("SUCCESS", "200", "Qualification callback processed successfully", null);
        when(ocrService.qualificationCallBack(any())).thenReturn(expectedResponse);

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/qualification/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.statusCode").value("200"))
                .andExpect(jsonPath("$.statusMessage").value("Qualification callback processed successfully"));
    }

    @Test
    void testQualificationCallBack_Failure() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        request.put("status", "FAILED");
        request.put("statusCode", "400");
        request.put("statusMessage", "FAILED: Internal Server Error");
        request.put("requestId", "test-request-id");

        ResponseUmp expectedResponse = new ResponseUmp("SUCCESS", "200", "Redis updated with failure status", null);
        when(ocrService.qualificationCallBack(any())).thenReturn(expectedResponse);

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/qualification/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.statusCode").value("200"))
                .andExpect(jsonPath("$.statusMessage").value("Redis updated with failure status"));
    }

    @Test
    void testQualificationCallBack_Exception() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        request.put("status", "SUCCESS");
        request.put("statusCode", "200");
        request.put("statusMessage", "OK");
        request.put("requestId", "test-request-id");

        when(ocrService.qualificationCallBack(any())).thenThrow(new RuntimeException("Test exception"));

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/qualification/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("FAILED"))
                .andExpect(jsonPath("$.statusCode").value("500"))
                .andExpect(jsonPath("$.statusMessage").value("Internal server error"));
    }

    @Test
    void testDeductionsCallBack_Success() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        request.put("status", "SUCCESS");
        request.put("statusCode", "200");
        request.put("statusMessage", "OK");
        request.put("requestId", "test-request-id");

        Map<String, Object> response = new HashMap<>();
        response.put("processedImageFileIds", new String[]{"DM143819942560"});

        Map<String, Object> imageWiseParameters = new HashMap<>();
        Map<String, Object> imageParams = new HashMap<>();
        imageParams.put("account_number", Map.of("value", "**************", "confidence", 0.9999));
        imageParams.put("ifsc_code", Map.of("value", "HDFC0000732", "confidence", 0.9999));
        imageParams.put("account_holder_name", Map.of("value", "RAJEEV KUMER GAUTAM", "confidence", 0.9999));
        imageWiseParameters.put("******************", imageParams);
        response.put("imageWiseParameters", imageWiseParameters);

        request.put("response", response);

        ResponseUmp expectedResponse = new ResponseUmp("SUCCESS", "200", "Current state retrieved", response);
        when(ocrService.deductionsCallBack(any())).thenReturn(expectedResponse);

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/deductions/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.statusCode").value("200"))
                .andExpect(jsonPath("$.statusMessage").value("Current state retrieved"));
    }

    @Test
    void testDeductionsCallBack_Failure() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        request.put("status", "FAILED");
        request.put("statusCode", "402");
        request.put("statusMessage", "FAILED: Invalid request data");
        request.put("requestId", "test-request-id");

        ResponseUmp expectedResponse = new ResponseUmp("SUCCESS", "200", "Current state retrieved", null);
        when(ocrService.deductionsCallBack(any())).thenReturn(expectedResponse);

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/deductions/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.statusCode").value("200"))
                .andExpect(jsonPath("$.statusMessage").value("Current state retrieved"));
    }

    @Test
    void testDeductionsCallBack_Exception() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        request.put("status", "SUCCESS");
        request.put("statusCode", "200");
        request.put("statusMessage", "OK");
        request.put("requestId", "test-request-id");

        when(ocrService.deductionsCallBack(any())).thenThrow(new RuntimeException("Test exception"));

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/deductions/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("FAILED"))
                .andExpect(jsonPath("$.statusCode").value("500"))
                .andExpect(jsonPath("$.statusMessage").value("Internal server error"));
    }

    @Test
    void testQualificationCallBack_InvalidJson() throws Exception {
        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/qualification/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testDeductionsCallBack_InvalidJson() throws Exception {
        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/deductions/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testQualificationCallBack_EmptyRequest() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        ResponseUmp expectedResponse = new ResponseUmp("SUCCESS", "200", "Qualification callback processed successfully", null);
        when(ocrService.qualificationCallBack(any())).thenReturn(expectedResponse);

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/qualification/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.statusCode").value("200"));
    }

    @Test
    void testDeductionsCallBack_EmptyRequest() throws Exception {
        // Setup
        Map<String, Object> request = new HashMap<>();
        ResponseUmp expectedResponse = new ResponseUmp("SUCCESS", "200", "Current state retrieved", null);
        when(ocrService.deductionsCallBack(any())).thenReturn(expectedResponse);

        // Execute & Verify
        mockMvc.perform(post("/api/v1/ocr/bankProof/deductions/callBack")
                .contentType(MediaType.APPLICATION_JSON)
                .content(testObjectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("SUCCESS"))
                .andExpect(jsonPath("$.statusCode").value("200"));
    }
}
