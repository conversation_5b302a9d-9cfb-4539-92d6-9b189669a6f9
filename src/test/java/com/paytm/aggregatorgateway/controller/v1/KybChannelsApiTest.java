package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.KybChannelsService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashMap;
import java.util.Map;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class KybChannelsApiTest {

    @Mock
    private KybChannelsService kybChannelsService;

    @InjectMocks
    private KybChannelsAPI kybChannelsApi;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception{
        MockitoAnnotations.openMocks(this);
        authentication = Mockito.mock(UserAuthentication.class);
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void fetchChannelFailure() throws Exception{
        // empty kybId
        mockUserAuthentication(authentication);
        SecurityUtils.getCurrentMerchant().setKybid("");
        when(kybChannelsService.fetchChannels(""))
                .thenReturn(new ResponseUmp("FAILURE",null,"KybId not associated with this merchant",null)
		);
        ResponseUmp response = kybChannelsApi.fetchChannel();
        assertEquals("FAILURE", response.getStatus());
        assertNull(response.getStatusCode());
        assertEquals("KybId not associated with this merchant", response.getStatusMessage());
        assertNull(response.getResults());
    }

    @Test
    void fetchChannelFailure2() throws Exception{
        // downstream failure
        mockUserAuthentication(authentication);
        when(kybChannelsService.fetchChannels("kybid123"))
                .thenReturn(new ResponseUmp("FAILURE","400","Error Occcured In Channels Api",null));
        ResponseUmp response = kybChannelsApi.fetchChannel();
        assertEquals("FAILURE", response.getStatus());
        assertEquals("400", response.getStatusCode());
        assertEquals("Error Occcured In Channels Api", response.getStatusMessage());
        assertNull(response.getResults());
    }

    @Test
    void fetchChannelSuccess() throws Exception{
        // success from downstream
        mockUserAuthentication(authentication);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("chatEligible", true);
        when(kybChannelsService.fetchChannels("kybid123"))
                .thenReturn(new ResponseUmp("SUCCESS","200",null,resultMap));
        ResponseUmp response = kybChannelsApi.fetchChannel();
        assertEquals("SUCCESS", response.getStatus());
        assertEquals("200", response.getStatusCode());
        assertNull(response.getStatusMessage());
        assertNotNull(response.getResults());
    }

}
