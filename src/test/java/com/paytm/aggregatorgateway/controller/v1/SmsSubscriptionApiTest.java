package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.dto.UpdateSubscriptionStatusDTO;
import com.paytm.aggregatorgateway.service.SmsSubscriptionService;
import com.paytm.aggregatorgateway.service.SubscriptionService;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.HashMap;

import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.FAILURE;
import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class SmsSubscriptionApiTest {

	private static final String ID1 = "625026502618107360";

	private static final String ID2 = "265021810073606625";

	@Mock
	private SmsSubscriptionService smsSubscriptionService;

	@Mock
	private SubscriptionService subscriptionService;

	@InjectMocks
	private SmsSubscriptionAPI smsSubscriptionApi;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		ReflectionTestUtils.setField(smsSubscriptionApi, "segmentId", ID1);
		ReflectionTestUtils.setField(smsSubscriptionApi, "commissionValue", "mockCommissionValue");
	}

	@Test
	void updateSubscriptionStatusTest() throws Exception {
		mockUserAuthentication(authentication);

		UpdateSubscriptionStatusDTO updateSubscriptionStatusDTO = new UpdateSubscriptionStatusDTO();
		HttpServletRequest httpServletRequest = mock(HttpServletRequest.class);
		when(smsSubscriptionService.updateSubscriptionStatus(updateSubscriptionStatusDTO, true, null)).thenReturn("SUCCESS");

		String response = smsSubscriptionApi.updateSubscriptionStatus(updateSubscriptionStatusDTO, httpServletRequest);
		assertEquals("SUCCESS", response);
	}

	@Test
	void fetchSegmentIdTest() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> mockResponse = new HashMap<>();
		mockResponse.put("statusCode", "200");
		String response = "[" +
                "{" +
                "\"category_name\":\"SMS_CHARGES_UO_DIY\"," +
                "\"realised_cat\":\"SMS_CHARGES_UO_DIY\"," +
                "\"realised_cat_id\":235126," +
                "\"commission_type\":\"FLAT\"," +
                "\"commission_value\":" +
                "mockCommissionValue" + "}" +
                "]";

		when(smsSubscriptionService.fetchIdFromCleverTap("mockClient", null, "mockSegmentId")).thenReturn("mockSegmentId");
		when(subscriptionService.fetchSubscription("mid1234", "mid1234", "SMS_CHARGE", null, null, "ACTIVE,SUSPEND")).thenReturn(mockResponse);

		String actualResponse = smsSubscriptionApi.fetchSegmentId("ios", "mockPlatformVersion", null, null, null, null, null);
		assertEquals(response, actualResponse);
	}

	@Test
	void fetchSegmentIdTest2() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> mockResponse = new HashMap<>();
		mockResponse.put("statusCode", "400");

		when(smsSubscriptionService.fetchIdFromCleverTap("mockClient", null, "mockSegmentId")).thenReturn("mockId");
		when(subscriptionService.fetchSubscription("mid1234", "mid1234", "SMS_CHARGE", null, null, "ACTIVE,SUSPEND")).thenReturn(mockResponse);
		when(smsSubscriptionService.fetchIdFromCleverTap(anyString(), anyString(), anyString())).thenReturn(ID2);

		String response = smsSubscriptionApi.fetchSegmentId("ios", "mockPlatformVersion", null, null, null, null, null);
		assertNotNull(response);
		assertTrue(response.contains("status"));
		assertTrue(response.contains(FAILURE));
		assertTrue(response.contains("statusMessage"));
		assertTrue(response.contains("Segment Id not found/invalid"));
	}

}
