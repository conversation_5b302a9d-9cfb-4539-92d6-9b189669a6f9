package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import jakarta.servlet.http.HttpServletResponse;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class AppSupportApiTest {

    @Mock
    private MerchantProfileService merchantProfileService;

    @InjectMocks
    private AppSupportApi appSupportApi;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void forceUpdateMerchants() throws Exception{
        mockUserAuthentication(authentication);
        HttpServletResponse httpServletResponse = mock(HttpServletResponse.class);
        when(merchantProfileService.forceUpdateMerchants("", "", "",httpServletResponse))
                .thenReturn(new ResponseUmp("SUCCESS","200",null, null));
        ResponseUmp response = appSupportApi.forceUpdateMerchants("", "", "",httpServletResponse);
        assertEquals("200",response.getStatusCode());
        assertEquals("SUCCESS",response.getStatus());
    }

}
