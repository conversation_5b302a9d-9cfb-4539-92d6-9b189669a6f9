package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.WidgetApiService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.*;

import static com.paytm.aggregatorgateway.constants.PromoConstants.HTTP_200;
import static com.paytm.aggregatorgateway.constants.UPSIntegrationConstants.SUCCESS;
import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class WidgetApiTest {

	@InjectMocks
	private WidgetApi widgetApi;

	@Mock
	private WidgetApiService widgetApiService;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
	}

	@Test
	void updateWidgetTest() throws Exception {
		mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("mid", "abc");
		requestBody.put("status", "ACTIVE");
		requestBody.put("cardType", "CM_TRANSACTION_BLOCKED");
		requestBody.put("identifierValue", "1235");
		requestBody.put("ttl", 30);

		ResponseUmp responseUmp = new ResponseUmp(SUCCESS, HTTP_200,"Widget pushed successfully", new ArrayList<>());

		when(widgetApiService.updateWidget(any())).thenReturn(responseUmp);

		ResponseUmp response = widgetApi.updateWidget(requestBody);
		assertEquals(SUCCESS, response.getStatus());
		assertEquals(HTTP_200, response.getStatusCode());
		assertEquals("Widget pushed successfully", response.getStatusMessage());
	}

}
