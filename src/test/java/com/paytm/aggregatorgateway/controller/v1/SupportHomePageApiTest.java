package com.paytm.aggregatorgateway.controller.v1;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.service.impl.SupportHomePageServiceImpl;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class SupportHomePageApiTest {

    @InjectMocks
    SupportHomePageApi supportHomePageApi;

    @Mock
    SupportHomePageServiceImpl supportHomePageService;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception{
        MockitoAnnotations.openMocks(this);

        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);

    }

    @Test
    void fetchSurveyTest() throws Exception {

        when(supportHomePageService.getSurvey()).thenReturn("{\n" +
                "  \"actionMapping\": [\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a21\",\n" +
                "      \"eventCategory\": \"Notifications\",\n" +
                "      \"eventAction\": \"Audio Alert Toggled\",\n" +
                "      \"surveyId\": 12,\n" +
                "      \"eventLabel\": \"Off\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=12\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a22\",\n" +
                "      \"eventCategory\": \"Notifications\",\n" +
                "      \"eventAction\": \"Full screen Alert Toggled\",\n" +
                "      \"surveyId\": 13,\n" +
                "      \"eventLabel\": \"Off\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=13\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a23\",\n" +
                "      \"eventCategory\": \"App POS\",\n" +
                "      \"eventAction\": \"Payment detail\",\n" +
                "      \"surveyId\": 14,\n" +
                "      \"eventLabel\": \"Success\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=14\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a24\",\n" +
                "      \"eventCategory\": \"App POS\",\n" +
                "      \"eventAction\": \"Payment detail\",\n" +
                "      \"surveyId\": 14,\n" +
                "      \"eventLabel\": \"Pending\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=14\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a25\",\n" +
                "      \"eventCategory\": \"Notifications\",\n" +
                "      \"eventAction\": \"Sticky Notification Activation toggle clicked\",\n" +
                "      \"surveyId\": 75,\n" +
                "      \"eventLabel\": \"Off\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=75\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a26\",\n" +
                "      \"eventCategory\": \"Notifications\",\n" +
                "      \"eventAction\": \"Paytm Payment Links push notification clicked \",\n" +
                "      \"surveyId\": 78,\n" +
                "      \"eventLabel\": \"None\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=78\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a27\",\n" +
                "      \"eventCategory\": \"Banner\",\n" +
                "      \"eventAction\": \"Paytm Payment Links Banner clicked\",\n" +
                "      \"surveyId\": 78,\n" +
                "      \"eventLabel\": \"None\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=78\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a28\",\n" +
                "      \"eventCategory\": \"Settlement Settings\",\n" +
                "      \"eventAction\": \"Clicks_x_on_aadhar_verification_required\",\n" +
                "      \"surveyId\": 79,\n" +
                "      \"eventLabel\": \"None\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=79\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a29\",\n" +
                "      \"eventCategory\": \"Settlement Settings\",\n" +
                "      \"eventAction\": \"Clicks_back_on_main_add_aadhar\",\n" +
                "      \"surveyId\": 80,\n" +
                "      \"eventLabel\": \"None\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=80\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a2a\",\n" +
                "      \"eventCategory\": \"Bottom Navigation\",\n" +
                "      \"eventAction\": \"Home_Clicked\",\n" +
                "      \"surveyId\": 31,\n" +
                "      \"eventLabel\": \"None\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=31\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"active\": true,\n" +
                "      \"_id\": \"64d1fa0046b54035371c9a2d\",\n" +
                "      \"eventCategory\": \"support_feedback\",\n" +
                "      \"eventAction\": \"support_feedback_yes\",\n" +
                "      \"surveyId\": 8,\n" +
                "      \"eventLabel\": \"support_feedback_yes_issue_solved\",\n" +
                "      \"deeplink\": \"https://survey.paytm.com/static/index.html?survey_id=8\"\n" +
                "    }\n" +
                "  ]\n" +
                "}");
        String result=supportHomePageApi.fetchSurvey();
        assertNotNull(result);

    }

    @Test
    void fetchIssueCategoryTest() throws Exception
    {
        Map<String,String> response = new HashMap<>();
        response.put("edcRented","true");
        when(supportHomePageService.fetchIssueCategory(anyMap())).thenReturn("     {\n" +
                "    \"metadata\": {},\n" +
                "    \"page_id\": 58186,\n" +
                "    \"page_name\": \"Category Listing on Support P4B \",\n" +
                "    \"context\": {\n" +
                "        \"user\": {},\n" +
                "        \"request_id\": \"cb72a465-48fe-42c0-add4-e8bd6e125145\"\n" +
                "    },\n" +
                "    \"paytm_logo_url\": \"https://assetscdn1.paytm.com/images/catalog/category/5165/paytm_logo.png\",\n" +
                "    \"page\": [\n" +
                "        {\n" +
                "            \"id\": 338971,\n" +
                "            \"views\": [\n" +
                "                {\n" +
                "                    \"id\": 338971,\n" +
                "                    \"type\": \"list-small-ti\",\n" +
                "                    \"title\": \"Category Selection\",\n" +
                "                    \"items\": [\n" +
                "                        {\n" +
                "                            \"id\": 1407710,\n" +
                "                            \"name\": \"Soundbox\",\n" +
                "                            \"url\": \"paytmba://csttree?featuretype=cst_issue&cstentity=p4bsoundbox&urlType=launchBot&calledFrom=StorefrontSupport\",\n" +
                "                            \"url_type\": \"embed\",\n" +
                "                            \"source\": \"storefront\",\n" +
                "                            \"seourl\": \"paytmba://csttree?featuretype=cst_issue&cstentity=p4bsoundbox&urlType=launchBot&calledFrom=StorefrontSupport\",\n" +
                "                            \"layout\": {},\n" +
                "                            \"image_url\": \"https://assetscdn1.paytm.com/images/catalog/view_item/1407710/1685437702776.png\",\n" +
                "                            \"item_id\": 0,\n" +
                "                            \"priority\": 1,\n" +
                "                            \"cta\": {\n" +
                "                                \"label\": \"\",\n" +
                "                                \"url\": \"\",\n" +
                "                                \"aligned\": \"\"\n" +
                "                            },\n" +
                "                            \"ga_category\": \"Others\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\": 1407711,\n" +
                "                            \"name\": \"Payment & Settlement\",\n" +
                "                            \"url\": \"paytmba://csttree?featuretype=cst_issue&cstentity=p4bpayout&settlement&urlType=launchBot&calledFrom=StorefrontSupport\",\n" +
                "                            \"url_type\": \"embed\",\n" +
                "                            \"source\": \"storefront\",\n" +
                "                            \"seourl\": \"paytmba://csttree?featuretype=cst_issue&cstentity=p4bpayout&settlement&urlType=launchBot&calledFrom=StorefrontSupport\",\n" +
                "                            \"layout\": {},\n" +
                "                            \"image_url\": \"https://assetscdn1.paytm.com/images/catalog/view_item/1407711/1685437678484.png\",\n" +
                "                            \"item_id\": 0,\n" +
                "                            \"priority\": 2,\n" +
                "                            \"cta\": {\n" +
                "                                \"label\": \"\",\n" +
                "                                \"url\": \"\",\n" +
                "                                \"aligned\": \"\"\n" +
                "                            },\n" +
                "                            \"ga_category\": \"Others\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\": 1407712,\n" +
                "                            \"name\": \"Business Loan\",\n" +
                "                            \"url\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"url_type\": \"embed\",\n" +
                "                            \"source\": \"storefront\",\n" +
                "                            \"seourl\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"layout\": {},\n" +
                "                            \"image_url\": \"https://assetscdn1.paytm.com/images/catalog/view_item/1407712/1685437751474.png\",\n" +
                "                            \"item_id\": 0,\n" +
                "                            \"priority\": 3,\n" +
                "                            \"cta\": {\n" +
                "                                \"label\": \"\",\n" +
                "                                \"url\": \"\",\n" +
                "                                \"aligned\": \"\"\n" +
                "                            },\n" +
                "                            \"ga_category\": \"Others\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\": 1407713,\n" +
                "                            \"name\": \"Account & Settings\",\n" +
                "                            \"url\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"url_type\": \"embed\",\n" +
                "                            \"source\": \"storefront\",\n" +
                "                            \"seourl\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"layout\": {},\n" +
                "                            \"image_url\": \"https://assetscdn1.paytm.com/images/catalog/view_item/1407713/*************.png\",\n" +
                "                            \"item_id\": 0,\n" +
                "                            \"priority\": 4,\n" +
                "                            \"cta\": {\n" +
                "                                \"label\": \"\",\n" +
                "                                \"url\": \"\",\n" +
                "                                \"aligned\": \"\"\n" +
                "                            },\n" +
                "                            \"ga_category\": \"Others\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\": 1407714,\n" +
                "                            \"name\": \"Card Machine\",\n" +
                "                            \"url\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"url_type\": \"embed\",\n" +
                "                            \"source\": \"storefront\",\n" +
                "                            \"seourl\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"layout\": {},\n" +
                "                            \"image_url\": \"https://assetscdn1.paytm.com/images/catalog/view_item/1407714/1685437826375.png\",\n" +
                "                            \"item_id\": 0,\n" +
                "                            \"priority\": 5,\n" +
                "                            \"cta\": {\n" +
                "                                \"label\": \"\",\n" +
                "                                \"url\": \"\",\n" +
                "                                \"aligned\": \"\"\n" +
                "                            },\n" +
                "                            \"ga_category\": \"Others\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\": 1407715,\n" +
                "                            \"name\": \"Deals\",\n" +
                "                            \"url\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"url_type\": \"embed\",\n" +
                "                            \"source\": \"storefront\",\n" +
                "                            \"seourl\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"layout\": {},\n" +
                "                            \"image_url\": \"https://assetscdn1.paytm.com/images/catalog/view_item/1407715/1685437851999.png\",\n" +
                "                            \"item_id\": 0,\n" +
                "                            \"priority\": 6,\n" +
                "                            \"cta\": {\n" +
                "                                \"label\": \"\",\n" +
                "                                \"url\": \"\",\n" +
                "                                \"aligned\": \"\"\n" +
                "                            },\n" +
                "                            \"ga_category\": \"Others\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\": 1407716,\n" +
                "                            \"name\": \"Others\",\n" +
                "                            \"url\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"url_type\": \"embed\",\n" +
                "                            \"source\": \"storefront\",\n" +
                "                            \"seourl\": \"https://storefront-staging.paytm.compaytm.com\",\n" +
                "                            \"layout\": {},\n" +
                "                            \"image_url\": \"https://assetscdn1.paytm.com/images/catalog/view_item/1407716/1685437878446.png\",\n" +
                "                            \"item_id\": 0,\n" +
                "                            \"priority\": 7,\n" +
                "                            \"cta\": {\n" +
                "                                \"label\": \"\",\n" +
                "                                \"url\": \"\",\n" +
                "                                \"aligned\": \"\"\n" +
                "                            },\n" +
                "                            \"ga_category\": \"Others\"\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"auto_scroll\": \"False\",\n" +
                "                    \"is_bg_active\": false,\n" +
                "                    \"meta_layout\": {\n" +
                "                        \"show_scroll_indicator\": false\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"footer_image_url\": \"https://assetscdn1.paytm.com/images/catalog/category/5165/footer1.png\",\n" +
                "    \"placeholder_image_url\": \"https://assetscdn1.paytm.com/images/catalog/category/5165/placeholder.png\",\n" +
                "    \"ga_key\": \"/h/category-listing-on-support-p4b-clpid-58186\",\n" +
                "    \"entity_type\": \"category\",\n" +
                "    \"ga_category\": \"Others\",\n" +
                "    \"vertical_name\": \"P4B\",\n" +
                "    \"layout\": {}\n" +
                "}");
        String result = supportHomePageApi.fetchIssueCategory(response);
        assertNotNull(result);
    }

    //TODO mock response to be pasted
    @Test
    void getAllTicketsTest() throws Exception
    {
        String alltickets = "{\n" +
                "    \"tickets\": [\n" +
                "        {\n" +
                "            \"source\": \"FreshDesk\",\n" +
                "            \"createdAt\": \"2023-06-06 14:54:20\",\n" +
                "            \"updatedAt\": \"2023-07-18 17:23:42\",\n" +
                "            \"cstentity\": \"p4bedc\",\n" +
                "            \"botParams\": null,\n" +
                "            \"requesterId\": 14000006635053,\n" +
                "            \"ticketIcon\": \"https://assetscdn1.paytm.com/paytm-cst/Verticals/ic_insurance.png\",\n" +
                "            \"itemName\": \"Ticket related to p4bedc\",\n" +
                "            \"caseCreationDate\": \"2023-06-06 14:54:20\",\n" +
                "            \"ticketNumber\": \"34170\",\n" +
                "            \"status\": \"Open\",\n" +
                "            \"formattedCreatedDate\": \"06/06/2023 14:54 PM\",\n" +
                "            \"umpMid\": null,\n" +
                "            \"viewType\": \"FreshDesk\",\n" +
                "            \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "            \"transactionId\": null,\n" +
                "            \"l1IssueCategory\": \" Others\",\n" +
                "            \"verticalLabel\": null,\n" +
                "            \"custom_fields\": null,\n" +
                "            \"tags\": [],\n" +
                "            \"customerIssueCategoryL1\": null,\n" +
                "            \"ticketStatus\": 28\n" +
                "        },\n" +
                "        {\n" +
                "            \"source\": \"FreshDesk\",\n" +
                "            \"createdAt\": \"2023-07-12 19:03:27\",\n" +
                "            \"updatedAt\": \"2023-07-18 17:08:22\",\n" +
                "            \"cstentity\": \"p4bsoundbox\",\n" +
                "            \"botParams\": null,\n" +
                "            \"requesterId\": 14000006635053,\n" +
                "            \"ticketIcon\": \"https://assetscdn1.paytm.com/paytm-cst/Verticals/ic_insurance.png\",\n" +
                "            \"itemName\": \"Ticket Related to Payout & Settlement\",\n" +
                "            \"caseCreationDate\": \"2023-07-12 19:03:27\",\n" +
                "            \"ticketNumber\": \"35575\",\n" +
                "            \"status\": \"Open\",\n" +
                "            \"formattedCreatedDate\": \"12/07/2023 19:03 PM\",\n" +
                "            \"umpMid\": null,\n" +
                "            \"viewType\": \"FreshDesk\",\n" +
                "            \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "            \"transactionId\": \"1234567891\",\n" +
                "            \"l1IssueCategory\": \"SB Deactivation Request\",\n" +
                "            \"verticalLabel\": null,\n" +
                "            \"custom_fields\": null,\n" +
                "            \"tags\": [\n" +
                "                \"FSM Parked\",\n" +
                "                \"Service\"\n" +
                "            ],\n" +
                "            \"customerIssueCategoryL1\": null,\n" +
                "            \"ticketStatus\": 2\n" +
                "        },\n" +
                "        {\n" +
                "            \"source\": \"FreshDesk\",\n" +
                "            \"createdAt\": \"2023-06-08 16:55:52\",\n" +
                "            \"updatedAt\": \"2023-06-13 02:50:13\",\n" +
                "            \"cstentity\": \"p4bedc\",\n" +
                "            \"botParams\": null,\n" +
                "            \"requesterId\": 14000006635053,\n" +
                "            \"ticketIcon\": \"https://assetscdn1.paytm.com/paytm-cst/Verticals/ic_insurance.png\",\n" +
                "            \"itemName\": \"Ticket related to p4bedc\",\n" +
                "            \"caseCreationDate\": \"2023-06-08 16:55:52\",\n" +
                "            \"ticketNumber\": \"34216\",\n" +
                "            \"status\": \"Open\",\n" +
                "            \"formattedCreatedDate\": \"08/06/2023 16:55 PM\",\n" +
                "            \"umpMid\": null,\n" +
                "            \"viewType\": \"FreshBot\",\n" +
                "            \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "            \"transactionId\": null,\n" +
                "            \"l1IssueCategory\": \" Others\",\n" +
                "            \"verticalLabel\": null,\n" +
                "            \"custom_fields\": null,\n" +
                "            \"tags\": [],\n" +
                "            \"customerIssueCategoryL1\": null,\n" +
                "            \"ticketStatus\": 21\n" +
                "        },\n" +
                "        {\n" +
                "            \"source\": \"FreshDesk\",\n" +
                "            \"createdAt\": \"2023-06-06 14:54:34\",\n" +
                "            \"updatedAt\": \"2023-06-09 02:36:27\",\n" +
                "            \"cstentity\": \"p4bedc\",\n" +
                "            \"botParams\": null,\n" +
                "            \"requesterId\": 14000006635053,\n" +
                "            \"ticketIcon\": \"https://assetscdn1.paytm.com/paytm-cst/Verticals/ic_insurance.png\",\n" +
                "            \"itemName\": \"Ticket related to p4bedc\",\n" +
                "            \"caseCreationDate\": \"2023-06-06 14:54:34\",\n" +
                "            \"ticketNumber\": \"34171\",\n" +
                "            \"status\": \"Open\",\n" +
                "            \"formattedCreatedDate\": \"06/06/2023 14:54 PM\",\n" +
                "            \"umpMid\": null,\n" +
                "            \"viewType\": \"FreshBot\",\n" +
                "            \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "            \"transactionId\": null,\n" +
                "            \"l1IssueCategory\": \" Others\",\n" +
                "            \"verticalLabel\": null,\n" +
                "            \"custom_fields\": null,\n" +
                "            \"tags\": [],\n" +
                "            \"customerIssueCategoryL1\": null,\n" +
                "            \"ticketStatus\": 21\n" +
                "        },\n" +
                "        {\n" +
                "            \"source\": \"FreshDesk\",\n" +
                "            \"createdAt\": \"2023-06-02 17:39:43\",\n" +
                "            \"updatedAt\": \"2023-06-07 18:07:45\",\n" +
                "            \"cstentity\": \"p4bedc\",\n" +
                "            \"botParams\": null,\n" +
                "            \"requesterId\": 14000006635053,\n" +
                "            \"ticketIcon\": \"https://assetscdn1.paytm.com/paytm-cst/Verticals/ic_insurance.png\",\n" +
                "            \"itemName\": \"Ticket related to p4bedc\",\n" +
                "            \"caseCreationDate\": \"2023-06-02 17:39:43\",\n" +
                "            \"ticketNumber\": \"34041\",\n" +
                "            \"status\": \"Open\",\n" +
                "            \"formattedCreatedDate\": \"02/06/2023 17:39 PM\",\n" +
                "            \"umpMid\": null,\n" +
                "            \"viewType\": \"FreshDesk\",\n" +
                "            \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "            \"transactionId\": null,\n" +
                "            \"l1IssueCategory\": \"EDC Hardware\",\n" +
                "            \"verticalLabel\": null,\n" +
                "            \"custom_fields\": null,\n" +
                "            \"tags\": [],\n" +
                "            \"customerIssueCategoryL1\": null,\n" +
                "            \"ticketStatus\": 2\n" +
                "        },\n" +
                "        {\n" +
                "            \"source\": \"FreshDesk\",\n" +
                "            \"createdAt\": \"2023-06-01 17:06:05\",\n" +
                "            \"updatedAt\": \"2023-07-16 13:53:23\",\n" +
                "            \"cstentity\": \"p4bedc\",\n" +
                "            \"botParams\": null,\n" +
                "            \"requesterId\": 14000006635053,\n" +
                "            \"ticketIcon\": \"https://assetscdn1.paytm.com/paytm-cst/Verticals/ic_insurance.png\",\n" +
                "            \"itemName\": \"Ticket related to p4bedc\",\n" +
                "            \"caseCreationDate\": \"2023-06-01 17:06:05\",\n" +
                "            \"ticketNumber\": \"34022\",\n" +
                "            \"status\": \"Solved\",\n" +
                "            \"formattedCreatedDate\": \"01/06/2023 17:06 PM\",\n" +
                "            \"umpMid\": null,\n" +
                "            \"viewType\": \"FreshDesk\",\n" +
                "            \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "            \"transactionId\": null,\n" +
                "            \"l1IssueCategory\": null,\n" +
                "            \"verticalLabel\": null,\n" +
                "            \"custom_fields\": null,\n" +
                "            \"tags\": [],\n" +
                "            \"customerIssueCategoryL1\": null,\n" +
                "            \"ticketStatus\": 5\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String,Object> MockResponse = objectMapper.readValue(alltickets,Map.class);
        when(supportHomePageService.getAllTickets(anyBoolean())).thenReturn(MockResponse);
        Map<String,Object> response = supportHomePageApi.getAllTickets();
        assertNotNull(response);
    }

    //TODO mock response to be pasted
    @Test
    void getRecentTicketTest() throws Exception
    {
        String recentTicket = "{\n" +
                "    \"pastTickets\": false,\n" +
                "    \"itemName\": \"Ticket related to p4bedc\",\n" +
                "    \"cst_entity\": \"p4bedc\",\n" +
                "    \"ticketStatus\": 28,\n" +
                "    \"ticketTimeLine\": {\n" +
                "        \"ticketNumber\": \"34170\",\n" +
                "        \"states\": [\n" +
                "            {\n" +
                "                \"displayOrder\": 1,\n" +
                "                \"statusFlag\": \"Completed\",\n" +
                "                \"headerText\": \"Issue Raised on Chat\",\n" +
                "                \"descriptionText\": null,\n" +
                "                \"metadata\": null,\n" +
                "                \"timestamp\": 1689681222000\n" +
                "            },\n" +
                "            {\n" +
                "                \"displayOrder\": 2,\n" +
                "                \"statusFlag\": \"In-Progress\",\n" +
                "                \"headerText\": \"Document Upload\",\n" +
                "                \"descriptionText\": null,\n" +
                "                \"metadata\": {\n" +
                "                    \"cta\": \"Upload Document\",\n" +
                "                    \"deeplink\": \" paytmmp://csttree?featuretype=cst_issue&cstentity=<CSTENTITY>&urlType=launchBot&ticketNumber=<CSTTicketNumber>&calledFrom=<PageName>\"\n" +
                "                },\n" +
                "                \"timestamp\": 1689681222000\n" +
                "            },\n" +
                "            {\n" +
                "                \"displayOrder\": 3,\n" +
                "                \"statusFlag\": \"Not Started\",\n" +
                "                \"headerText\": \"Issue Resolution\",\n" +
                "                \"descriptionText\": null,\n" +
                "                \"metadata\": null,\n" +
                "                \"timestamp\": null\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String,Object> MockResponse = objectMapper.readValue(recentTicket,Map.class);
        when(supportHomePageService.getRecentTicket()).thenReturn(MockResponse);
        Map<String,Object> response = supportHomePageApi.getRecentTicket();
        assertNotNull(response);
    }

    @Test
    void ticketTimeLineTest() throws Exception
    {
        TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();
        ticketInfoDTO.setCreatedAt("12-11-2023");
        ticketInfoDTO.setTicketIcon("icon");
        ticketInfoDTO.setCstentity("p4b");
        ticketInfoDTO.setTicketNumber("12345678");
        ticketInfoDTO.setTicketStatus(5);
        ticketInfoDTO.setCaseCreationDate("12-11-2020");
        ticketInfoDTO.setCustom_fields(new HashMap<>());
        ticketInfoDTO.setCustomerIssueCategoryL1("category");
        ticketInfoDTO.setFormattedCreatedDate("12-11-2020");
        ticketInfoDTO.setFreshDeskUrl("url");
        ticketInfoDTO.setItemName("itemName");
        ticketInfoDTO.setL1IssueCategory("l1");
        ticketInfoDTO.setSource("p4b");
        ticketInfoDTO.setBotParams("params");
        ticketInfoDTO.setViewType("viewType");
        ticketInfoDTO.setVerticalLabel("label");
        ticketInfoDTO.setUpdatedAt("12-11-2020");
        ticketInfoDTO.setUmpMid("mid");
        ticketInfoDTO.setTransactionId("txnId");
        ticketInfoDTO.setTags(new ArrayList<>());
        when(supportHomePageService.getTicketTimeLine(Mockito.any())).thenReturn("{\n" +
                "    \"ticketNumber\": \"35575\",\n" +
                "    \"states\": [\n" +
                "        {\n" +
                "            \"displayOrder\": 1,\n" +
                "            \"statusFlag\": \"Completed\",\n" +
                "            \"headerText\": \"Issue Raised on Mail\",\n" +
                "            \"descriptionText\": null,\n" +
                "            \"metadata\": null,\n" +
                "            \"timestamp\": 1689680302000\n" +
                "        },\n" +
                "        {\n" +
                "            \"displayOrder\": 2,\n" +
                "            \"statusFlag\": \"Completed\",\n" +
                "            \"headerText\": \"Agent Karan Assigned\",\n" +
                "            \"descriptionText\": \"Agent will visit you in <TAT>\",\n" +
                "            \"metadata\": {\n" +
                "                \"tatHrs\": \"72\",\n" +
                "                \"tatEndTimestamp\": \"1689939502000\"\n" +
                "            },\n" +
                "            \"timestamp\": 1689680302000\n" +
                "        },\n" +
                "        {\n" +
                "            \"displayOrder\": 3,\n" +
                "            \"statusFlag\": \"Not Started\",\n" +
                "            \"headerText\": \"Issue Resolution\",\n" +
                "            \"descriptionText\": null,\n" +
                "            \"metadata\": null,\n" +
                "            \"timestamp\": null\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        String result = supportHomePageApi.getTicketTimeLine(ticketInfoDTO);
        assertNotNull(result);
    }

    @Test
    void reopenClosedTicketTest() throws Exception {

        mockUserAuthentication(authentication);
        when(supportHomePageService.reopenTicket(anyString(),anyString(), anyString(),anyBoolean())).thenReturn("abc");
        String response = supportHomePageApi.reopenClosedTicket("1234","abcd",false);
        assertNotNull(response);

    }

    @Test
    void reopenClosedTicketExceptionTest() throws Exception {

        when(supportHomePageService.reopenTicket(anyString(),anyString(), anyString(),anyBoolean())).thenReturn("abc");
        assertThrows(Exception.class, () -> {
            supportHomePageApi.reopenClosedTicket("abcd","abcd",false);
        });

    }

    @Test
    void uploadFeedbackTest() throws Exception {

        mockUserAuthentication(authentication);
        when(supportHomePageService.feedbackUpload(anyMap())).thenReturn("abc");
        Object response = supportHomePageApi.uploadFeedback(new HashMap<>());
        assertNotNull(response);
    }

    /**
     * Unit testCase for function getCallDetails()
     * Expectation: clean flow, returns response
     */
    @Test
    void getCallDetails() throws IOException, InterruptedException, UMPIntegrationException {
        Mockito.when(supportHomePageService.getCallDetails()).thenReturn(new ResponseUmp());
        assertNotNull(supportHomePageApi.getCallDetails());
    }

    /**
     * Unit testCase for function requestCallBack
     */
    @Test
    void requestCallback() throws Exception {
        Mockito.when(supportHomePageService.requestCallBack(true, true)).thenReturn(new ResponseUmp());
        assertNotNull(supportHomePageApi.requestCallBack(true, true));
    }

}