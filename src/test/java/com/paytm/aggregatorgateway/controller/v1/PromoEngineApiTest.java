package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.PromoEngineService;
import com.paytm.aggregatorgateway.vo.PromoRequestVO;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class PromoEngineApiTest {

	private static final String ID = "625026502618107360";

	@Mock
	private PromoEngineService promoEngineService;

	@InjectMocks
	private PromoEngineApi promoEngineApi;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = Mockito.mock(UserAuthentication.class);
		SecurityContext securityContext = Mockito.mock(SecurityContext.class);
		Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
	}

	@Test
	void gameListTest() throws Exception {
		mockUserAuthentication(authentication);

		PromoRequestVO request = new PromoRequestVO();
		request.setPage_number(0);
		request.setPage_size(5);
		request.setGame_id(ID);

		when(promoEngineService.getGameList(any())).thenReturn("mockResponse");

		String res = promoEngineApi.gameList(request);
		assertNotNull(res);
		assertEquals("mockResponse", res);
	}

	@Test
	void txnDetailTest() throws Exception {
		mockUserAuthentication(authentication);

		PromoRequestVO request = new PromoRequestVO();
		request.setPage_number(0);
		request.setPage_size(5);
		request.setGame_id(ID);

		when(promoEngineService.getTxnDetail(any())).thenReturn("mockResponse");

		String res = promoEngineApi.txnDetail(request);
		assertNotNull(res);
		assertEquals("mockResponse", res);
	}

	@Test
	void allOffersTest() throws Exception {
		mockUserAuthentication(authentication);

		PromoRequestVO request = new PromoRequestVO();
		request.setPage_number(0);
		request.setPage_size(5);
		request.setCampaign_id(ID);

		when(promoEngineService.getAllOffers(any())).thenReturn("mockResponse");

		String res = promoEngineApi.allOffers(request);
		assertNotNull(res);
		assertEquals("mockResponse", res);
	}

	@Test
	void activateOfferTest() throws Exception {
		mockUserAuthentication(authentication);

		PromoRequestVO request = new PromoRequestVO();
		request.setPage_number(0);
		request.setPage_size(5);
		request.setCampaign_id(ID);

		when(promoEngineService.activateOffer(any())).thenReturn("mockResponse");

		String res = promoEngineApi.activateOffer(request);
		assertNotNull(res);
		assertEquals("mockResponse", res);
	}

}
