package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.dto.Address;
import com.paytm.aggregatorgateway.dto.AddressUpdateDTO;
import com.paytm.aggregatorgateway.enums.Status;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.service.AddressUpdateService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;

import static com.paytm.aggregatorgateway.constants.PromoConstants.HTTP_200;
import static com.paytm.aggregatorgateway.constants.UPSIntegrationConstants.SUCCESS;
import static org.junit.jupiter.api.Assertions.*;
import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class AddressUpdateApiTest {

	@InjectMocks
	private AddressUpdateAPI addressUpdateAPI;

	@Mock
	private AddressUpdateService addressUpdateService;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception{
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
	}

	@Test
	void getTrendingTopicsTest1() throws Exception {
		mockUserAuthentication(authentication);

		AddressUpdateDTO addressUpdateDTO = new AddressUpdateDTO();
		addressUpdateDTO.setTicketNumber("TicketNumber");
		addressUpdateDTO.setAddress(getAddress());
		ResponseUmp responseUmp = new ResponseUmp(SUCCESS,HTTP_200,"Address updated successfully", new ArrayList<>());

		when(addressUpdateService.updateAddress(addressUpdateDTO)).thenReturn(responseUmp);
		ResponseUmp response = addressUpdateAPI.addressUpdate(addressUpdateDTO);

		assertEquals(SUCCESS, response.getStatus());
		assertEquals(HTTP_200, response.getStatusCode());
		assertEquals("Address updated successfully", response.getStatusMessage());
	}

	@Test
	void getTrendingTopicsTest2() throws Exception {
		mockUserAuthentication(authentication);

		AddressUpdateDTO addressUpdateDTO = new AddressUpdateDTO();
		addressUpdateDTO.setAddress(getAddress());
		ResponseUmp responseUmp = new ResponseUmp(SUCCESS,HTTP_200,"Address updated successfully", new ArrayList<>());

		when(addressUpdateService.updateAddress(addressUpdateDTO)).thenReturn(responseUmp);

		try {
			addressUpdateAPI.addressUpdate(addressUpdateDTO);
		} catch (ResponseUmpException exception){
			assertNotNull(exception.getResponse());
			assertEquals(Status.FAILURE.name(), exception.getResponse().getStatus());
			assertEquals(UMPErrorCodeEnums.EMPTY_TICKET_NO.getErrorCode(), exception.getResponse().getStatusCode());
			assertEquals(UMPErrorCodeEnums.EMPTY_TICKET_NO.getErrorMsg(), exception.getResponse().getStatusMessage());
			assertNull(exception.getResponse().getResults());
		}
	}

	@Test
	void getTrendingTopicsTest3() throws Exception {
		mockUserAuthentication(authentication);

		AddressUpdateDTO addressUpdateDTO = new AddressUpdateDTO();
		addressUpdateDTO.setTicketNumber("TicketNumber");
		ResponseUmp responseUmp = new ResponseUmp(SUCCESS,HTTP_200,"Address updated successfully", new ArrayList<>());

		when(addressUpdateService.updateAddress(addressUpdateDTO)).thenReturn(responseUmp);

		try {
			addressUpdateAPI.addressUpdate(addressUpdateDTO);
		} catch (ResponseUmpException exception){
			assertNotNull(exception.getResponse());
			assertEquals(Status.FAILURE.name(), exception.getResponse().getStatus());
			assertEquals(UMPErrorCodeEnums.EMPTY_ADDRESS.getErrorCode(), exception.getResponse().getStatusCode());
			assertEquals(UMPErrorCodeEnums.EMPTY_ADDRESS.getErrorMsg(), exception.getResponse().getStatusMessage());
			assertNull(exception.getResponse().getResults());
		}
	}

	private Address getAddress() {
		Address address = new Address();
		address.setAddress_line_2("Noida Heights");
		address.setCity("Noida");
		address.setPostalCode("145001");
		address.setLatitude("38.8951");
		address.setLongitude("-77.0364");
		address.setState("Uttar Pradesh");
		return address;
	}

}
