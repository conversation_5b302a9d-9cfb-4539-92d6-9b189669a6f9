package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.MerchantReferralService;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class MerchantReferralAPITest {

    @InjectMocks
    private MerchantReferralAPI merchantReferralAPI;

    @Mock
    private MerchantReferralService merchantReferralService;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void getReferralTest() throws Exception
    {
        mockUserAuthentication(authentication);
        String mockRes="123";
        when( merchantReferralService.getReferral(anyString(),anyString(),anyString(),anyString())).thenReturn(mockRes);
       String res=merchantReferralAPI.getReferral("123","","");
        assertEquals(res,mockRes);
    }

}
