package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.aggregatorgateway.service.LoyaltyPointsService;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashMap;
import java.util.Map;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class LoyaltyPointsAPITest {

    @Mock
    private LoyaltyPointsService service;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private LoyaltyPointsAPI loyaltyPointsAPI;

    @BeforeEach
    public void init() throws Exception {
        MockitoAnnotations.openMocks(this);

        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void getSummaryTest() throws Exception{
        mockUserAuthentication(authentication);
        when(service.fetchUid(anyString())).thenReturn("123");
        when(service.getSummary(anyString())).thenReturn(new ResponseUmp());
        ResponseUmp response = loyaltyPointsAPI.getSummary();
        assertNotNull(response);

    }

    @Test
    void checkBalanceTest() throws Exception{
        mockUserAuthentication(authentication);
        when(service.fetchUid(anyString())).thenReturn("123");
        when(service.checkBalance(anyString())).thenReturn("123");
        String response = loyaltyPointsAPI.checkBalance();
        assertNotNull(response);
    }

    @Test
    void getListTest() throws Exception{
        mockUserAuthentication(authentication);
        Map<String,Object> request= new HashMap<>();
        when(service.fetchUid(any())).thenReturn("123");
        when(service.getList(any(), any())).thenReturn(new ResponseUmp());
        ResponseUmp response = loyaltyPointsAPI.getList(request);
        assertNotNull(response);
    }
}
