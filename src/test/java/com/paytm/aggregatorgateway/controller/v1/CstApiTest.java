package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.CstService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class CstApiTest {

    @Mock
    private CstService cstService;

    @InjectMocks
    private CstApis cstApis;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception{
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void getTrendingTopicsTest() throws Exception{
        mockUserAuthentication(authentication);
        when(cstService.getTrendingTopics("lan","src","verticalId","tag"))
                .thenReturn(new ResponseUmp("SUCCESS","200","Successfully fetched details from CST",new ArrayList<>()));
        ResponseUmp response = cstApis.getTrendingTopics("lan","src","verticalId","tag");
        assertEquals("SUCCESS", response.getStatus());
        assertEquals("200", response.getStatusCode());
        assertEquals("Successfully fetched details from CST", response.getStatusMessage());
    }

    @Test
    void getTicketDetailsTest() throws Exception{
        mockUserAuthentication(authentication);
        when(cstService.getTicketDetails(anyString()))
                .thenReturn(new ResponseUmp("SUCCESS","200","Successfully fetched details from CST",new ArrayList<>()));
        ResponseUmp response = cstApis.getTicketDetails("t123");
        assertEquals("SUCCESS", response.getStatus());
        assertEquals("200", response.getStatusCode());
        assertEquals("Successfully fetched details from CST", response.getStatusMessage());
    }

}
