package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.AppPosService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class AppPosTest {

    @InjectMocks
    private AppPos appPos;

    @Mock
    private AppPosService appPosService;

    @Test
    void testCheckStatus() throws Exception {
        Mockito.when(appPosService.checkStatus(Mockito.anyMap())).thenReturn("STATUS");
        String res = appPos.checkStatus(Mockito.anyMap());
        assertEquals("STATUS", res);
    }
}