package com.paytm.aggregatorgateway.controller.v1;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.service.NudgeApiService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class NudgeApiTest {

    @InjectMocks
    private NudgeApi nudgeApi;

    @Mock
    private NudgeApiService nudgeApiService;

    @Test
    public void updateNudgeTest() throws Exception
    {
        ResponseUmp responseUmp = new ResponseUmp();
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW reason: merchant PAN is not submitted.\",\n" +
                "  \"consultDate\": \"2023-05-12T13:25:05.364+05:30\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12T13:25:05.362+05:30\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW reason: merchant PAN is not submitted.\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        when(nudgeApiService.updateNudge(any())).thenReturn(responseUmp);
        ResponseUmp response = nudgeApi.updateNudge(requestMap);
        assertNotNull(response);
    }

}