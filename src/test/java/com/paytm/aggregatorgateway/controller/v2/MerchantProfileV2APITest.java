package com.paytm.aggregatorgateway.controller.v2;

import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MerchantProfileV2APITest {

    @InjectMocks
    private MerchantProfileV2API merchantProfileV2API;

    @Mock
    private MerchantProfileService merchantProfileService;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception
    {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void testGetNotificationSettingsException()  throws Exception
    {
        HttpServletRequest httpRequest=mock(HttpServletRequest.class);
        assertThrows(ValidationException.class, () -> {
            merchantProfileV2API.getNotificationSettings("",httpRequest);
        });
    }

    @Test
    void testGetNotificationSettingsException1()  throws Exception
    {
        HttpServletRequest httpRequest=mock(HttpServletRequest.class);
        assertThrows(ValidationException.class, () -> {
            merchantProfileV2API.getNotificationSettings("ALL",httpRequest);
        });
    }

    @Test
    void testGetNotificationSettings()  throws Exception
    {
        mockUserAuthentication(authentication);
        Map<String,Object> map=new HashMap<>();
        when(merchantProfileService.getCommunicationConfiguration(any(), any(), any())).thenReturn(map);
        HttpServletRequest httpRequest=mock(HttpServletRequest.class);
        Map<String,Object> response=merchantProfileV2API.getNotificationSettings("ALL", httpRequest);
        assertEquals(map, response);
    }

    @Test
    void testSetNotificationSettingsException() throws Exception
    {
        HttpServletRequest httpRequest=mock(HttpServletRequest.class);
        assertThrows(ValidationException.class, () -> {
            merchantProfileV2API.setNotificationSettings(null,httpRequest);
        });
    }

    @Test
    void testSetNotificationSettings() throws Exception
    {
        mockUserAuthentication(authentication);
        Map<String,Object> map=new HashMap<>();
        Map<String,Object> transaction=new HashMap<>();
        transaction.put("event","event");
        map.put("transaction",transaction);
        when(merchantProfileService.setCommunicationConfiguration(anyMap(),anyString(), any())).thenReturn(map);
        HttpServletRequest httpRequest=mock(HttpServletRequest.class);
        Map<String,Object> response=merchantProfileV2API.setNotificationSettings(map, httpRequest);
        assertTrue(response.containsKey("transaction"));
    }

}
