package com.paytm.aggregatorgateway.controller.v2;

import com.paytm.aggregatorgateway.service.PromoEngineService;
import com.paytm.aggregatorgateway.vo.PromoRequestVO;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Map;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class PromoEngineApiV2Test {

	private static final String MID = "625026502618107360";

	private static final String CAMPAIGN_ID = "26502618106181";

	@InjectMocks
	private PromoEngineApiV2 promoEngineApiV2;

	@Mock
	private PromoEngineService promoEngineService;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
	}

    @Test
	void gameListTest() throws Exception {
		mockUserAuthentication(authentication);
        PromoRequestVO promoRequestVO = new PromoRequestVO();
		promoRequestVO.setPage_size(5);
		promoRequestVO.setPage_number(0);

		when(promoEngineService.getGameListV2(any())).thenReturn("res");
        String res = promoEngineApiV2.gameList(promoRequestVO);
		assertEquals("res", res);
    }

    @Test
	void campaignGameTest() throws Exception {
		mockUserAuthentication(authentication);
		when(promoEngineService.getCampaignGameV2(anyString(), anyString())).thenReturn("mid");
		String res = promoEngineApiV2.campaignGame(MID);
		assertEquals("mid", res);
    }

    @Test
	void selectOfferTest() throws Exception {
		mockUserAuthentication(authentication);
		when(promoEngineService.selectOffer(anyString(), anyString(), anyString())).thenReturn("selectOffer");
		String res = promoEngineApiV2.selectOffer(CAMPAIGN_ID, "body");
		assertEquals("selectOffer", res);
    }

    @Test
	void gameDetailsTest() throws Exception {
		mockUserAuthentication(authentication);
        when(promoEngineService.gameDetails(anyString(), anyString())).thenReturn("gameDetails");
        String res = promoEngineApiV2.gameDetails(MID);
        assertEquals("gameDetails", res);
    }

	@Test
	void campaignGamesTest() throws Exception {
		mockUserAuthentication(authentication);
		when(promoEngineService.campaignGames()).thenReturn(null);
		Map<String, Object> res = promoEngineApiV2.campaignGames();
		assertNull(res);
	}

}