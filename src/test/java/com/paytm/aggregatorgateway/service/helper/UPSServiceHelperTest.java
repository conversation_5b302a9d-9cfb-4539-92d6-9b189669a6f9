package com.paytm.aggregatorgateway.service.helper;

import com.paytm.aggregatorgateway.constants.UPSIntegrationConstants;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

class UPSServiceHelperTest {

    @InjectMocks
    private UPSServiceHelper upsServiceHelper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put(AWSSecrets.UPS_CLIENT_ID.getValue(), "clientId");
        AWSSecretManager.awsSecretsMap.put(AWSSecrets.UPS_SECRET_KEY.getValue(), "jwtTokenSecretKey");
    }

    @Test
    void testGenerateHash() throws UMPIntegrationException {
        String data = "test";
        String hash = upsServiceHelper.generateHash(data);
        assertNotNull(hash);
    }

    @Test
    void testGenerateJwtToken() throws UMPIntegrationException {
        String clientId = "clientId";
        String jwtTokenSecretKey = "jwtTokenSecretKey";
        HttpMethod httpMethod = HttpMethod.GET;
        String requestBody = "requestBody";

        String jwtToken = upsServiceHelper.generateJwtToken(clientId, jwtTokenSecretKey, httpMethod, requestBody);
        assertNotNull(jwtToken);
    }

    @Test
    void testGetHeaders() throws UMPIntegrationException {
        HttpMethod httpMethod = HttpMethod.GET;
        String requestBody = "requestBody";

        HttpHeaders headers = upsServiceHelper.getHeaders(httpMethod, requestBody);

        assertTrue(headers.containsKey(UPSIntegrationConstants.JWT_TOKEN));
        assertTrue(headers.containsKey(UPSIntegrationConstants.CONTENT_TYPE));
        assertTrue(headers.containsKey(UPSIntegrationConstants.REQUEST_ID));
    }
}