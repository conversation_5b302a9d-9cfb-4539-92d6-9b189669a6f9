package com.paytm.aggregatorgateway.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateNFCStatus;
import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateStatus;
import com.paytm.aggregatorgateway.service.helper.UPSServiceHelper;
import com.paytm.aggregatorgateway.service.impl.UPSServiceImpl;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.FAILURE;
import static com.paytm.aggregatorgateway.constants.UPSIntegrationConstants.SUCCESS;
import static com.paytm.aggregatorgateway.constants.UPSUpdateConstants.SECURITY_SHIELD;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UPSServiceImplTest {

    @InjectMocks
    UPSServiceImpl upsService;

    @Mock
    RestProcessorDelegate restProcessorDelegate;

    @Mock
    Environment environment;

    @Mock
    ObjectMapper jsonMapper;

    @Mock
    UPSServiceHelper upsServiceHelper;

    @BeforeEach
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(upsService, "jsonMapper", new ObjectMapper());
    }

    @Test
    public void updateNFCStatusTest1() throws Exception {
        ReqParamsToUpdateNFCStatus obj = getReqParamsToUpdateNFCStatus();

        when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        //when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");

        ResponseUmp ans = upsService.updateNFCStatus(obj);
        assertNotNull(ans);
        assertEquals(SUCCESS, ans.getStatus());
        assertEquals("200", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    private static ReqParamsToUpdateNFCStatus getReqParamsToUpdateNFCStatus() {
        ReqParamsToUpdateNFCStatus obj = new ReqParamsToUpdateNFCStatus();
        obj.setEntityId("123");
        obj.setEntityType("merchant");
        obj.setVersion(1L);
        obj.setPreferenceKey("abc");
        ReqParamsToUpdateNFCStatus.PreferenceValue preferenceValue = new ReqParamsToUpdateNFCStatus.PreferenceValue();
        preferenceValue.setCustId("001");
        preferenceValue.setDeviceId("007");
        preferenceValue.setNfcEnabled("yes");
        preferenceValue.setIsSubuser("true");
        List<ReqParamsToUpdateNFCStatus.PreferenceValue> list = new ArrayList<>();
        list.add(preferenceValue);
        obj.setPreferenceValue(list);
        return obj;
    }

    @Test
    public void updateNFCStatusTest2() throws Exception {
        ReqParamsToUpdateNFCStatus obj = getReqParamsToUpdateNFCStatus();
        obj.setEntityId(null);

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateNFCStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateNFCStatusTest3() throws Exception {
        ReqParamsToUpdateNFCStatus obj = getReqParamsToUpdateNFCStatus();
        obj.setEntityType(null);

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateNFCStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateNFCStatusTest4() throws Exception {
        ReqParamsToUpdateNFCStatus obj = getReqParamsToUpdateNFCStatus();
        obj.setPreferenceKey(null);

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateNFCStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateNFCStatusTest5() throws Exception {
        ReqParamsToUpdateNFCStatus obj = new ReqParamsToUpdateNFCStatus();
        obj.setEntityId("123");
        obj.setEntityType("merchant");
        obj.setVersion(1L);
        obj.setPreferenceKey("abc");
        obj.setPreferenceValue(null);

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateNFCStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    //@Test(expected = Test.None.class) //ToDo: check why this is failing
    public void updateNFCStatusExceptionTest() throws Exception {
        ReqParamsToUpdateNFCStatus obj = getReqParamsToUpdateNFCStatus();
        when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        upsService.updateNFCStatus(obj);
    }

    //@Test(expected = Test.None.class) //ToDo: check why this is failing
    public void updateNFCStatusNullTest() throws Exception {
        when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        upsService.updateNFCStatus(null);
    }

    @Test
    public void updateStatusTest1() throws Exception {
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityId("123");
        obj.setEntityType("merchant");
        obj.setVersion(1L);
        obj.setPreferenceKey("abc");
        obj.setPreferenceValue(getPreferenceValue());

        when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        //when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");

        ResponseUmp ans = upsService.updateStatus(obj);
        assertNotNull(ans);
        assertEquals(SUCCESS, ans.getStatus());
        assertEquals("200", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateStatusTest2() throws Exception {
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityType("merchant");
        obj.setVersion(1L);
        obj.setPreferenceKey("abc");
        obj.setPreferenceValue(getPreferenceValue());

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateStatusTest3() throws Exception {
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityId("123");
        obj.setVersion(1L);
        obj.setPreferenceKey("abc");
        obj.setPreferenceValue(getPreferenceValue());

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateStatusTest4() throws Exception {
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityId("123");
        obj.setPreferenceKey("abc");
        obj.setPreferenceValue(getPreferenceValue());

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateStatusTest5() throws Exception {
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityId("123");
        obj.setVersion(1L);
        obj.setPreferenceKey("abc");
        obj.setPreferenceValue(getPreferenceValue());

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateStatusTest6() throws Exception {
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityId("123");
        obj.setEntityType("merchant");
        obj.setVersion(1L);
        obj.setPreferenceValue(getPreferenceValue());

        /*when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<String>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");*/

        ResponseUmp ans = upsService.updateStatus(obj);
        assertNotNull(ans);
        assertEquals(FAILURE, ans.getStatus());
        assertEquals("UMP-601", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void updateStatusTest7() throws Exception {
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityId("123");
        obj.setEntityType("merchant");
        obj.setVersion(1L);
        obj.setPreferenceKey(SECURITY_SHIELD);
        obj.setPreferenceValue(getPreferenceValue());

        when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("{\n" +
                "    \"requestId\":\"123\",\n" +
                "    \"statusInfo\": {\n" +
                "        \"status\":\"SUCCESS\",\n" +
                "        \"statusCode\":\"200\",\n" +
                "        \"statusMessage\":\"123\"\n" +
                "        \n" +
                "    },\n" +
                "    \"response\":[\n" +
                "        {\n" +
                "            \"entityId\":\"123\",\n" +
                "            \"entityType\":\"123\",\n" +
                "            \"preferences\":[\n" +
                "                {\n" +
                "                    \"key\":\"001\",\n" +
                "                    \"version\":9223372036854775807,\n" +
                "                    \"value\":[\n" +
                "                        {\n" +
                "                            \"custId\":\"2021\",\n" +
                "                            \"deviceId\":\"2020\",\n" +
                "                            \"isSubuser\":\"yes\",\n" +
                "                            \"nfcEnabled\":\"true\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                    \n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}", HttpStatus.OK));
        //when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");

        ResponseUmp ans = upsService.updateStatus(obj);
        assertNotNull(ans);
        assertEquals(SUCCESS, ans.getStatus());
        assertEquals("200", ans.getStatusCode());
        assertNotNull(ans.getStatusMessage());
        assertNull(ans.getResults());
    }

    @Test
    public void getDevicePreferencesTest() throws Exception {
        String mid = "mid";
        ReqParamsToUpdateStatus obj = new ReqParamsToUpdateStatus();
        obj.setEntityId("123");
        obj.setEntityType("merchant");
        obj.setVersion(1L);
        obj.setPreferenceKey("abc");
        obj.setPreferenceValue(getPreferenceValue());

        when(restProcessorDelegate.executeUPSRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("{\n" +
                "  \"requestId\": \"123\",\n" +
                "  \"statusInfo\": {\n" +
                "    \"status\": \"SUCCESS\",\n" +
                "    \"statusCode\": \"200\",\n" +
                "    \"statusMessage\": \"123\"\n" +
                "  },\n" +
                "  \"response\": [\n" +
                "    {\n" +
                "      \"entityId\": \"123\",\n" +
                "      \"entityType\": \"123\",\n" +
                "      \"preferences\": [\n" +
                "        {\n" +
                "          \"key\": \"001\",\n" +
                "          \"version\": 9223372036854776000,\n" +
                "          \"value\": [\n" +
                "            true\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK));
        //when(environment.getRequiredProperty(anyString())).thenReturn("baseUrl");

        Map<String, Boolean> ans = upsService.getDevicePreferences(mid);
        assertNotNull(ans);
        assertTrue(ans.containsKey("001"));
        assertNotNull(ans.get("001"));
    }

    private List<ReqParamsToUpdateStatus.PreferenceValue> getPreferenceValue() {
        ReqParamsToUpdateStatus.PreferenceValue preferenceValue = new ReqParamsToUpdateStatus.PreferenceValue();
        preferenceValue.setCustId("001");
        preferenceValue.setDeviceId("007");
        preferenceValue.setNfcEnabled("yes");
        preferenceValue.setIsSubuser("true");
        List<ReqParamsToUpdateStatus.PreferenceValue> list = new ArrayList<>();
        list.add(preferenceValue);
        return list;
    }
}
