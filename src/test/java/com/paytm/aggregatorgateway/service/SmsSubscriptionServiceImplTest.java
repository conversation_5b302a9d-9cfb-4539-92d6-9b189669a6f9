package com.paytm.aggregatorgateway.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.dto.*;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.service.impl.SmsSubscriptionServiceImpl;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.metrics.MetricUtils;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class SmsSubscriptionServiceImplTest {

    @Mock
    private Environment env;

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @Mock
    private Authentication authentication;

    @Mock
    private MetricUtils metricUtils;

    @Mock
    private MerchantProfileService merchantProfileService;

    @Mock
    private SubscriptionService subscriptionService;

    @InjectMocks
    private SmsSubscriptionServiceImpl smsSubscriptionService;

    @BeforeEach
    public void init() throws Exception
    {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put("subscription.rental.jwt.secret", "80879ypiyuyidnijlkjnDo2780hILxdvvQXu9sh");
        AWSSecretManager.awsSecretsMap.put("cleverTap.accountId","mockAccountId");
        AWSSecretManager.awsSecretsMap.put("cleverTap.passCode","mockPassCode");
        ReflectionTestUtils.setField(smsSubscriptionService,"context","override~paytm~315642");
        ReflectionTestUtils.setField(smsSubscriptionService,"segmentId","975785");
    }


    @Test
    public void createSubscriptionTest() throws Exception
    {
      when(env.getRequiredProperty(anyString())).thenReturn("123");
        SmsSubscriptionDTO smsSubscriptionDTO=mock(SmsSubscriptionDTO.class);
        when(smsSubscriptionDTO.getPhoneNumber()).thenReturn("*********");
        when(smsSubscriptionDTO.getSecurityDeposit()).thenReturn("345");
        when(smsSubscriptionDTO.getDeductionStartDate()).thenReturn("22/12/2020");
        when(smsSubscriptionDTO.getEndDate()).thenReturn("22/12/2020");
        when(smsSubscriptionDTO.getUserSubscriptionMetadata()).thenReturn(new HashMap<>());
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("123",HttpStatus.OK));
        String response=smsSubscriptionService.createSubscription(smsSubscriptionDTO);
        assertTrue(response.equals("123"));
    }

    @Test
    public void updateSubscriptionStatusTest() throws Exception
    {
        when(env.getRequiredProperty(anyString())).thenReturn("123");
        UpdateSubscriptionStatusDTO updateStatusDTO=new UpdateSubscriptionStatusDTO();
        updateStatusDTO.setStatus("RESUME");
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(anyString(),any(),any(),any(),any(),eq(String.class))).thenReturn(new ResponseEntity<>("123",HttpStatus.OK));
        String response=smsSubscriptionService.updateSubscriptionStatus(updateStatusDTO,false,"androidapp");
        assertTrue(response.equals("123"));
    }

    @Test
    public void fetchIdFromCleverTapTestFailure() throws Exception{
        mockUserAuthentication(authentication);
        when(env.getRequiredProperty(anyString())).thenReturn("mockProperty");
        when(restProcessorDelegate.executeClevertapRequestHystrix(anyString(), anyString(),any(),any(HttpHeaders.class),any(),any(Class.class)))
        .thenReturn(new ResponseEntity<>("",HttpStatus.BAD_REQUEST));
        String response=smsSubscriptionService.fetchIdFromCleverTap("","","");
        assertNull(response);
    }

    @Test
    public void fetchIdFromCleverTapTestFailure2() throws Exception{
        mockUserAuthentication(authentication);
        String mockResponse = "{\n" +
                "  \"error_code\": 1104,\n" +
                "  \"retry\": false,\n" +
                "  \"error\": \"Notargetsfound\",\n" +
                "  \"status\": \"fail\"\n" +
                "}";
        when(env.getRequiredProperty(anyString())).thenReturn("mockProperty");
        when(restProcessorDelegate.executeClevertapRequestHystrix(anyString(), anyString(),any(),any(HttpHeaders.class),any(),any(Class.class)))
        .thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String response=smsSubscriptionService.fetchIdFromCleverTap("","","");
        assertNull(response);
    }

    @Test
    public void fetchIdFromCleverTapTestSuccess() throws Exception{
        mockUserAuthentication(authentication);
        String mockResponse = "{\n" +
                "  \"adUnit_notifs\": {\n" +
                "    \"override~paytm~315642\": [\n" +
                "      {\n" +
                "        \"type\": \"custom-key-value\",\n" +
                "        \"bg\": \"#ffffff\",\n" +
                "        \"custom_kv\": {\n" +
                "          \"bannerId\": \"889812\",\n" +
                "          \"slotId\": \"1\",\n" +
                "          \"priority\": \"100\"\n" +
                "        },\n" +
                "        \"wzrk_info\": {\n" +
                "          \"j\": 296155594,\n" +
                "          \"wzrk_id\": \"1684121833_20230607\",\n" +
                "          \"wzrk_pivot\": \"Variant A\",\n" +
                "          \"subContext\": \"default\"\n" +
                "        },\n" +
                "        \"subContext\": \"default\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"reminder_notifs\": {\n" +
                "    \"override~paytm~315642\": [\n" +
                "      \n" +
                "    ]\n" +
                "  },\n" +
                "  \"errors\": {\n" +
                "    \"adUnit_notifs\": {\n" +
                "      \"override~paytm~315642\": [\n" +
                "        \n" +
                "      ]\n" +
                "    },\n" +
                "    \"reminder_notifs\": {\n" +
                "      \"override~paytm~315642\": [\n" +
                "        \n" +
                "      ]\n" +
                "    },\n" +
                "    \"invalid_contexts\": {\n" +
                "      \n" +
                "    }\n" +
                "  },\n" +
                "  \"debug\": {\n" +
                "    \"evaluation_failed\": {\n" +
                "      \n" +
                "    }\n" +
                "  },\n" +
                "  \"status\": \"success\"\n" +
                "}";
        when(env.getRequiredProperty(anyString())).thenReturn("mockProperty");
        when(restProcessorDelegate.executeClevertapRequestHystrix(anyString(), anyString(),any(),any(HttpHeaders.class),any(),any(Class.class)))
                .thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String response=smsSubscriptionService.fetchIdFromCleverTap("","","889812");
        assertEquals("889812", response);
    }

    @Test
    public void checkAndUpdateSubscriptionFlowShouldUpdateWithResumeFlowSuccessfully() throws Exception {
        mockUserAuthentication(authentication);

        Map<String, Object> notifications = new HashMap<>();
        Map<String, Object> updatedTransaction = new HashMap<>();
        updatedTransaction.put("smsAllowed", Boolean.TRUE);
        notifications.put("transaction", updatedTransaction);

        Map<String, Object> updatedRefund = new HashMap<>();
        updatedRefund.put("smsAllowed", Boolean.TRUE);
        notifications.put("refund", updatedRefund);

        Map<String, Object> alertsStatusOnBoss = new HashMap<>();
        alertsStatusOnBoss.put("notifications", notifications);

        UpdateSubscriptionStatusDTO updateStatusDTO = new UpdateSubscriptionStatusDTO();
        updateStatusDTO.setStatus("RESUME");

        when(merchantProfileService.getCommunicationConfiguration(any(), any(), any())).thenReturn(alertsStatusOnBoss);

        assertThrows(IllegalArgumentException.class, () ->
                smsSubscriptionService.checkAndUpdateSubscriptionFlow(updateStatusDTO, "client"));

    }

    @Test
    public void checkAndUpdateSubscriptionFlowShouldUpdateWithResumeFlowSuccessfully2() throws Exception {
        mockUserAuthentication(authentication);

        Map<String, Object> notifications = new HashMap<>();
        Map<String, Object> updatedTransaction = new HashMap<>();
        updatedTransaction.put("smsAllowed", Boolean.TRUE);
        notifications.put("transaction", updatedTransaction);

        Map<String, Object> updatedRefund = new HashMap<>();
        updatedRefund.put("smsAllowed", Boolean.TRUE);
        notifications.put("refund", updatedRefund);

        Map<String, Object> alertsStatusOnBoss = new HashMap<>();
        alertsStatusOnBoss.put("notifications", notifications);

        UpdateSubscriptionStatusDTO updateStatusDTO = new UpdateSubscriptionStatusDTO();
        updateStatusDTO.setStatus("RESUME");
        updateStatusDTO.setMid("mid");
        updateStatusDTO.setUsn("usn");
        updateStatusDTO.setServiceName("name");
        updateStatusDTO.setSubscriptionType("type");
        updateStatusDTO.setCustId("type");

        when(merchantProfileService.getCommunicationConfiguration(any(), any(), any())).thenReturn(alertsStatusOnBoss);

        String mockResponse1 = "{\n" +
                "  \"status\": \"Success\",\n" +
                "  \"statusCode\": \"200\",\n" +
                "  \"results\": {\n" +
                "    \"subscriptions\": [\n" +
                "      {\n" +
                "        \"id\": 14,\n" +
                "        \"custId\": 12421,\n" +
                "        \"mid\": \"BCFD7HD7H38R34RU39\",\n" +
                "        \"currentDueAmt\": 423,\n" +
                "        \"nextDueDate\": \"2019-12-01\",\n" +
                "        \"securityDeposit\": 299,\n" +
                "        \"serviceType\": \"Android\",\n" +
                "        \"serviceName\": \"EDC\",\n" +
                "        \"description\": null,\n" +
                "        \"planPrice\": 399,\n" +
                "        \"usn\": \"WERTY17483\",\n" +
                "        \"frequency\": \"1m\",\n" +
                "        \"onboardDate\": \"2019-10-21\",\n" +
                "        \"cycleEndDate\": \"2021-11-30\",\n" +
                "        \"endDate\": \"2020-02-01\",\n" +
                "        \"status\": \"SUSPEND\",\n" +
                "        \"subscriptionType\": \"RENTAL\",\n" +
                "        \"otherCharges\": null,\n" +
                "        \"conditional\": false,\n" +
                "        \"planType\": \"STATIC\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
        ObjectMapper jacksonObjectMapper = new ObjectMapper();
        Map<String, Object> responseMap = jacksonObjectMapper.readValue(mockResponse1, Map.class);
        when(subscriptionService.fetchSubscription(anyString(), anyString(), anyString(), anyString(), any(), anyString())).thenReturn(responseMap);

        String mockResponse2 = "{\n" +
                "\"status\": \"Success\",\n" +
                "\"statusCode\": \"200\",\n" +
                "\"results\": \"User Subscription Updated Successfully\"\n" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(mockResponse2, HttpStatus.OK);
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(anyString(), anyString(), any(), any(), any(), any(Class.class))).thenReturn(responseEntity);

        String res = smsSubscriptionService.checkAndUpdateSubscriptionFlow(updateStatusDTO, "client");
        assertNotNull(res);

    }

    @Test
    public void checkAndUpdateSubscriptionFlowShouldUpdateWithSuspendFlowSuccessfully3() throws Exception {
        mockUserAuthentication(authentication);

        Map<String, Object> notifications = new HashMap<>();
        Map<String, Object> updatedTransaction = new HashMap<>();
        updatedTransaction.put("smsAllowed", Boolean.TRUE);
        notifications.put("transaction", updatedTransaction);

        Map<String, Object> updatedRefund = new HashMap<>();
        updatedRefund.put("smsAllowed", Boolean.TRUE);
        notifications.put("refund", updatedRefund);

        Map<String, Object> alertsStatusOnBoss = new HashMap<>();
        alertsStatusOnBoss.put("notifications", notifications);

        UpdateSubscriptionStatusDTO updateStatusDTO = new UpdateSubscriptionStatusDTO();
        updateStatusDTO.setStatus("SUSPEND");
        updateStatusDTO.setMid("mid");
        updateStatusDTO.setUsn("usn");
        updateStatusDTO.setServiceName("name");
        updateStatusDTO.setSubscriptionType("type");
        updateStatusDTO.setCustId("type");

        when(merchantProfileService.getCommunicationConfiguration(any(), any(), any())).thenReturn(alertsStatusOnBoss);

        String mockResponse1 = "{\n" +
                "  \"status\": \"Success\",\n" +
                "  \"statusCode\": \"200\",\n" +
                "  \"results\": {\n" +
                "    \"subscriptions\": [\n" +
                "      {\n" +
                "        \"id\": 14,\n" +
                "        \"custId\": 12421,\n" +
                "        \"mid\": \"BCFD7HD7H38R34RU39\",\n" +
                "        \"currentDueAmt\": 423,\n" +
                "        \"nextDueDate\": \"2019-12-01\",\n" +
                "        \"securityDeposit\": 299,\n" +
                "        \"serviceType\": \"Android\",\n" +
                "        \"serviceName\": \"EDC\",\n" +
                "        \"description\": null,\n" +
                "        \"planPrice\": 399,\n" +
                "        \"usn\": \"WERTY17483\",\n" +
                "        \"frequency\": \"1m\",\n" +
                "        \"onboardDate\": \"2019-10-21\",\n" +
                "        \"cycleEndDate\": \"2021-11-30\",\n" +
                "        \"endDate\": \"2020-02-01\",\n" +
                "        \"status\": \"SUSPEND\",\n" +
                "        \"subscriptionType\": \"RENTAL\",\n" +
                "        \"otherCharges\": null,\n" +
                "        \"conditional\": false,\n" +
                "        \"planType\": \"STATIC\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
        ObjectMapper jacksonObjectMapper = new ObjectMapper();
        Map<String, Object> responseMap = jacksonObjectMapper.readValue(mockResponse1, Map.class);
        when(subscriptionService.fetchSubscription(anyString(), anyString(), anyString(), anyString(), any(), anyString())).thenReturn(responseMap);

        String mockResponse2 = "{\n" +
                "\"status\": \"Success\",\n" +
                "\"statusCode\": \"200\",\n" +
                "\"results\": \"User Subscription Updated Successfully\"\n" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(mockResponse2, HttpStatus.OK);
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(anyString(), anyString(), any(), any(), any(), any(Class.class))).thenReturn(responseEntity);

        String res = smsSubscriptionService.checkAndUpdateSubscriptionFlow(updateStatusDTO, "client");
        assertNotNull(res);

    }

    @Test
    public void checkAndUpdateSubscriptionFlowShouldUpdateWithResumeFlowSuccessfully4() throws Exception {
        mockUserAuthentication(authentication);

        Map<String, Object> notifications = new HashMap<>();
        Map<String, Object> updatedTransaction = new HashMap<>();
        updatedTransaction.put("smsAllowed", Boolean.TRUE);
        notifications.put("transaction", updatedTransaction);

        Map<String, Object> updatedRefund = new HashMap<>();
        updatedRefund.put("smsAllowed", Boolean.FALSE);
        notifications.put("refund", updatedRefund);

        Map<String, Object> alertsStatusOnBoss = new HashMap<>();
        alertsStatusOnBoss.put("notifications", notifications);

        UpdateSubscriptionStatusDTO updateStatusDTO = new UpdateSubscriptionStatusDTO();
        updateStatusDTO.setStatus("RESUME");
        updateStatusDTO.setMid("mid");
        updateStatusDTO.setUsn("usn");
        updateStatusDTO.setServiceName("name");
        updateStatusDTO.setSubscriptionType("type");
        updateStatusDTO.setCustId("type");

        when(merchantProfileService.getCommunicationConfiguration(any(), any(), any())).thenReturn(alertsStatusOnBoss);

        String mockResponse1 = "{\n" +
                "\"status\": \"FAILURE\",\n" +
                "\"statusCode\": \"204\",\n" +
                "\"statusMessage\": \"No data found.\"\n" +
                "}";
        ObjectMapper jacksonObjectMapper = new ObjectMapper();
        Map<String, Object> responseMap = jacksonObjectMapper.readValue(mockResponse1, Map.class);
        when(subscriptionService.fetchSubscription(anyString(), anyString(), anyString(), anyString(), any(), anyString())).thenReturn(responseMap);

        String mockResponse2 = "{\n" +
                "\"status\": \"Success\",\n" +
                "\"statusCode\": \"200\",\n" +
                "\"results\": \"User Subscription Updated Successfully\"\n" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(mockResponse2, HttpStatus.OK);
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(anyString(), anyString(), any(), any(), any(), any(Class.class))).thenReturn(responseEntity);

        ResponseEntity<String> responseEntity2 = new ResponseEntity<>(getCleverTapMockResponse(), HttpStatus.OK);
        when(restProcessorDelegate.executeClevertapRequestHystrix(anyString(), anyString(), any(), any(), any(), any(Class.class))).thenReturn(responseEntity2);

        String res = smsSubscriptionService.checkAndUpdateSubscriptionFlow(updateStatusDTO, "client");
        assertNotNull(res);

    }

    @Test
    public void checkAndUpdateSubscriptionFlowShouldUpdateWithResumeFlowSuccessfully5() throws Exception {
        mockUserAuthentication(authentication);

        Map<String, Object> notifications = new HashMap<>();
        Map<String, Object> updatedTransaction = new HashMap<>();
        updatedTransaction.put("smsAllowed", Boolean.TRUE);
        notifications.put("transaction", updatedTransaction);

        Map<String, Object> updatedRefund = new HashMap<>();
        updatedRefund.put("smsAllowed", Boolean.FALSE);
        notifications.put("refund", updatedRefund);

        Map<String, Object> alertsStatusOnBoss = new HashMap<>();
        alertsStatusOnBoss.put("notifications", notifications);

        UpdateSubscriptionStatusDTO updateStatusDTO = new UpdateSubscriptionStatusDTO();
        updateStatusDTO.setStatus("RESUME");
        updateStatusDTO.setMid("mid");
        updateStatusDTO.setUsn("usn");
        updateStatusDTO.setServiceName("name");
        updateStatusDTO.setSubscriptionType("type");
        updateStatusDTO.setCustId("type");

        when(merchantProfileService.getCommunicationConfiguration(any(), any(), any())).thenReturn(alertsStatusOnBoss);

        String mockResponse1 = "{\n" +
                "\"status\": \"FAILURE\",\n" +
                "\"statusCode\": \"204\",\n" +
                "\"statusMessage\": \"No data found.\"\n" +
                "}";
        ObjectMapper jacksonObjectMapper = new ObjectMapper();
        Map<String, Object> responseMap = jacksonObjectMapper.readValue(mockResponse1, Map.class);
        when(subscriptionService.fetchSubscription(anyString(), anyString(), anyString(), anyString(), any(), anyString())).thenReturn(responseMap);

        String mockResponse2 = "{\n" +
                "\"status\": \"Success\",\n" +
                "\"statusCode\": \"200\",\n" +
                "\"results\": \"User Subscription Updated Successfully\"\n" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(mockResponse2, HttpStatus.OK);
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(anyString(), anyString(), any(), any(), any(), any(Class.class))).thenReturn(responseEntity);

        String mockResponse3 = "{\n" +
                "  \"error_code\": 1104,\n" +
                "  \"retry\": false,\n" +
                "  \"error\": \"No targets found\",\n" +
                "  \"status\": \"fail\"\n" +
                "}";
        ResponseEntity<String> responseEntity2 = new ResponseEntity<>(mockResponse3, HttpStatus.OK);
        when(restProcessorDelegate.executeClevertapRequestHystrix(anyString(), anyString(), any(), any(), any(), any(Class.class))).thenReturn(responseEntity2);

        String res = smsSubscriptionService.checkAndUpdateSubscriptionFlow(updateStatusDTO, "client");
        assertNotNull(res);

    }

    private String getCleverTapMockResponse() {
        return "{\n" +
                "  \"adUnit_notifs\": {\n" +
                "    \"override~paytm~315642\": [\n" +
                "      {\n" +
                "        \"type\": \"custom-key-value\",\n" +
                "        \"bg\": \"#ffffff\",\n" +
                "        \"custom_kv\": {\n" +
                "          \"bannerId\": \"975785\",\n" +
                "          \"slotId\": \"1\",\n" +
                "          \"priority\": \"100\"\n" +
                "        },\n" +
                "        \"wzrk_info\": {\n" +
                "          \"j\": 294847443,\n" +
                "          \"wzrk_id\": \"1689168494_20440628\",\n" +
                "          \"wzrk_pivot\": \"Variant A\",\n" +
                "          \"subContext\": \"default\"\n" +
                "        },\n" +
                "        \"subContext\": \"default\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"reminder_notifs\": {\n" +
                "    \"override~paytm~315642\": [\n" +
                "      \n" +
                "    ]\n" +
                "  },\n" +
                "  \"errors\": {\n" +
                "    \"adUnit_notifs\": {\n" +
                "      \"override~paytm~315642\": [\n" +
                "        \n" +
                "      ]\n" +
                "    },\n" +
                "    \"reminder_notifs\": {\n" +
                "      \"override~paytm~315642\": [\n" +
                "        \n" +
                "      ]\n" +
                "    },\n" +
                "    \"invalid_contexts\": {\n" +
                "      \n" +
                "    },\n" +
                "    \"context_limit_reached\": [\n" +
                "      \n" +
                "    ]\n" +
                "  },\n" +
                "  \"debug\": {\n" +
                "    \"evaluation_failed\": {\n" +
                "      \n" +
                "    }\n" +
                "  },\n" +
                "  \"status\": \"success\"\n" +
                "}";
    }

}
