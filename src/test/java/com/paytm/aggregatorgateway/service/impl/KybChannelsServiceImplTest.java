package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class KybChannelsServiceImplTest {

    @Mock
	private RestProcessorDelegate restProcessorDelegate;

	@InjectMocks
	private KybChannelsServiceImpl kybChannelsServiceImpl;

	private Authentication authentication;

	@BeforeEach
	public void init() {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		ReflectionTestUtils.setField(kybChannelsServiceImpl, "baseUrl", "mockBaseUrl");
	}

	@Test
	public void testFetchChannels1() throws Exception {
		// kybId blank or null

        when(restProcessorDelegate.executeDigitalProxyRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.OK));

        ResponseUmp response = kybChannelsServiceImpl.fetchChannels(null);
		assertEquals("FAILURE", response.getStatus());
		assertEquals("KybId not associated with this merchant", response.getStatusMessage());
	}

	@Test
	public void testFetchChannels2() throws Exception {
		// failure from downstream

        when(restProcessorDelegate.executeDigitalProxyRequestHystrix(anyString(), anyString(), anyMap(), any(), any(), any())).thenReturn(new ResponseEntity<>("", HttpStatus.BAD_REQUEST));

        ResponseUmp response = kybChannelsServiceImpl.fetchChannels("kybId123");
		assertNotNull(response);
	}

	@Test
	public void testFetchChannel3() throws Exception {
		// success from downstream

		when(restProcessorDelegate.executeDigitalProxyRequestHystrix(anyString(), anyString(), anyMap(), any(), any(), any())).thenReturn(new ResponseEntity<>(getMockResponseForFetchChannels(), HttpStatus.OK));

        ResponseUmp response = kybChannelsServiceImpl.fetchChannels("kybId123");
		assertEquals("SUCCESS", response.getStatus());
	}

    private String getMockResponseForFetchChannels() {
		return "{\n" +
                "  \"channelList\": {\n" +
                "    \"kybid123\": [\n" +
                "      \n" +
                "    ]\n" +
                "  },\n" +
                "  \"userDetails\": {\n" +
                "    \"194919989\": [\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }

}
