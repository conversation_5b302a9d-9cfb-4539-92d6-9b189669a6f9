package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class MaquetteServiceImplTest {

	@Mock
	private RestProcessorDelegate restProcessorDelegate;

	@Mock
	private ObjectMapper objectMapper;

	@Mock
	private Environment commonProperties;

	@InjectMocks
	private MaquetteServiceImpl maquetteServiceImpl;

	private Authentication authentication;

	@BeforeEach
	public void init() {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		ReflectionTestUtils.setField(maquetteServiceImpl, "objectMapper", new ObjectMapper());
	}

	@Test
	public void testVerifyLatLongSuccess() throws InterruptedException {
		UtsHelper.mockUserAuthentication(authentication);
		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("latitude", 37.7749);
		requestBody.put("longitude", -122.4194);

		String maquetteResponse = "{\n" +
				"  \"session_id\": \"0a42eb15165528267989786044810\",\n" +
				"  \"status\": \"SUCCESS\",\n" +
				"  \"action_recommended\": \"PASS\",\n" +
				"  \"action_recommended_type\": \"ActionCode\",\n" +
				"  \"actions\": null,\n" +
				"  \"reason\": null,\n" +
				"  \"messages\": {\n" +
				"    \"user\": null,\n" +
				"    \"cst\": []\n" +
				"  },\n" +
				"  \"extra_options\": {},\n" +
				"  \"decision_impacting_timeout\": false\n" +
				"}";

		when(commonProperties.getRequiredProperty(anyString())).thenReturn("commonProperties");
		when(restProcessorDelegate.executeMaquetteRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(maquetteResponse, HttpStatus.OK));

		ResponseUmp response = maquetteServiceImpl.verifyLatLong(requestBody);

		assertEquals("Success", response.getStatus());
		assertEquals("200", response.getStatusCode());
		assertEquals("Successfully fetched verification status", response.getStatusMessage());
		assertNotNull(response.getResults());
	}

	@Test
	public void testVerifyLatLongFailure() throws InterruptedException {
		UtsHelper.mockUserAuthentication(authentication);
		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("latitude", 37.7749);
		requestBody.put("longitude", -122.4194);

		String maquetteResponse = "{\n" +
				"  \"session_id\": \"03f180f8-8509-4d1c-9c9a-11e2bda50dd5\",\n" +
				"  \"status\": \"SUCCESS\",\n" +
				"  \"action_recommended\": \"BLOCK\",\n" +
				"  \"action_recommended_type\": \"ActionCode\",\n" +
				"  \"messages\": {\n" +
				"    \"cst\": [\n" +
				"      \n" +
				"    ]\n" +
				"  },\n" +
				"  \"extra_options\": {\n" +
				"    \n" +
				"  },\n" +
				"  \"decision_impacting_timeout\": false\n" +
				"}";

		when(commonProperties.getRequiredProperty(anyString())).thenReturn("commonProperties");
		when(restProcessorDelegate.executeMaquetteRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(maquetteResponse, HttpStatus.BAD_REQUEST));

		try {
			maquetteServiceImpl.verifyLatLong(requestBody);
		} catch (Exception e) {
			assertEquals("Error occurred while fetching/parsing Maquette API response", e.getMessage());
		}
	}

}