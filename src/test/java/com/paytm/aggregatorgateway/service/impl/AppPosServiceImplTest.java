package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class AppPosServiceImplTest {

    Authentication authentication;

    @Mock
    Environment environment;

    @Mock
    RestProcessorDelegate restProcessorDelegate;

    @InjectMocks
    AppPosServiceImpl appPosService;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);

        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put("boss.client.key", "80879ypiyuyidnijlkjnDo2780hILxdvvQXu9sh");
        AWSSecretManager.awsSecretsMap.put("boss.client.id", "mockBossClientId");

    }

    @Test
    public void testCheckStatus() throws Exception {

        UtsHelper.mockUserAuthentication(authentication);

        Map<String, String> queryParamMap = new HashMap<>();
        queryParamMap.put("mid", "100");
        queryParamMap.put("deviceId", "111");
        queryParamMap.put("vendorName", "V1");

        when(environment.getRequiredProperty(anyString())).thenReturn("base");

        String mockResponse = "{\n" +
                "  \"statusCode\": \"200\"\n" +
                "}";

        when(restProcessorDelegate.executeBOSSRequestHystrix(anyString(), any(),any(),any(),any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        String actualResponse = appPosService.checkStatus(queryParamMap);

        assertEquals(mockResponse, actualResponse);

    }

}
