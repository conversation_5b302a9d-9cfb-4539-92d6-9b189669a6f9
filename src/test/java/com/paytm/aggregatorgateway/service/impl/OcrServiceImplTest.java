package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.dto.QualificationCallbackRequest;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.pgdashboard.commons.dto.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OcrServiceImplTest implements DomainConstants {

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private OcrServiceImpl ocrService;

    private User mockUser;
    private Merchant mockMerchant;
    private QualificationCallbackRequest successRequest;
    private QualificationCallbackRequest failureRequest;

    @BeforeEach
    void setUp() {
        // Set up test properties
        ReflectionTestUtils.setField(ocrService, "ocrClientID", "test-client-id");
        ReflectionTestUtils.setField(ocrService, "ocrBaseUrl", "https://test-ocr-url.com");
        ReflectionTestUtils.setField(ocrService, "bankProofCacheTtl", 3600L);
        ReflectionTestUtils.setField(ocrService, "umpBaseUrl", "https://test-app-url.com");

        // Mock AWS secrets
        Map<String, String> mockSecrets = new HashMap<>();
        mockSecrets.put("ocr.client.secret", "test-secret");
        AWSSecretManager.awsSecretsMap = mockSecrets;

        // Setup Redis template mock - moved to individual tests that need it

        // Setup mock user and merchant
        setupMockUserAndMerchant();
        setupTestRequests();
    }

    private void setupMockUserAndMerchant() {
        mockUser = new User();
        mockUser.setId("test-user-id");
        mockUser.setCurrentMerchant(123L);

        mockMerchant = new Merchant();
        mockMerchant.setId(123L);
        mockMerchant.setMid("test-mid");
        mockMerchant.setAdminUserId("test-admin-user-id");

        List<Merchant> merchants = Arrays.asList(mockMerchant);
        mockUser.setMerchants(merchants);
    }

    private void setupTestRequests() {
        // Success request
        successRequest = new QualificationCallbackRequest();
        successRequest.setStatus("SUCCESS");
        successRequest.setStatusCode("200");
        successRequest.setStatusMessage("OK");
        successRequest.setRequestId("test-request-id");

        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        response.setProcessedImageFileIds(Arrays.asList("DM1012489886194796"));

        Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("cancelledChequePhoto");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);

        QualificationCallbackRequest.ParameterValue liveliness = new QualificationCallbackRequest.ParameterValue();
        liveliness.setValue("original");
        liveliness.setConfidence(0.9999);
        params.setLiveliness(liveliness);

        QualificationCallbackRequest.ParameterValue completeness = new QualificationCallbackRequest.ParameterValue();
        completeness.setValue("complete_image");
        completeness.setConfidence(0.95);
        params.setCompleteness(completeness);

        QualificationCallbackRequest.ParameterValue valid = new QualificationCallbackRequest.ParameterValue();
        valid.setValue("valid_image");
        valid.setConfidence(0.999);
        params.setValid(valid);

        imageParams.put("******************", params);
        response.setImageWiseParameters(imageParams);
        successRequest.setResponse(response);

        // Failure request
        failureRequest = new QualificationCallbackRequest();
        failureRequest.setStatus("FAILED");
        failureRequest.setStatusCode("400");
        failureRequest.setStatusMessage("FAILED: Internal Server Error");
        failureRequest.setRequestId("test-request-id");
    }

    @Test
    void testQualificationCallBack_Success() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            securityUtilsMock.when(SecurityUtils::getLoggedInUser).thenReturn(mockUser);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");
            when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
            
            ResponseEntity<String> mockResponse = new ResponseEntity<>(
                "{\"status\":\"SUCCESS\",\"statusCode\":\"200\",\"statusMessage\":\"Request successful\",\"requestId\":\"test-request-id\"}", 
                HttpStatus.OK
            );
            when(restProcessorDelegate.executeOCRRequestHystrix(anyString(), anyString(), any(), any(), any(), eq(String.class)))
                .thenReturn(mockResponse);

            // Execute
            ResponseUmp result = ocrService.qualificationCallBack(successRequest);

            // Verify
            assertEquals("SUCCESS", result.getStatus());
            assertEquals("200", result.getStatusCode());
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
            verify(restProcessorDelegate).executeOCRRequestHystrix(anyString(), eq("POST"), any(), any(), any(), any());
        }
    }

    @Test
    void testQualificationCallBack_Failure() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");

            // Execute
            ResponseUmp result = ocrService.qualificationCallBack(failureRequest);

            // Verify
            assertEquals("SUCCESS", result.getStatus());
            assertEquals("200", result.getStatusCode());
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
            verify(restProcessorDelegate, never()).executeOCRRequestHystrix(anyString(), anyString(), any(), any(), any(), any());
        }
    }

    @Test
    void testQualificationCallBack_ValidationFailure() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");

            // Create request with invalid parameters
            QualificationCallbackRequest invalidRequest = new QualificationCallbackRequest();
            invalidRequest.setStatus("SUCCESS");
            invalidRequest.setStatusCode("200");
            invalidRequest.setStatusMessage("OK");
            invalidRequest.setRequestId("test-request-id");

            QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
            response.setProcessedImageFileIds(Arrays.asList("DM1012489886194796"));

            Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
            QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
            
            // Invalid document type
            QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
            docType.setValue("invalidType");
            docType.setConfidence(0.9999);
            params.setDocument_type(docType);

            imageParams.put("******************", params);
            response.setImageWiseParameters(imageParams);
            invalidRequest.setResponse(response);

            // Execute
            ResponseUmp result = ocrService.qualificationCallBack(invalidRequest);

            // Verify
            assertEquals("SUCCESS", result.getStatus());
            assertEquals("200", result.getStatusCode());
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
            verify(restProcessorDelegate, never()).executeOCRRequestHystrix(anyString(), anyString(), any(), any(), any(), any());
        }
    }

    @Test
    void testDeductionsCallBack_Success() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");
            when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());

            // Mock existing Redis data
            String existingRedisData = "{\"requestStatus\":\"DEDUCTION_AWAITING\",\"result\":{\"documentType\":\"cancelledChequePhoto\"}}";
            when(valueOperations.get(anyString())).thenReturn(existingRedisData);

            // Create success request
            Map<String, Object> request = new HashMap<>();
            request.put("status", "SUCCESS");
            request.put("statusCode", "200");
            request.put("statusMessage", "OK");
            request.put("requestId", "test-request-id");

            Map<String, Object> response = new HashMap<>();
            response.put("processedImageFileIds", Arrays.asList("DM143819942560"));

            Map<String, Object> imageWiseParameters = new HashMap<>();
            Map<String, Object> imageParams = new HashMap<>();
            imageParams.put("account_number", Map.of("value", "**************", "confidence", 0.9999));
            imageParams.put("ifsc_code", Map.of("value", "HDFC0000732", "confidence", 0.9999));
            imageParams.put("account_holder_name", Map.of("value", "RAJEEV KUMER GAUTAM", "confidence", 0.9999));
            imageWiseParameters.put("******************", imageParams);
            response.put("imageWiseParameters", imageWiseParameters);

            request.put("response", response);

            // Execute
            ResponseUmp result = ocrService.deductionsCallBack(request);

            // Verify
            assertEquals("SUCCESS", result.getStatus());
            assertEquals("200", result.getStatusCode());
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
        }
    }

    @Test
    void testDeductionsCallBack_Failure() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");
            
            // Mock Redis data for getCurrentRedisState
            String redisData = "{\"requestStatus\":\"DEDUCTION_FAILED\",\"result\":{\"status\":\"FAILED\",\"statusCode\":\"402\",\"statusMessage\":\"FAILED: Invalid request data\"}}";
            when(valueOperations.get(anyString())).thenReturn(redisData);
            
            // Create proper response map for getCurrentRedisState
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("requestStatus", "DEDUCTION_FAILED");
            responseMap.put("result", Map.of("status", "FAILED", "statusCode", "402", "statusMessage", "FAILED: Invalid request data"));
            when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(responseMap);

            // Create failure request
            Map<String, Object> request = new HashMap<>();
            request.put("status", "FAILED");
            request.put("statusCode", "402");
            request.put("statusMessage", "FAILED: Invalid request data");
            request.put("requestId", "test-request-id");

            // Execute
            ResponseUmp result = ocrService.deductionsCallBack(request);

            // Verify
            assertEquals("SUCCESS", result.getStatus());
            assertEquals("200", result.getStatusCode());
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
        }
    }

    @Test
    void testValidateQualificationParameters_ValidParameters() throws Exception {
        // Execute - using reflection to access private method
        var method = OcrServiceImpl.class.getDeclaredMethod("validateQualificationParameters", QualificationCallbackRequest.class);
        method.setAccessible(true);
        var validationResult = method.invoke(ocrService, successRequest);

        // Verify
        assertNotNull(validationResult);
    }

    @Test
    void testValidateQualificationParameters_InvalidDocumentType() throws Exception {
        // Create request with invalid document type
        QualificationCallbackRequest invalidRequest = new QualificationCallbackRequest();
        invalidRequest.setStatus("SUCCESS");
        invalidRequest.setStatusCode("200");
        invalidRequest.setStatusMessage("OK");
        invalidRequest.setRequestId("test-request-id");

        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        response.setProcessedImageFileIds(Arrays.asList("DM1012489886194796"));

        Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("invalidType");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);

        imageParams.put("******************", params);
        response.setImageWiseParameters(imageParams);
        invalidRequest.setResponse(response);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("validateQualificationParameters", QualificationCallbackRequest.class);
        method.setAccessible(true);
        var validationResult = method.invoke(ocrService, invalidRequest);

        assertNotNull(validationResult);
    }

    @Test
    void testValidateQualificationParameters_InvalidLiveliness() throws Exception {
        // Create request with invalid liveliness
        QualificationCallbackRequest invalidRequest = new QualificationCallbackRequest();
        invalidRequest.setStatus("SUCCESS");
        invalidRequest.setStatusCode("200");
        invalidRequest.setStatusMessage("OK");
        invalidRequest.setRequestId("test-request-id");

        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        response.setProcessedImageFileIds(Arrays.asList("DM1012489886194796"));

        Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("cancelledChequePhoto");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);

        QualificationCallbackRequest.ParameterValue liveliness = new QualificationCallbackRequest.ParameterValue();
        liveliness.setValue("photo_of_photo"); // Invalid for cancelledChequePhoto
        liveliness.setConfidence(0.9999);
        params.setLiveliness(liveliness);

        imageParams.put("******************", params);
        response.setImageWiseParameters(imageParams);
        invalidRequest.setResponse(response);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("validateQualificationParameters", QualificationCallbackRequest.class);
        method.setAccessible(true);
        var validationResult = method.invoke(ocrService, invalidRequest);

        assertNotNull(validationResult);
    }

    @Test
    void testValidateQualificationParameters_InvalidCompleteness() throws Exception {
        // Create request with invalid completeness
        QualificationCallbackRequest invalidRequest = new QualificationCallbackRequest();
        invalidRequest.setStatus("SUCCESS");
        invalidRequest.setStatusCode("200");
        invalidRequest.setStatusMessage("OK");
        invalidRequest.setRequestId("test-request-id");

        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        response.setProcessedImageFileIds(Arrays.asList("DM1012489886194796"));

        Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("cancelledChequePhoto");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);

        QualificationCallbackRequest.ParameterValue liveliness = new QualificationCallbackRequest.ParameterValue();
        liveliness.setValue("original");
        liveliness.setConfidence(0.9999);
        params.setLiveliness(liveliness);

        QualificationCallbackRequest.ParameterValue completeness = new QualificationCallbackRequest.ParameterValue();
        completeness.setValue("cropped_image"); // Invalid
        completeness.setConfidence(0.95);
        params.setCompleteness(completeness);

        imageParams.put("******************", params);
        response.setImageWiseParameters(imageParams);
        invalidRequest.setResponse(response);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("validateQualificationParameters", QualificationCallbackRequest.class);
        method.setAccessible(true);
        var validationResult = method.invoke(ocrService, invalidRequest);

        assertNotNull(validationResult);
    }

    @Test
    void testValidateQualificationParameters_InvalidValid() throws Exception {
        // Create request with invalid valid
        QualificationCallbackRequest invalidRequest = new QualificationCallbackRequest();
        invalidRequest.setStatus("SUCCESS");
        invalidRequest.setStatusCode("200");
        invalidRequest.setStatusMessage("OK");
        invalidRequest.setRequestId("test-request-id");

        QualificationCallbackRequest.QualificationResponse response = new QualificationCallbackRequest.QualificationResponse();
        response.setProcessedImageFileIds(Arrays.asList("DM1012489886194796"));

        Map<String, QualificationCallbackRequest.ImageParameters> imageParams = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("cancelledChequePhoto");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);

        QualificationCallbackRequest.ParameterValue liveliness = new QualificationCallbackRequest.ParameterValue();
        liveliness.setValue("original");
        liveliness.setConfidence(0.9999);
        params.setLiveliness(liveliness);

        QualificationCallbackRequest.ParameterValue completeness = new QualificationCallbackRequest.ParameterValue();
        completeness.setValue("complete_image");
        completeness.setConfidence(0.95);
        params.setCompleteness(completeness);

        QualificationCallbackRequest.ParameterValue valid = new QualificationCallbackRequest.ParameterValue();
        valid.setValue("edited_image"); // Invalid
        valid.setConfidence(0.999);
        params.setValid(valid);

        imageParams.put("******************", params);
        response.setImageWiseParameters(imageParams);
        invalidRequest.setResponse(response);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("validateQualificationParameters", QualificationCallbackRequest.class);
        method.setAccessible(true);
        var validationResult = method.invoke(ocrService, invalidRequest);

        assertNotNull(validationResult);
    }

    @Test
    void testGetCustomerIdFromContext_WithUser() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getLoggedInUser).thenReturn(mockUser);
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);

            // Execute
            var method = OcrServiceImpl.class.getDeclaredMethod("getCustomerIdFromContext");
            method.setAccessible(true);
            String customerId = (String) method.invoke(ocrService);

            // Verify
            assertEquals("test-user-id", customerId);
        }
    }

    @Test
    void testGetCustomerIdFromContext_WithMerchant() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getLoggedInUser).thenReturn(null);
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);

            // Execute
            var method = OcrServiceImpl.class.getDeclaredMethod("getCustomerIdFromContext");
            method.setAccessible(true);
            String customerId = (String) method.invoke(ocrService);

            // Verify
            assertEquals("test-admin-user-id", customerId);
        }
    }

    @Test
    void testGetCustomerIdFromContext_Default() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks
            securityUtilsMock.when(SecurityUtils::getLoggedInUser).thenReturn(null);
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(null);

            // Execute
            var method = OcrServiceImpl.class.getDeclaredMethod("getCustomerIdFromContext");
            method.setAccessible(true);
            String customerId = (String) method.invoke(ocrService);

            // Verify
            assertEquals("UNKNOWN_CUSTOMER_ID", customerId);
        }
    }

    @Test
    void testGetDocumentTypeForImage_Success() throws Exception {
        // Setup
        Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("BankStatement");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);
        imageWiseParameters.put("******************", params);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getDocumentTypeForImage", Map.class);
        method.setAccessible(true);
        String documentType = (String) method.invoke(ocrService, imageWiseParameters);

        // Verify
        assertEquals("BankStatement", documentType);
    }

    @Test
    void testGetDocumentTypeForImage_CancelledChequePhoto() throws Exception {
        // Setup
        Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("cancelledChequePhoto");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);
        imageWiseParameters.put("******************", params);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getDocumentTypeForImage", Map.class);
        method.setAccessible(true);
        String documentType = (String) method.invoke(ocrService, imageWiseParameters);

        // Verify
        assertEquals("cancelledChequePhoto", documentType);
    }

    @Test
    void testGetDocumentTypeForImage_NoDocumentType() throws Exception {
        // Setup
        Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        // No document type set
        imageWiseParameters.put("******************", params);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getDocumentTypeForImage", Map.class);
        method.setAccessible(true);
        
        // Verify - should throw RuntimeException
        assertThrows(RuntimeException.class, () -> {
            try {
                method.invoke(ocrService, imageWiseParameters);
            } catch (Exception e) {
                if (e.getCause() instanceof RuntimeException) {
                    throw (RuntimeException) e.getCause();
                }
                throw e;
            }
        });
    }

    @Test
    void testGetDocumentTypeForImage_EmptyMap() throws Exception {
        // Setup
        Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters = new HashMap<>();

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getDocumentTypeForImage", Map.class);
        method.setAccessible(true);
        
        // Verify - should throw RuntimeException
        assertThrows(RuntimeException.class, () -> {
            try {
                method.invoke(ocrService, imageWiseParameters);
            } catch (Exception e) {
                if (e.getCause() instanceof RuntimeException) {
                    throw (RuntimeException) e.getCause();
                }
                throw e;
            }
        });
    }

    @Test
    void testGenerateOCRHeaders() throws Exception {
        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("generateOCRHeaders");
        method.setAccessible(true);
        HttpHeaders headers = (HttpHeaders) method.invoke(ocrService);

        // Verify
        assertNotNull(headers);
        assertEquals("test-client-id", headers.getFirst("client-id"));
        assertEquals("test-secret", headers.getFirst("client-secret"));
        assertEquals("application/json", headers.getFirst("Content-Type"));
    }

    @Test
    void testCallUmpRedisSetKeyAPI_Success() throws Exception {
        // Setup
        String redisKey = "test-midBANK_PROOF_UPLOAD";
        Map<String, Object> result = new HashMap<>();
        
        // Create nested maps for the OCR data
        Map<String, Object> accountNumberMap = new HashMap<>();
        accountNumberMap.put("value", "**************");
        accountNumberMap.put("confidence", 0.9999);
        result.put("account_number", accountNumberMap);
        
        Map<String, Object> ifscCodeMap = new HashMap<>();
        ifscCodeMap.put("value", "HDFC0000732");
        ifscCodeMap.put("confidence", 0.9999);
        result.put("ifsc_code", ifscCodeMap);
        
        Map<String, Object> accountHolderMap = new HashMap<>();
        accountHolderMap.put("value", "Amit");
        accountHolderMap.put("confidence", 0.9999);
        result.put("account_holder_name", accountHolderMap);
        
        result.put("dmsIds", "DM143819942560");
        result.put("documentType", "cancelledChequePhoto");
        
        // Mock the UMP API response
        String umpResponse = "{\"status\":\"SUCCESS\",\"message\":\"Key-value pair set successfully in Redis\",\"data\":null}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(umpResponse, HttpStatus.OK);
        when(restProcessorDelegate.executeUMPRequestHystrix(anyString(), eq("POST"), isNull(), any(), any(), eq(String.class)))
                .thenReturn(responseEntity);
        
        // No need to mock objectMapper.convertValue since we're using Map directly
        when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<String, Object>() {{
            put("status", "SUCCESS");
            put("message", "Key-value pair set successfully in Redis");
        }});

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("callUmpRedisSetKeyAPI", String.class, Map.class);
        method.setAccessible(true);
        method.invoke(ocrService, redisKey, result);

        // Verify
        verify(restProcessorDelegate, atLeastOnce()).executeUMPRequestHystrix(
                eq("https://test-app-url.com/api/v1/redis/setKey"), 
                eq("POST"), 
                isNull(), 
                any(), 
                any(), 
                eq(String.class)
        );
    }

    @Test
    void testCallUmpRedisSetKeyAPI_Error() throws Exception {
        // Setup
        String redisKey = "test-midBANK_PROOF_UPLOAD";
        Map<String, Object> result = new HashMap<>();
        result.put("account_number", null);
        result.put("ifsc_code", null);
        result.put("account_holder_name", null);
        result.put("dmsIds", "DM143819942560");
        result.put("documentType", "cancelledChequePhoto");
        
        // Mock the UMP API error response
        String umpResponse = "{\"status\":\"ERROR\",\"message\":\"Key and value are required\",\"data\":null}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(umpResponse, HttpStatus.BAD_REQUEST);
        when(restProcessorDelegate.executeUMPRequestHystrix(anyString(), eq("POST"), isNull(), any(), any(), eq(String.class)))
                .thenReturn(responseEntity);
        
        // No need to mock objectMapper.convertValue since we're using Map directly
        when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<String, Object>() {{
            put("status", "ERROR");
            put("message", "Key and value are required");
        }});

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("callUmpRedisSetKeyAPI", String.class, Map.class);
        method.setAccessible(true);
        method.invoke(ocrService, redisKey, result);

        // Verify
        verify(restProcessorDelegate, atLeastOnce()).executeUMPRequestHystrix(
                eq("https://test-app-url.com/api/v1/redis/setKey"), 
                eq("POST"), 
                isNull(), 
                any(), 
                any(), 
                eq(String.class)
        );
    }

    @Test
    void testGenerateOCRHeaders_MissingSecret() throws Exception {
        // Setup - remove secret
        AWSSecretManager.awsSecretsMap.remove("ocr.client.secret");

        // Execute and verify exception
        var method = OcrServiceImpl.class.getDeclaredMethod("generateOCRHeaders");
        method.setAccessible(true);
        
        assertThrows(RuntimeException.class, () -> {
            try {
                method.invoke(ocrService);
            } catch (Exception e) {
                if (e.getCause() instanceof RuntimeException) {
                    throw (RuntimeException) e.getCause();
                }
                throw e;
            }
        });
        
        // Restore secret for other tests
        AWSSecretManager.awsSecretsMap.put("ocr.client.secret", "test-secret");
    }

    @Test
    void testGenerateUMPHeaders() throws Exception {
        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("generateUMPHeaders");
        method.setAccessible(true);
        HttpHeaders headers = (HttpHeaders) method.invoke(ocrService);

        // Verify
        assertNotNull(headers);
        assertEquals("application/json", headers.getFirst("Content-Type"));
        assertEquals("AggregatorGateway-BFF-V2/1.0", headers.getFirst("User-Agent"));
        assertEquals("1.0", headers.getFirst("x-ump-version"));
    }

    @Test
    void testHandleDeductionAPIResponse_Success() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            String responseBody = "{\"status\":\"SUCCESS\",\"statusCode\":\"200\",\"statusMessage\":\"Request successful\",\"requestId\":\"test-request-id\"}";
            ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");
            
            // Create a proper response map
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("status", "SUCCESS");
            responseMap.put("statusCode", "200");
            responseMap.put("statusMessage", "Request successful");
            responseMap.put("requestId", "test-request-id");
            when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(responseMap);

            // Execute
            var method = OcrServiceImpl.class.getDeclaredMethod("handleDeductionAPIResponse", ResponseEntity.class, String.class);
            method.setAccessible(true);
            boolean result = (Boolean) method.invoke(ocrService, response, "test-request-id");

            // Verify
            assertTrue(result);
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
        }
    }

    @Test
    void testHandleDeductionAPIResponse_Failure() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            String responseBody = "{\"status\":\"FAILED\",\"statusCode\":\"402\",\"statusMessage\":\"Invalid request data\"}";
            ResponseEntity<String> response = new ResponseEntity<>(responseBody, HttpStatus.OK);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");
            
            // Create a proper response map
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("status", "FAILED");
            responseMap.put("statusCode", "402");
            responseMap.put("statusMessage", "Invalid request data");
            when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(responseMap);

            // Execute
            var method = OcrServiceImpl.class.getDeclaredMethod("handleDeductionAPIResponse", ResponseEntity.class, String.class);
            method.setAccessible(true);
            boolean result = (Boolean) method.invoke(ocrService, response, "test-request-id");

            // Verify
            assertFalse(result);
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
        }
    }

    @Test
    void testHandleDeductionAPIResponse_EmptyResponse() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenReturn(mockMerchant);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            ResponseEntity<String> response = new ResponseEntity<>(null, HttpStatus.OK);
            when(objectMapper.writeValueAsString(any())).thenReturn("{}");

            // Execute
            var method = OcrServiceImpl.class.getDeclaredMethod("handleDeductionAPIResponse", ResponseEntity.class, String.class);
            method.setAccessible(true);
            boolean result = (Boolean) method.invoke(ocrService, response, "test-request-id");

            // Verify
            assertFalse(result);
            verify(valueOperations, atLeastOnce()).set(anyString(), anyString(), anyLong(), any());
        }
    }

    @Test
    void testGetCurrentRedisState_WithData() throws Exception {
        // Setup
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        String redisData = "{\"requestStatus\":\"DEDUCTION_PASSED\",\"result\":{\"dmsIds\":\"DM143819942560\"}}";
        when(valueOperations.get(anyString())).thenReturn(redisData);
        
        // Create a proper response map
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("requestStatus", "DEDUCTION_PASSED");
        responseMap.put("result", Map.of("dmsIds", "DM143819942560"));
        when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(responseMap);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getCurrentRedisState", String.class);
        method.setAccessible(true);
        ResponseUmp result = (ResponseUmp) method.invoke(ocrService, "test-key");

        // Verify
        assertEquals("SUCCESS", result.getStatus());
        assertEquals("200", result.getStatusCode());
        assertNotNull(result.getResults());
    }

    @Test
    void testGetCurrentRedisState_NoData() throws Exception {
        // Setup
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(anyString())).thenReturn(null);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getCurrentRedisState", String.class);
        method.setAccessible(true);
        ResponseUmp result = (ResponseUmp) method.invoke(ocrService, "test-key");

        // Verify
        assertEquals("FAILED", result.getStatus());
        assertEquals("404", result.getStatusCode());
    }

    @Test
    void testGetFirstDocumentType() throws Exception {
        // Setup
        Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        QualificationCallbackRequest.ParameterValue docType = new QualificationCallbackRequest.ParameterValue();
        docType.setValue("BankStatement");
        docType.setConfidence(0.9999);
        params.setDocument_type(docType);
        imageWiseParameters.put("******************", params);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getFirstDocumentType", Map.class);
        method.setAccessible(true);
        String documentType = (String) method.invoke(ocrService, imageWiseParameters);

        // Verify
        assertEquals("BankStatement", documentType);
    }

    @Test
    void testGetFirstDocumentType_Default() throws Exception {
        // Setup
        Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters = new HashMap<>();
        QualificationCallbackRequest.ImageParameters params = new QualificationCallbackRequest.ImageParameters();
        // No document type set
        imageWiseParameters.put("******************", params);

        // Execute
        var method = OcrServiceImpl.class.getDeclaredMethod("getFirstDocumentType", Map.class);
        method.setAccessible(true);
        String documentType = (String) method.invoke(ocrService, imageWiseParameters);

        // Verify
        assertEquals("cancelledChequePhoto", documentType);
    }

    @Test
    void testQualificationCallBack_Exception() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks to throw exception
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenThrow(new RuntimeException("Test exception"));

            // Execute
            ResponseUmp result = ocrService.qualificationCallBack(successRequest);

            // Verify
            assertEquals("FAILED", result.getStatus());
            assertEquals("500", result.getStatusCode());
            assertEquals("Internal server error", result.getStatusMessage());
        }
    }

    @Test
    void testDeductionsCallBack_Exception() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {
            // Setup mocks to throw exception
            securityUtilsMock.when(SecurityUtils::getCurrentMerchant).thenThrow(new RuntimeException("Test exception"));

            Map<String, Object> request = new HashMap<>();
            request.put("status", "SUCCESS");

            // Execute
            ResponseUmp result = ocrService.deductionsCallBack(request);

            // Verify
            assertEquals("FAILED", result.getStatus());
            assertEquals("500", result.getStatusCode());
            assertEquals("Internal server error", result.getStatusMessage());
        }
    }
}
