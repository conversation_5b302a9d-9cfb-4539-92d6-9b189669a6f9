package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class LoyaltyPointsServiceImplTest {

    @Mock
    private ObjectMapper jsonMapper;

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @InjectMocks
    private LoyaltyPointsServiceImpl loyaltyPointsService;

    private Authentication authentication;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        ReflectionTestUtils.setField(loyaltyPointsService, "jsonMapper", new ObjectMapper());
        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put("pg.reward.secret.key", "mockRewardSecretKey");
        AWSSecretManager.awsSecretsMap.put("kyb.client.secret", "mockKybClientSecret");
    }

    @Test
    public void testGetSummary() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        String mockResponse = "{\n" +
                "  \"response\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        ResponseUmp res = loyaltyPointsService.getSummary("123");
        Map<String, Map<String, Map<String, String>>> actual = MappingUtils.convertJsonToType(mockResponse, Map.class);
        assertEquals(actual.get("response").get("result").get("resultMsg"), res.getStatusMessage());
    }

    @Test
    public void testGetSummaryNoUid() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        String mockResponse = "{\n" +
                "  \"response\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        ResponseUmp res = loyaltyPointsService.getSummary(null);
        Map<String, Map<String, Map<String, String>>> actual = MappingUtils.convertJsonToType(mockResponse, Map.class);
        assertEquals("SUCCESS", res.getStatus());
    }

    @Test
    public void testGetSummaryInvalid() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        String mockResponse = "{\n" +
                "  \"response\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.INTERNAL_SERVER_ERROR));

        assertThrows(ValidationException.class, () -> {
            loyaltyPointsService.getSummary("123");
            MappingUtils.convertJsonToType(mockResponse, Map.class);
        });

    }

    @Test
    public void testGetSummaryInvalidResponse() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        String mockResponse = "{\n" +
                "  \"body\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        assertThrows(ValidationException.class, () -> {
            loyaltyPointsService.getSummary("123");
            MappingUtils.convertJsonToType(mockResponse, Map.class);
        });

    }

    @Test
    public void testCheckBalance() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        String mockResponse = "{\n" +
                "  \"response\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        String res = loyaltyPointsService.checkBalance("123");
        assertEquals(mockResponse, res);
    }

    @Test
    public void testCheckBalanceNoUid() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        String mockResponse = "{\n" +
                "  \"response\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        String res = loyaltyPointsService.checkBalance(null);
        Map<String, Map<String, Map<String, String>>> resultInfo = MappingUtils.convertJsonToType(res, Map.class);
        assertEquals("SUCCESS", resultInfo.get("response").get("result").get("resultCode"));
    }

    @Test
    public void testFetchUid() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(loyaltyPointsService, "kybClientId", "123");
        ReflectionTestUtils.setField(loyaltyPointsService, "kybClientUserId", "123");
        String mockResponse = "{\n" +
                "  \"custId\": 12345\n" +
                "}";

        when(restProcessorDelegate.executeKybRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        String res = loyaltyPointsService.fetchUid("123");
        Map<String, Integer> expect = MappingUtils.convertJsonToType(mockResponse, Map.class);
        assertEquals(expect.get("custId").toString(), res);
    }

    @Test
    public void testFetchUidInvalidRes() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(loyaltyPointsService, "kybClientId", "123");
        ReflectionTestUtils.setField(loyaltyPointsService, "kybClientUserId", "123");
        String mockResponse = "{\n" +
                "  \"cust_id\": \"12345\"\n" +
                "}";

        when(restProcessorDelegate.executeKybRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        String res = loyaltyPointsService.fetchUid("123");
		assertNull(res);
    }

    @Test
    public void testFetchUidInvalid() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(loyaltyPointsService, "kybClientId", "123");
        ReflectionTestUtils.setField(loyaltyPointsService, "kybClientUserId", "123");
        String mockResponse = "{\n" +
                "  \"cust_id\": \"12345\"\n" +
                "}";

        when(restProcessorDelegate.executeKybRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.INTERNAL_SERVER_ERROR));

        assertThrows(ValidationException.class, () -> {
            loyaltyPointsService.fetchUid("123");
        });
    }

    @Test
    public void testGetList() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Object> req = new HashMap<>();
        req.put("pageNum", 1);
        req.put("pageSize", 1);
        req.put("transactionType", "test");
        req.put("accountingType", "user");
        String mockResponse = "{\n" +
                "  \"response\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

        ResponseUmp res = loyaltyPointsService.getList(req, "123");
        Map<String, Map<String, Map<String, Object>>> actual = jsonMapper.readValue(mockResponse, new TypeReference<Map<String, Map<String, Map<String, Object>>>>() {});
        assertEquals(actual.get("response").get("result").get("resultCode").toString(), res.getStatus());
    }

    @Test
    public void testGetListNoUid() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Object> req = new HashMap<>();

        ResponseUmp res = loyaltyPointsService.getList(req, null);
        assertEquals("SUCCESS", res.getStatus());
    }

    @Test
    public void testGetListInvalid() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Object> req = new HashMap<>();
        req.put("pageNum", 1);
        req.put("pageSize", 1);
        req.put("transactionType", "test");
        req.put("accountingType", "user");
        String mockResponse = "{\n" +
                "  \"body\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));
        assertThrows(ValidationException.class, () -> {
            loyaltyPointsService.getList(req, "123");
        });
    }

    @Test
    public void testGetListInvalidStatus() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Object> req = new HashMap<>();
        req.put("pageNum", 1);
        req.put("pageSize", 1);
        req.put("transactionType", "test");
        req.put("accountingType", "user");
        String mockResponse = "{\n" +
                "  \"body\": {\n" +
                "    \"result\": {\n" +
                "      \"resultCode\": 1,\n" +
                "      \"resultMsg\": \"Yes\"\n" +
                "    }\n" +
                "  }\n" +
                "}";

        when(restProcessorDelegate.executeRewardsRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.INTERNAL_SERVER_ERROR));

        assertThrows(ValidationException.class, () -> {
            loyaltyPointsService.getList(req, "123");
        });
    }

}
