package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class MerchantReferralServiceImplTest {

    @Mock
    RestProcessorDelegate restProcessorDelegate;

    @Mock
    AWSSecretManager awsSecretManager;

    @InjectMocks
    MerchantReferralServiceImpl merchantReferralService;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception {
        MockitoAnnotations.openMocks(this);

        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        ReflectionTestUtils.setField(merchantReferralService, "merchantReferralBaseUrl", "123");
        ReflectionTestUtils.setField(merchantReferralService, "promocardJwtClient", "123");
        Map<String, String> awsSecretsMapMock = new HashMap<>();
        awsSecretsMapMock.put(AWSSecrets.MSUPERCASH_JWT_SECRET.getValue(), "7vVcodm4P8Jen1cvNMmQSdZYCgknctxU22100FnUR");
        ReflectionTestUtils.setField(awsSecretManager, "awsSecretsMap", awsSecretsMapMock);
    }

    @Test
    public void getReferralTest() throws Exception{

        when(restProcessorDelegate.executePromoRequestHystrix(any(), any(),any(),any(),any(),any()))
                .thenReturn(new ResponseEntity<>("123", HttpStatus.OK));
        String response = merchantReferralService.getReferral("123","123","123","123");
        assertEquals(response,"123");
    }

    @Test
    public void getReferralFailureTest() throws Exception{

        when(restProcessorDelegate.executePromoRequestHystrix(any(), any(),any(),any(),any(),any()))
                .thenReturn(new ResponseEntity<>("{\n" +
                        "  \"status\": 0,\n" +
                        "  \"errors\": [\n" +
                        "    {\n" +
                        "      \"status\": 400,\n" +
                        "      \"message\": \"Validation failed\",\n" +
                        "      \"title\": \"Validation Error\",\n" +
                        "      \"errorCode\": \"SUCASH_4013\"\n" +
                        "    }\n" +
                        "  ]\n" +
                        "}", HttpStatus.BAD_REQUEST));
        String response = merchantReferralService.getReferral("123","123","123","123");
        assertTrue(response.contains("FAILURE"));
    }

}
