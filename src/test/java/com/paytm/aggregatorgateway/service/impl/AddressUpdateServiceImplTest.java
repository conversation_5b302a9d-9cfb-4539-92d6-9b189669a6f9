package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.dto.Address;
import com.paytm.aggregatorgateway.dto.AddressUpdateDTO;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;

public class AddressUpdateServiceImplTest {

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @Mock
    private Environment environment;

    @Mock
    private ObjectMapper jacksonObjectMapper;

    @Mock
    private HomepageWidgetDao homepageWidgetDao;

	@Mock
	private Authentication authentication;

    @InjectMocks
    private AddressUpdateServiceImpl addressUpdateService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		ReflectionTestUtils.setField(addressUpdateService, "jacksonObjectMapper", new ObjectMapper());
		AWSSecretManager.awsSecretsMap = new HashMap<>();
		AWSSecretManager.awsSecretsMap.put(AWSSecrets.CST_SECRET_KEY.getValue(), "testSecretKey");
    }

	@Test
	public void testUpdateAddress() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);
		AddressUpdateDTO addressUpdateDTO = new AddressUpdateDTO();
		Address address = new Address();
		addressUpdateDTO.setAddress(address);
		addressUpdateDTO.setTicketNumber("testTicketNumber");

		ResponseEntity<String> httpResponse = new ResponseEntity<>("{}", HttpStatus.OK);
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), eq(HttpMethod.GET.name()), any(), any(), eq(null), eq(String.class))).thenReturn(httpResponse);

		JsonNode httpResponseBody = jacksonObjectMapper.readTree(httpResponse.getBody());
		Map<String, Object> responseBody = jacksonObjectMapper.convertValue(httpResponseBody, new TypeReference<Map<String, Object>>() {});
		when(jacksonObjectMapper.readTree(anyString())).thenReturn(httpResponseBody);
		when(jacksonObjectMapper.convertValue(any(JsonNode.class), any(TypeReference.class))).thenReturn(responseBody);

		when(environment.getProperty(anyString())).thenReturn("testUrl");
		doNothing().when(homepageWidgetDao).updateStatusAndFlag(anyString(), anyString(), any(), anyString(), anyString(), any(), anyString(), anyString(), any());
		when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class)))
				.thenReturn(new ResponseEntity<>("{\"status\": \"SUCCESS\", \"statusCode\": \"200\", \"statusMessage\": \"Address updated successfully\"}", HttpStatus.OK));

		ResponseUmp result = addressUpdateService.updateAddress(addressUpdateDTO);

		assertEquals("SUCCESS", result.getStatus());
		assertEquals("200", result.getStatusCode());
		assertEquals("Address updated successfully", result.getStatusMessage());
	}

	@Test
	public void testUpdateAddressException() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);
		AddressUpdateDTO addressUpdateDTO = new AddressUpdateDTO();
		Address address = new Address();
		addressUpdateDTO.setAddress(address);
		addressUpdateDTO.setTicketNumber("testTicketNumber");

		ResponseEntity<String> httpResponse = new ResponseEntity<>("{}", HttpStatus.OK);
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), eq(HttpMethod.GET.name()), any(), any(), eq(null), eq(String.class))).thenReturn(httpResponse);

		JsonNode httpResponseBody = jacksonObjectMapper.readTree(httpResponse.getBody());
		Map<String, Object> responseBody = jacksonObjectMapper.convertValue(httpResponseBody, new TypeReference<Map<String, Object>>() {});
		when(jacksonObjectMapper.readTree(anyString())).thenReturn(httpResponseBody);
		when(jacksonObjectMapper.convertValue(any(JsonNode.class), any(TypeReference.class))).thenReturn(responseBody);

		when(environment.getProperty(anyString())).thenReturn("testUrl");
		doNothing().when(homepageWidgetDao).updateStatusAndFlag(anyString(), anyString(), any(), anyString(), anyString(), any(), anyString(), anyString(), any());
		when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class)))
				.thenReturn(new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR));

		assertThrows(RuntimeException.class, () -> {
			addressUpdateService.updateAddress(addressUpdateDTO);
		});
	}

    @Test
    public void testUpdateAddressFailure() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);
        AddressUpdateDTO addressUpdateDTO = new AddressUpdateDTO();
        Address address = new Address();
        addressUpdateDTO.setAddress(address);
        addressUpdateDTO.setTicketNumber("testTicketNumber");

		ResponseEntity<String> httpResponse = new ResponseEntity<>("{\"addressLine1\": \"testAddress1\", \"addressLine2\": \"testAddress2\"}", HttpStatus.OK);
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), eq(HttpMethod.GET.name()), any(), any(), eq(null), eq(String.class))).thenReturn(httpResponse);

		JsonNode httpResponseBody = jacksonObjectMapper.readTree(httpResponse.getBody());
		Map<String, Object> responseBody = jacksonObjectMapper.convertValue(httpResponseBody, new TypeReference<Map<String, Object>>() {});
		when(jacksonObjectMapper.readTree(anyString())).thenReturn(httpResponseBody);
		when(jacksonObjectMapper.convertValue(any(JsonNode.class), any(TypeReference.class))).thenReturn(responseBody);

		when(environment.getProperty(anyString())).thenReturn("testUrl");
		doNothing().when(homepageWidgetDao).updateStatusAndFlag(anyString(), anyString(), any(), anyString(), anyString(), any(), anyString(), anyString(), any());
        when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class)))
                .thenReturn(new ResponseEntity<>("{\"status\": \"SUCCESS\", \"statusCode\": \"200\", \"statusMessage\": \"Address updated successfully\"}", HttpStatus.OK));

		try {
			ResponseUmp result = addressUpdateService.updateAddress(addressUpdateDTO);
		} catch (ResponseUmpException e) {
			assertEquals("UMP-911", e.getResponse().getStatusCode());
			assertEquals("Address already updated", e.getResponse().getStatusMessage());
		}
	}

}