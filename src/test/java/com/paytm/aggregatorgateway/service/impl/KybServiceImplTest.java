package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class KybServiceImplTest {

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @Mock
    private ObjectMapper jsonMapper;

    @Mock
    AWSSecretManager awsSecretManager;

    @InjectMocks
    private KybServiceImpl kybService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        Map<String, String> awsSecretsMapMock = new HashMap<>();
        awsSecretsMapMock.put(AWSSecrets.KYB_CLIENT_SECRET.getValue(), "TYMVUzbMcW55uMk7AchYQePyMF5IUoFD9Q_9AA");
        ReflectionTestUtils.setField(awsSecretManager, "awsSecretsMap", awsSecretsMapMock);
    }

    @Test
    public void testGetDeploymentAddress() throws Exception {
        String mid = "mid";
        String deviceId = "deviceId";

        String expectedResponse = "{\n" +
                "  \"deploymentAddress\": {\n" +
                "    \"mid\": \"abc52319797699466\",\n" +
                "    \"deviceAddress\": [\n" +
                "      {\n" +
                "        \"deviceId\": \"123456\",\n" +
                "        \"deviceType\": \"EDC\",\n" +
                "        \"addressType\": \"Deployment\",\n" +
                "        \"status\": 1,\n" +
                "        \"address\": \"XYZ\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

        ObjectMapper objectMapper = new ObjectMapper();
        TypeReference<Map<String, Object>> typeReference = new TypeReference<Map<String, Object>>() {};
        Map<String, Object> responseMap = objectMapper.readValue(expectedResponse, typeReference);
        ResponseEntity<String> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);

        when(restProcessorDelegate.executeKybAddressRequestHystrix(anyString(), anyString(), any(Map.class), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);
        when(jsonMapper.readValue(anyString(), any(Class.class))).thenReturn(responseMap);

        String actualResponse = kybService.getDeploymentAddress(mid, deviceId, new HashMap<>());

        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testGetDeploymentAddressThrowsValidationException() throws Exception {
        String mid = "mid";
        String deviceId = "deviceId";
        HashMap<String, Object> responseMap = new HashMap<>();

        ResponseEntity<String> responseEntity = new ResponseEntity<>("", HttpStatus.OK);

        when(restProcessorDelegate.executeKybAddressRequestHystrix(anyString(), anyString(), any(Map.class), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);
        when(jsonMapper.readValue(anyString(), any(Class.class))).thenReturn(null);

        assertThrows(ValidationException.class, () -> {
            kybService.getDeploymentAddress(mid, deviceId, responseMap);
        });
    }

    @Test
    public void testGetDeploymentAddressThrowsUMPIntegrationException() throws Exception {
        String mid = "mid";
        String deviceId = "deviceId";

        String expectedResponse = "{\n" +
                "  \"deploymentAddress\": {\n" +
                "    \"mid\": \"abc52319797699466\",\n" +
                "    \"deviceAddress\": []\n" +
                "  }\n" +
                "}";

        ObjectMapper objectMapper = new ObjectMapper();
        TypeReference<Map<String, Object>> typeReference = new TypeReference<Map<String, Object>>() {};
        Map<String, Object> responseMap = objectMapper.readValue(expectedResponse, typeReference);

        ResponseEntity<String> responseEntity = new ResponseEntity<>("", HttpStatus.OK);

        when(restProcessorDelegate.executeKybAddressRequestHystrix(anyString(), anyString(), any(Map.class), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);
        when(jsonMapper.readValue(anyString(), any(Class.class))).thenReturn(responseMap);

        assertThrows(UMPIntegrationException.class, () -> {
            kybService.getDeploymentAddress(mid, deviceId, new HashMap<>());
        });
    }

    @Test
    public void testGetDeploymentAddressThrowsRuntimeException() throws Exception {
        String mid = "mid";
        String deviceId = "deviceId";

        ResponseEntity<String> responseEntity = new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        when(restProcessorDelegate.executeKybAddressRequestHystrix(anyString(), anyString(), any(Map.class), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);

        assertThrows(RuntimeException.class, () -> {
            kybService.getDeploymentAddress(mid, deviceId, new HashMap<>());
        });
    }
}
