package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.dao.SupportHomePageDao;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.CstService;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.service.helper.SupportHomePageTitleMapping;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class SupportHomePageServiceImplTest {

    @InjectMocks
    SupportHomePageServiceImpl supportHomePageService;

    @Mock
    Environment environment;

    @Mock
    RestProcessorDelegate restProcessorDelegate;

    @Mock
    CstService cstService;

    @Mock
    SupportHomePageDao supportHomePageDao;

    @Mock
    private SupportHomePageTitleMapping supportHomePageTitleMapping;

    private Authentication authentication;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private UPSService upsService;

    @BeforeEach
    public void init() throws Exception{
        MockitoAnnotations.openMocks(this);

        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);

        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put(AWSSecrets.STORE_FRONT_SECRET.getValue(), "storeFrontSecret");
        ReflectionTestUtils.setField(supportHomePageService,"objectMapper", new ObjectMapper());
    }

    @Test
    public void reopenTicketTest() throws Exception {

        when(environment.getRequiredProperty(anyString())).thenReturn("abcd");
        when(restProcessorDelegate.executeCstRequestHystrix(any(),any(),anyMap(),any(),anyMap(),eq(String.class))).thenReturn(new ResponseEntity<>("", HttpStatus.OK));
        String result = supportHomePageService.reopenTicket("1234","","",false);
        assertEquals("Ticket number 1234 has been successfully reopened.",result);
    }

    @Test
    public void reopenTicketCloseTicketTest() throws Exception {

        when(environment.getRequiredProperty(anyString())).thenReturn("abcd");
        when(restProcessorDelegate.executeCstRequestHystrix(anyString(),anyString(),anyMap(),any(),anyMap(),eq(String.class))).thenReturn(new ResponseEntity<>("", HttpStatus.OK));
        String result = supportHomePageService.reopenTicket("1234","","",true);
        assertEquals("Ticket number 1234 has been successfully reopened.",result);
    }

    @Test
    public void reopenTicketExceptionTest() throws Exception {

        when(environment.getRequiredProperty(anyString())).thenReturn("abcd");
        when(restProcessorDelegate.executeCstRequestHystrix(anyString(),anyString(),anyMap(),any(),anyMap(),eq(String.class))).thenReturn(new ResponseEntity<>("", HttpStatus.BAD_REQUEST));
        doThrow(new Exception()).when(cstService).updateTicket(anyString(),anyMap());
        assertThrows(Exception.class, () -> {
            supportHomePageService.reopenTicket("1234","","",false);
        });
    }

    @Test
    public void getSurveyTest() throws Exception {

        UtsHelper.mockUserAuthentication(authentication);
        String mockResponse="{\n" +
                "  \"actionMapping\": [\n" +
                "    {\n" +
                "      \"_id\": \"602617219cfebc1509d25883\",\n" +
                "      \"eventCategory\": \"a\",\n" +
                "      \"eventAction\": \"d\",\n" +
                "      \"eventLabel\": \"f\",\n" +
                "      \"deeplink\": \"paytmba://business-app/genericsurveypopup?url=https://survey-staging.paytm.com/static/index.html?survey_id=5\",\n" +
                "      \"surveyId\": 5,\n" +
                "      \"active\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": \"602617219cfebc1509d25884\",\n" +
                "      \"eventCategory\": \"d\",\n" +
                "      \"eventAction\": \"c\",\n" +
                "      \"eventLabel\": \"g\",\n" +
                "      \"deeplink\": \"paytmba://business-app/genericsurveypopup?url=https://survey-staging.paytm.com/static/index.html?survey_id=3\",\n" +
                "      \"surveyId\": 3,\n" +
                "      \"active\": true\n" +
                "    },\n" +
                "    {\n" +
                "      \"_id\": \"602617219cfebc1509d25885\",\n" +
                "      \"eventCategory\": \"j\",\n" +
                "      \"eventAction\": \"k\",\n" +
                "      \"eventLabel\": \"l\",\n" +
                "      \"deeplink\": \"paytmba://business-app/genericsurveypopup?url=https://survey-staging.paytm.com/static/index.html?survey_id=6\",\n" +
                "      \"surveyId\": 6,\n" +
                "      \"active\": true\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        ResponseEntity<String> mock = new ResponseEntity<>(mockResponse,HttpStatus.OK);
        when(restProcessorDelegate.executeSurveyRequestHystrix(any(),any(),any(),any(),any(),eq(String.class))).thenReturn(mock);
        String result= supportHomePageService.getSurvey();
        assertNotNull(result);
    }

    @Test
    public void getSurveyExceptionTest() throws Exception {

        ResponseEntity<String> mock = new ResponseEntity<>("",HttpStatus.BAD_REQUEST);
        when(restProcessorDelegate.executeSurveyRequestHystrix(anyString(),anyString(),anyMap(),any(),any(),eq(String.class))).thenReturn(mock);
        assertThrows(Exception.class, () -> {
            supportHomePageService.getSurvey();
        });

    }

    @Test
    public void feedbackUploadTest() throws Exception {

        when(environment.getRequiredProperty(anyString())).thenReturn("abcd");
        ResponseEntity<String> mockResponse=new ResponseEntity<>("",HttpStatus.OK);
        when(cstService.uploadFeedback(anyMap())).thenReturn(mockResponse);
        Object result = supportHomePageService.feedbackUpload(new HashMap<>());
        assertNotNull(result);
    }

    @Test
    public void feedbackUploadExceptionTest() throws Exception {

        when(environment.getRequiredProperty(anyString())).thenReturn("abcd");
        ResponseEntity<String> mockResponse=new ResponseEntity<>("",HttpStatus.BAD_REQUEST);
        when(cstService.uploadFeedback(anyMap())).thenReturn(mockResponse);
        assertThrows(Exception.class, () -> {
            supportHomePageService.feedbackUpload(new HashMap<>());
        });
    }

    @Test
    public void testFetchIssueCategory1() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(anyString(), anyString(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory2() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), anyMap(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory3() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory4() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory5() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory6() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory7() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory8() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory9() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory10() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory11() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory12() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory13() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "true");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory14() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory15() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory16() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory17() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory18() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory19() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "true");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory20() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory21() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory22() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory23() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory24() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategory25() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>(fetchIssueCategoryResponse(), HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.fetchIssueCategory(requestBody);
        verifyResults(result);
    }

    @Test
    public void testFetchIssueCategoryItemDTOSNull() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"page\": [\n" +
                "    {\n" +
                "      \"views\": [\n" +
                "        {\n" +
                "          \"items\": []\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(anyString(), anyString(), anyMap(), any(), anyMap(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        assertThrows(RuntimeException.class, () -> supportHomePageService.fetchIssueCategory(requestBody));
    }

    @Test
    public void testFetchIssueCategoryViewsDTOSNull() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"page\": [\n" +
                "    {\n" +
                "      \"views\": []\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(anyString(), anyString(), anyMap(), any(), anyMap(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        assertThrows(RuntimeException.class, () -> supportHomePageService.fetchIssueCategory(requestBody));
    }

    @Test
    public void testFetchIssueCategoryPageDTONull() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"page\": []\n" +
                "}", HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(anyString(), anyString(), anyMap(), any(), anyMap(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        assertThrows(RuntimeException.class, () -> supportHomePageService.fetchIssueCategory(requestBody));
    }

    @Test
    public void testFetchIssueCategoryPageDTOSNull() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("", HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(anyString(), anyString(), anyMap(), any(), anyMap(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        assertThrows(RuntimeException.class, () -> supportHomePageService.fetchIssueCategory(requestBody));
    }

    @Test
    public void testFetchIssueCategoryViewsDTOSListNull() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"page\": [\n" +
                "    {\n" +
                "      \"views\": [\n" +
                "        {\n" +
                "          \"wrong\": []\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(anyString(), anyString(), anyMap(), any(), anyMap(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        assertThrows(RuntimeException.class, () -> supportHomePageService.fetchIssueCategory(requestBody));
    }

    @Test
    public void testFetchIssueCategoryResponseParseError() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ReflectionTestUtils.setField(supportHomePageService, "soundBoxStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "cardMachineStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "paymentSettlementStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "bussinessLoanStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "accountSettingStoreFrontId", "2");
        ReflectionTestUtils.setField(supportHomePageService, "dealStoreFrontId", "1");
        ReflectionTestUtils.setField(supportHomePageService, "otherStoreFrontId", "1");

        // Mocking the required dependencies
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("sbRented", "false");
        requestBody.put("edcRented", "false");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"wrong\": []\n" +
                "}", HttpStatus.OK);
        when(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).thenReturn("http://localhost:8080");
        when(restProcessorDelegate.executeStoreFrontRequestHystrix(anyString(), anyString(), anyMap(), any(), anyMap(), eq(String.class))).thenReturn(httpResponse);

        // Calling the method under test
        assertThrows(RuntimeException.class, () -> supportHomePageService.fetchIssueCategory(requestBody));
    }

    @Test
    public void testGetTicketTimeLineWithValidTicketNumber() throws Exception {
        // Mocking the required dependencies
        TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();
        ticketInfoDTO.setTicketNumber("12345");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"timeLine\": [\n" +
                "    {\n" +
                "      \"id\": \"1\",\n" +
                "      \"name\": \"Event 1\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"2\",\n" +
                "      \"name\": \"Event 2\"\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK);
        when(cstService.getTicketTimeLine(anyString(), any())).thenReturn(httpResponse);

        // Calling the method under test
        String result = supportHomePageService.getTicketTimeLine(ticketInfoDTO);

        // Verifying the result
        ObjectMapper jacksonObjectMapper = new ObjectMapper();
        Map<String, Object> responseMap = jacksonObjectMapper.readValue(result, Map.class);
        List<Object> timeLineList = (List<Object>) responseMap.get("timeLine");
        assertEquals(2, timeLineList.size());
        Map<String, Object> event1 = (Map<String, Object>) timeLineList.get(0);
        assertEquals("1", event1.get("id"));
        assertEquals("Event 1", event1.get("name"));
        Map<String, Object> event2 = (Map<String, Object>) timeLineList.get(1);
        assertEquals("2", event2.get("id"));
        assertEquals("Event 2", event2.get("name"));
    }

    @Test
    public void testGetTicketTimeLineWithInvalidTicketNumber() throws Exception {
        TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();
        ticketInfoDTO.setTicketNumber("");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("", HttpStatus.INTERNAL_SERVER_ERROR);
        when(cstService.getTicketTimeLine(anyString(), any())).thenReturn(httpResponse);

        assertThrows(RuntimeException.class, () -> {
            supportHomePageService.getTicketTimeLine(ticketInfoDTO);
        });
    }

    @Test
    public void testGetTicketTimeLineWithInvalidCSTResponse() throws Exception {
        TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();
        ticketInfoDTO.setTicketNumber("12345");
        ResponseEntity<String> httpResponse = new ResponseEntity<>("", HttpStatus.INTERNAL_SERVER_ERROR);
        when(cstService.getTicketTimeLine(anyString(), any())).thenReturn(httpResponse);

        assertThrows(RuntimeException.class, () -> {
            supportHomePageService.getTicketTimeLine(ticketInfoDTO);
        });
    }

    @Test
    public void testGetAllTicketDetailsWithValidResponse1() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);
        SupportHomePageTitleMapping homePageTitleMapping = new SupportHomePageTitleMapping();
        homePageTitleMapping.titleMapping = new HashMap<>();
        homePageTitleMapping.titleMapping.put("p4bsoundbox", "p4bsoundbox");

        ReflectionTestUtils.setField(supportHomePageService, "openTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "resolvedTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "closedTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "supportHomePageTitleMapping", homePageTitleMapping);


        // Mocking the required dependencies
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"tickets\": [\n" +
                "    {\n" +
                "      \"source\": \"some_source\",\n" +
                "      \"origin\": 100,\n" +
                "      \"cstentity\": \"p4bsoundbox\",\n" +
                "      \"l1IssueCategory\": \"p4bsoundbox\",\n" +
                "      \"ticketStatus\": \"2\"\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK);
        when(cstService.getAllTicketDetails()).thenReturn(httpResponse);

        // Calling the method under test
        Map<String, Object> responseMap = supportHomePageService.getAllTickets(true);
        assertNotNull(responseMap);

    }

    @Test
    public void testGetAllTicketDetailsWithValidResponse2() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);
        SupportHomePageTitleMapping homePageTitleMapping = new SupportHomePageTitleMapping();
        homePageTitleMapping.titleMapping = new HashMap<>();
        homePageTitleMapping.titleMapping.put("p4bsoundbox", "p4bsoundbox");

        ReflectionTestUtils.setField(supportHomePageService, "openTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "resolvedTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "closedTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "supportHomePageTitleMapping", homePageTitleMapping);


        // Mocking the required dependencies
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"tickets\": [\n" +
                "    {\n" +
                "      \"source\": \"some_source\",\n" +
                "      \"origin\": 3,\n" +
                "      \"cstentity\": \"p4bsoundbox\",\n" +
                "      \"l1IssueCategory\": \"p4bsoundbox\",\n" +
                "      \"ticketStatus\": \"2\"\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK);
        when(cstService.getAllTicketDetails()).thenReturn(httpResponse);

        // Calling the method under test
        Map<String, Object> responseMap = supportHomePageService.getAllTickets(true);
        assertNotNull(responseMap);

    }

    @Test
    public void testGetAllTicketDetailsWithValidResponse3() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);
        setGetTicketDetailsMocking();

        // Calling the method under test
        Map<String, Object> responseMap = supportHomePageService.getAllTickets(true);
        assertNotNull(responseMap);
    }

    @Test
    public void testGetRecentTicketWithOpenTickets() throws Exception {
        // Creating the required input parameters
        Map<String, Object> ticketsMap = new HashMap<>();
        List<TicketInfoDTO> openTickets = new ArrayList<>();
        TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();
        ticketInfoDTO.setCstentity("p4bsoundbox");
        ticketInfoDTO.setCreatedAt("2022-01-01T00:00:00Z");
        ticketInfoDTO.setItemName("Item 1");
        ticketInfoDTO.setTicketStatus(2);
        openTickets.add(ticketInfoDTO);
        ticketsMap.put("openTickets", openTickets);
        setGetTicketDetailsMocking();
        List<String> openTicketStatusList = new ArrayList<>();
        openTicketStatusList.add("2");
        ReflectionTestUtils.setField(supportHomePageService, "openTicketStatus", openTicketStatusList);

        // Creating a mock response
        String mockResponse = "{\"key\":\"value\"}";
        ResponseEntity<String> mockHttpResponse = new ResponseEntity<>(mockResponse, HttpStatus.OK);

        // Mocking the getTicketTimeLine() method of the CstService class
        when(cstService.getTicketTimeLine(any(), any())).thenReturn(mockHttpResponse);

        // Calling the method under test with the input parameters
        Map<String, Object> result = supportHomePageService.getRecentTicket();

        // Verifying the result
        assertNotNull(result);
        assertNotNull(result.get("ticketDetails"));
        assertEquals("p4bsoundbox", ((TicketInfoDTO) result.get("ticketDetails")).getCstentity());
        assertEquals("2021-12-31 18:30:00", ((TicketInfoDTO) result.get("ticketDetails")).getCreatedAt());
        assertEquals("Paytm Services query", result.get("itemName"));
        assertEquals(2, result.get("ticketStatus"));
        assertNotNull(result.get("ticketTimeLine"));
        assertFalse((Boolean) result.get("pastTickets"));
    }

    private void setGetTicketDetailsMocking() throws Exception {
        SupportHomePageTitleMapping homePageTitleMapping = new SupportHomePageTitleMapping();
        homePageTitleMapping.titleMapping = new HashMap<>();
        homePageTitleMapping.titleMapping.put("p4bsoundbox", "p4bsoundbox");

        ReflectionTestUtils.setField(supportHomePageService, "openTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "resolvedTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "closedTicketStatus", new ArrayList<>());
        ReflectionTestUtils.setField(supportHomePageService, "supportHomePageTitleMapping", homePageTitleMapping);

        // Mocking the required dependencies
        ResponseEntity<String> httpResponse = new ResponseEntity<>("{\n" +
                "  \"tickets\": [\n" +
                "    {\n" +
                "      \"source\": \"some_source\",\n" +
                "      \"origin\": 5,\n" +
                "      \"cstentity\": \"p4bsoundbox\",\n" +
                "      \"l1IssueCategory\": \"p4bsoundbox\",\n" +
                "      \"ticketStatus\": \"2\",\n" +
                "      \"createdAt\": \"2022-01-01 00:00:00\",\n" +
                "      \"itemName\": \"itemName\"\n" +
                "    }\n" +
                "  ]\n" +
                "}", HttpStatus.OK);
        when(cstService.getAllTicketDetails()).thenReturn(httpResponse);
    }

    private void verifyResults(String result) throws IOException {
        ObjectMapper jacksonObjectMapper = new ObjectMapper();
        Map<String, Object> responseMap = jacksonObjectMapper.readValue(result, Map.class);
        List<Object> pageList = (List<Object>) responseMap.get("page");
        Map<String, Object> pageMap = (Map<String, Object>) pageList.get(0);
        List<Object> viewsList = (List<Object>) pageMap.get("views");
        Map<String, Object> viewsMap = (Map<String, Object>) viewsList.get(0);
        List<Object> itemsList = (List<Object>) viewsMap.get("items");
        assertEquals(2, itemsList.size());
        Map<String, Object> item1 = (Map<String, Object>) itemsList.get(0);
        assertEquals(2, item1.get("id"));
        assertEquals("Category 2", item1.get("name"));
    }

    private String fetchIssueCategoryResponse() {
        return "{\n" +
                "    \"page\": [\n" +
                "        {\n" +
                "            \"views\": [\n" +
                "                {\n" +
                "                    \"items\": [\n" +
                "                        {\n" +
                "                            \"id\": \"1\",\n" +
                "                            \"name\": \"Category 1\"\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\": \"2\",\n" +
                "                            \"name\": \"Category 2\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
    }

    /**
     * Unit testCases for function getCallDetails()
     * Case 1: When edcRented is false
     * Expectation: clean flow returns response
     */
    @Test
    public void getCallDetailsCase1() throws IOException, InterruptedException, UMPIntegrationException {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Boolean> devicePreferences = new HashMap<>();

        Mockito.when(upsService.getDevicePreferences(anyString())).thenReturn(devicePreferences);

        ResponseUmp actualResponse = supportHomePageService.getCallDetails();
        assertNotNull(actualResponse);
    }

    /**
     * Case 2: When getDevicePreferences returns null
     * Expectation: throws UMPIntegrationException
     */
    @Test
    public void getCallDetailsCase2() throws IOException, InterruptedException, UMPIntegrationException {
        UtsHelper.mockUserAuthentication(authentication);

        Mockito.when(upsService.getDevicePreferences(anyString())).thenReturn(null);
        assertThrows(UMPIntegrationException.class, () -> {
            supportHomePageService.getCallDetails();
        });
    }
    /**
     * Case 3: When edcRented is true & mid is not whitelisted
     * Expectation: clean flow returns response
     */
    @Test
    public void getCallDetailsCase3() throws IOException, InterruptedException, UMPIntegrationException {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Boolean> devicePreferences = new HashMap<>();
        devicePreferences.put("ocl.boss.merchant.edc", Boolean.TRUE);
        devicePreferences.put("ocl.iot.merchant.soundbox", Boolean.TRUE);

        Mockito.when(upsService.getDevicePreferences(anyString())).thenReturn(devicePreferences);
        Mockito.when(supportHomePageDao.checkMidWhitelistedForCallBack(anyString(), anyString())).thenReturn(Boolean.FALSE);

        Map<String, Object> expectedResult = new HashMap<>();
        expectedResult.put("edcRented", "true");
        expectedResult.put("sbRented", "true");
        expectedResult.put("isEligible", "false");

        ResponseUmp expectedResponse = new ResponseUmp("SUCCESS", "200", "Mid not whitelisted for call back", expectedResult);

        ResponseUmp actualResponse = supportHomePageService.getCallDetails();
        assertEquals(expectedResponse.getResults(), actualResponse.getResults());
    }
    /**
     *  Case 4: mid is whitelisted & IVR has placed call
     *  Expectation: clean flow, returns latestCallDetails
     */
    @Test
    public void getCallDetailsCase4() throws IOException, InterruptedException, UMPIntegrationException {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Boolean> devicePreferences = new HashMap<>();
        devicePreferences.put("ocl.boss.merchant.edc", Boolean.TRUE);
        devicePreferences.put("ocl.iot.merchant.soundbox", Boolean.TRUE);

        ResponseEntity<String> httpResponse = new ResponseEntity<>("\"{\\\"Status\\\":\\\"SUCCESS\\\",\\\"CustDetail\\\":[{\\\"CustId\\\":\\\"1435967372\\\",\\\"MID\\\":\\\"qa11VE51594478161509\\\",\\\"CallStatus\\\":\\\"NONCONNECTED\\\",\\\"CampaignName\\\":\\\"CallMeBack_EDC\\\",\\\"Attempt\\\":1,\\\"IsExist\\\":1,\\\"AttemptDateTime\\\":\\\"2023-09-27T17:49:34.303\\\",\\\"ExpectedNextCallTime\\\":\\\"2023-09-27T18:49:34.303\\\"}],\\\"ERRMSG\\\":\\\"NONE\\\"}\"",HttpStatus.OK);

        Mockito.when(upsService.getDevicePreferences(anyString())).thenReturn(devicePreferences);
        Mockito.when(supportHomePageDao.checkMidWhitelistedForCallBack(anyString(), anyString())).thenReturn(Boolean.TRUE);
        Mockito.when(cstService.getCallDetailIVR(any())).thenReturn(httpResponse);

        ResponseUmp actualResponse = supportHomePageService.getCallDetails();

        assertNotNull(actualResponse);
    }
    /**
     *  Case 5: mid is whitelisted & IVR has not placed call (No AttemptDateTime)
     *  Expectation: clean flow, returns latestCallDetails
     */
    @Test
    public void getCallDetailsCase5() throws IOException, InterruptedException, UMPIntegrationException {
        UtsHelper.mockUserAuthentication(authentication);
        Map<String, Boolean> devicePreferences = new HashMap<>();
        devicePreferences.put("ocl.boss.merchant.edc", Boolean.FALSE);
        devicePreferences.put("ocl.iot.merchant.soundbox", Boolean.TRUE);

        ResponseEntity<String> httpResponse = new ResponseEntity<>("\"{\\\"Status\\\":\\\"SUCCESS\\\",\\\"CustDetail\\\":[{\\\"CustId\\\":\\\"1107199221\\\",\\\"MID\\\":\\\"dipadd46834559641560\\\",\\\"UUID\\\":null,\\\"CallStatus\\\":null,\\\"CampaignName\\\":\\\"CallMeBack_EDC\\\",\\\"UserId\\\":null,\\\"Attempt\\\":0,\\\"IsExist\\\":1,\\\"InsertDt\\\":null,\\\"ExpectedNextCallTime\\\":null}],\\\"ERRMSG\\\":\\\"NONE\\\"}\"",HttpStatus.OK);

        Mockito.when(upsService.getDevicePreferences(anyString())).thenReturn(devicePreferences);
        Mockito.when(supportHomePageDao.checkMidWhitelistedForCallBack(anyString(), anyString())).thenReturn(Boolean.TRUE);
        Mockito.when(cstService.getCallDetailIVR(any())).thenReturn(httpResponse);

        ResponseUmp actualResponse = supportHomePageService.getCallDetails();

        assertNotNull(actualResponse);
    }
    /**
     *  Case 6: mid is whitelisted, error in parsing IVR response
     *  Expectation: throws RuntimeException
     */
    @Test
    public void getCallDetailsCase6() throws IOException, InterruptedException, UMPIntegrationException {
        UtsHelper.mockUserAuthentication(authentication);

        Map<String, Boolean> devicePreferences = new HashMap<>();
        devicePreferences.put("ocl.boss.merchant.edc", Boolean.FALSE);
        devicePreferences.put("ocl.iot.merchant.soundbox", Boolean.FALSE);

        ResponseEntity<String> httpResponse = new ResponseEntity<>("\"{\\\"Status\\\":\\\"SUCCESS\\\",\\\"CustDetail\\\":[{\\\"CustId\\\":\\\"1435967372\\\",\\\"MID\\\":\\\"qa11VE51594478161509\\\",\\\"CallStatus\\\":\\\"NONCONNECTED\\\",\\\"CampaignName\\\":\\\"CallMeBack_EDC\\\",\\\"Attempt\\\":1,\\\"IsExist\\\":1,\\\"AttemptDateTime\\\":\\\"2023-09-27T17:49:34.303.4\\\",\\\"ExpectedNextCallTime\\\":\\\"2023-09-27T18:49:34.303\\\"}],\\\"ERRMSG\\\":\\\"NONE\\\"}\"",HttpStatus.OK);

        Mockito.when(upsService.getDevicePreferences(anyString())).thenReturn(devicePreferences);
        Mockito.when(supportHomePageDao.checkMidWhitelistedForCallBack(anyString(), anyString())).thenReturn(Boolean.TRUE);
        Mockito.when(cstService.getCallDetailIVR(any())).thenReturn(httpResponse);

        assertThrows(RuntimeException.class, () -> supportHomePageService.getCallDetails());
    }

    /**
     * Unit testCases for function requestCallBack()
     * Case 1: ticket exists at FD
     * Expectation: clean flow, returns response
     */
    @Test
    public void getRequestCallbackCase1() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ResponseEntity<String> httpResponseGetTicket = new ResponseEntity<>("[\n" +
                "    {\n" +
                "        \"priority\": 1,\n" +
                "        \"source\": 110,\n" +
                "        \"status\": 72,\n" +
                "        \"subject\": \"P4B Callmeback\",\n" +
                "        \"id\": 38786,\n" +
                "        \"requester_id\": 14000006730929,\n" +
                "        \"due_by\": \"2023-10-30T21:00:00Z\",\n" +
                "        \"fr_due_by\": \"2023-10-26T21:00:00Z\",\n" +
                "        \"is_escalated\": false,\n" +
                "        \"group_id\": \"1082000383056\",\n" +
                "        \"custom_fields\": {\n" +
                "            \"cf_transaction_id\": \"Callback request\",\n" +
                "            \"cf_cst_entity\": \"p4bCallmeback\",\n" +
                "            \"cf_issue_category_l1\": \"Call back request\",\n" +
                "            \"cf_bot_params\": \"{\\\"uniqueKey\\\":\\\"P4B-dipadd46834559641560\\\",\\\"cstMetadata\\\":\\\"{\\\\\\\"cstentity\\\\\\\":\\\\\\\"p4bCallmeback\\\\\\\"}\\\"}\",\n" +
                "            \"cf_customer_issue_category_l1\": \"Call back request\",\n" +
                "            \"cf_merchant_id\": \"P4B-dipadd46834559641560\"\n" +
                "        },\n" +
                "        \"created_at\": \"2023-10-26T09:09:39Z\",\n" +
                "        \"updated_at\": \"2023-10-26T09:09:40Z\",\n" +
                "        \"tags\": [],\n" +
                "        \"product_id\": \"1082000000989\"\n" +
                "    }]", HttpStatus.OK);

        List<Map<String, Object>> ticketList = new ArrayList<>();
        Map<String, Object> ticket = new HashMap<>();
        ticket.put("status", 72);
        ticket.put("id", 38786);
        ticketList.add(ticket);

        ResponseEntity<String> httpResponseCallMeIVR = new ResponseEntity<>("SUCCESS", HttpStatus.OK);

        when(cstService.getTickets(any(), any())).thenReturn(httpResponseGetTicket);
        when(objectMapper.readValue(httpResponseGetTicket.getBody(), ArrayList.class)).thenReturn((ArrayList) ticketList);
        when(cstService.callMeIVR(any())).thenReturn(httpResponseCallMeIVR);
        Mockito.doNothing().when(cstService).updateTicket(anyString(), any());

        assertNotNull(supportHomePageService.requestCallBack(false, false));
    }
    /**
     * Case 2: ticket does not exist at FD, creates new ticket, IVR call failed, UpdateTicket failed
     * Expectation: throws UMPIntegrationException
     */
    @Test
    public void getRequestCallbackCase2() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ResponseEntity<String> httpResponseGetTicket = new ResponseEntity<>("[]", HttpStatus.OK);

        List<Map<String, Object>> ticketList = new ArrayList<>();

        ResponseEntity<String> httpResponseCreateTicket = new ResponseEntity<>("{\n" +
                "    \"responseCode\": \"BT_200\",\n" +
                "    \"message\": \"success\",\n" +
                "    \"cstentity\": \"p4bCallmeback\",\n" +
                "    \"ticketNumber\": \"38786\",\n" +
                "    \"createdAt\": \"2023-10-26 13:14:12\",\n" +
                "    \"updatedAt\": \"2023-10-26 13:14:12\",\n" +
                "    \"viewType\": \"FreshBot\",\n" +
                "    \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "    \"requesterId\": 14000006778279,\n" +
                "    \"botParams\": {\n" +
                "        \"uniqueKey\": \"P4B-WogAHt47348113552868\",\n" +
                "        \"cstMetadata\": \"{\\\"cstentity\\\":\\\"p4bCallmeback\\\"}\"\n" +
                "    }\n" +
                "}", HttpStatus.OK);
        Map<String, Object> ticket = new HashMap<>();
        ticket.put("ticketNumber", "38786");

        when(cstService.getTickets(any(), any())).thenReturn(httpResponseGetTicket);
        when(objectMapper.readValue(httpResponseGetTicket.getBody(), ArrayList.class)).thenReturn((ArrayList) ticketList);
        when(cstService.createTicket(anyString(), any(), any())).thenReturn(httpResponseCreateTicket);
        when(objectMapper.readValue(httpResponseCreateTicket.getBody(), Map.class)).thenReturn(ticket);
        doThrow(UMPIntegrationException.class).when(cstService).callMeIVR(any());
        doThrow(RuntimeException.class).when(cstService).updateTicket(anyString(), any());

        assertThrows(UMPIntegrationException.class, () -> supportHomePageService.requestCallBack(true, true));
    }

    /**
     * Case 2: ticket does not exist at FD, creates new ticket, IVR call failed
     * Expectation: throws UMPIntegrationException
     */
    @Test
    public void getRequestCallbackCase3() throws Exception {
        UtsHelper.mockUserAuthentication(authentication);

        ResponseEntity<String> httpResponseGetTicket = new ResponseEntity<>("[]", HttpStatus.OK);

        List<Map<String, Object>> ticketList = new ArrayList<>();

        ResponseEntity<String> httpResponseCreateTicket = new ResponseEntity<>("{\n" +
                "    \"responseCode\": \"BT_200\",\n" +
                "    \"message\": \"success\",\n" +
                "    \"cstentity\": \"p4bCallmeback\",\n" +
                "    \"ticketNumber\": \"38786\",\n" +
                "    \"createdAt\": \"2023-10-26 13:14:12\",\n" +
                "    \"updatedAt\": \"2023-10-26 13:14:12\",\n" +
                "    \"viewType\": \"FreshBot\",\n" +
                "    \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
                "    \"requesterId\": 14000006778279,\n" +
                "    \"botParams\": {\n" +
                "        \"uniqueKey\": \"P4B-WogAHt47348113552868\",\n" +
                "        \"cstMetadata\": \"{\\\"cstentity\\\":\\\"p4bCallmeback\\\"}\"\n" +
                "    }\n" +
                "}", HttpStatus.OK);
        Map<String, Object> ticket = new HashMap<>();
        ticket.put("ticketNumber", "38786");

        when(cstService.getTickets(any(), any())).thenReturn(httpResponseGetTicket);
        when(objectMapper.readValue(httpResponseGetTicket.getBody(), ArrayList.class)).thenReturn((ArrayList) ticketList);
        when(cstService.createTicket(anyString(), any(), any())).thenReturn(httpResponseCreateTicket);
        when(objectMapper.readValue(httpResponseCreateTicket.getBody(), Map.class)).thenReturn(ticket);
        doThrow(UMPIntegrationException.class).when(cstService).callMeIVR(any());
        Mockito.doNothing().when(cstService).updateTicket(anyString(), any());

        assertThrows(UMPIntegrationException.class, () -> supportHomePageService.requestCallBack(true, true));
    }

}
