package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SubscriptionServiceImplTest {

    @Mock
    private Environment env;

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @InjectMocks
    private SubscriptionServiceImpl subscriptionServiceImpl;

    private Authentication authentication;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
        authentication = mock(Authentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put("subscription.rental.jwt.secret",
                "s6R5p36e6a8nRJOjlxt6O6uRWUbf2Q1KW1bV2od2+gdudrFfkAPHkhBRos5fPcanE/OuS3VQZoU=");
    }

    @Test
    public void fetchSubscriptionTest1() throws Exception{
        UtsHelper.mockUserAuthentication(authentication);
        when(env.getRequiredProperty(anyString())).thenReturn("mockProperty");
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(
                anyString(),anyString(),anyMap(), any(HttpHeaders.class),any(),any(Class.class)))
                .thenReturn(new ResponseEntity<>("", HttpStatus.OK));
        Map<String, Object> response = subscriptionServiceImpl.fetchSubscription("", "", "", "", "", "");
        assertEquals("FAILURE", response.get("status"));
        assertEquals("UMP-606",response.get("statusCode"));
        assertEquals("Parsing Error Occurred", response.get("results"));
    }
    @Test
    public void fetchSubscriptionTest2() throws Exception{
        UtsHelper.mockUserAuthentication(authentication);
        when(env.getRequiredProperty(anyString())).thenReturn("mockProperty");
        when(restProcessorDelegate.executeSubscriptionRequestHystrix(
                anyString(),anyString(),anyMap(), any(HttpHeaders.class),any(),any(Class.class)))
                .thenReturn(new ResponseEntity<>("{}", HttpStatus.OK));
        Map<String, Object> response = subscriptionServiceImpl.fetchSubscription(
                "","","","","",""
        );
        assertEquals(0, response.size());
    }

}
