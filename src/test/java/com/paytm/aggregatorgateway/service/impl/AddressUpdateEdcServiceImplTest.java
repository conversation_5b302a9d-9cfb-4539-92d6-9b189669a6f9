package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.service.FsmService;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class AddressUpdateEdcServiceImplTest {

    private final String HTTP_RESPONSE = "{\n" +
            "    \"ticketNumber\": \"33348\",\n" +
            "    \"createdAt\": \"2023-05-15 16:32:17\"\n" +
            "}";

	@Mock
	private RestProcessorDelegate restProcessorDelegate;

	@Mock
	private ObjectMapper objectMapper;

	@Mock
	private Environment environment;

	@Mock
	private FsmService fsmService;

	@Mock
	private HomepageWidgetDao homepageWidgetDao;

	@InjectMocks
	private AddressUpdateEdcServiceImpl addressUpdateEdcService;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		ReflectionTestUtils.setField(addressUpdateEdcService, "objectMapper", new ObjectMapper());
		AWSSecretManager.awsSecretsMap = new HashMap<>();
		AWSSecretManager.awsSecretsMap.put("cst.secret.key.v2", "80879ypiyuyidnijlkjnDo2780hILxdvvQXu9sh");
		AWSSecretManager.awsSecretsMap.put("cst.secret.key", "80879ypiyuyidnijlkjnDo2780hILxdvvQXu9sh");
		AWSSecretManager.awsSecretsMap.put("boss.client.idt", "mockClientId");
	}

	@Test
	public void updateEdcAddressTest1() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		when(environment.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.INTERNAL_SERVER_ERROR));

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		assertThrows(UMPIntegrationException.class, () -> {
			addressUpdateEdcService.updateEdcAddress(requestBody, "mockMid");
		});
	}

	//TODO: unit test for the case when DB operation fails

	@Test
	public void updateEdcAddressTest2() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		when(environment.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>(HTTP_RESPONSE, HttpStatus.OK));
		when(restProcessorDelegate.executeFsmRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.INTERNAL_SERVER_ERROR));

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		ResponseUmp response = addressUpdateEdcService.updateEdcAddress(requestBody, "mockMid");
		assertNotNull(response);
		assertNotNull(response.getStatusCode());
		assertNotNull(response.getStatus());
		assertNotNull(response.getStatusMessage());
		assertNotNull(response.getResults());
		assertEquals("SUCCESS", response.getStatus());
		assertEquals("200", response.getStatusCode());
		assertEquals("Ticket creation, beat creation, and disabling tip was successful", response.getStatusMessage());

		Map<String, String> results = (Map<String, String>) response.getResults();
		assertEquals(1, results.size());
		assertTrue(results.containsKey("ticketNumber"));
		assertEquals("33348", results.get("ticketNumber"));
	}

	@Test
	public void updateEdcAddressTest3() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		when(environment.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>(HTTP_RESPONSE, HttpStatus.OK));
		when(restProcessorDelegate.executeFsmRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.OK));

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		ResponseUmp response = addressUpdateEdcService.updateEdcAddress(requestBody, "mockMid");
		assertNotNull(response);
		assertNotNull(response.getStatusCode());
		assertNotNull(response.getStatus());
		assertNotNull(response.getStatusMessage());
		assertNotNull(response.getResults());
		assertEquals("200", response.getStatusCode());
		assertEquals("SUCCESS", response.getStatus());
		assertEquals("Ticket creation, beat creation, and disabling tip was successful", response.getStatusMessage());

		Map<String, String> result = (Map<String, String>) response.getResults();
		assertTrue(result.containsKey("ticketNumber"));
		assertEquals(1, result.size());
		assertTrue(result.containsKey("ticketNumber"));
		assertEquals("33348", result.get("ticketNumber"));
	}

	@Test
	public void updateEdcAddressTest4() throws Exception {
		//ToDo: remove this
		UtsHelper.mockUserAuthentication(authentication);

		when(environment.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>(HTTP_RESPONSE, HttpStatus.OK));
		when(restProcessorDelegate.executeFsmRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.OK));

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("deviceId", "12345");
		requestBody.put("addressLine1", "abcd");
		requestBody.put("addressLine2", "efgh");
		requestBody.put("city", "city");
		requestBody.put("state", "state");
		requestBody.put("postalCode", "123456");

		ResponseUmp response = addressUpdateEdcService.updateEdcAddress(requestBody, "mockMid");
		assertNotNull(response);
		assertNotNull(response.getStatusCode());
		assertNotNull(response.getStatus());
		assertNotNull(response.getStatusMessage());
		assertNotNull(response.getResults());
		assertEquals("200", response.getStatusCode());
		assertEquals("SUCCESS", response.getStatus());
		assertEquals("Ticket creation, beat creation, and disabling tip was successful", response.getStatusMessage());

		Map<String, String> result = (Map<String, String>) response.getResults();
		assertTrue(result.containsKey("ticketNumber"));
		assertEquals(1, result.size());
		assertTrue(result.containsKey("ticketNumber"));
		assertEquals("33348", result.get("ticketNumber"));

	}

}
