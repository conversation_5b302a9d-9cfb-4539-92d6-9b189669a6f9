package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class EdcServiceImplTest {

	@Mock
	private RestProcessorDelegate restProcessorDelegate;

	@Mock
	private Environment commonProperties;

	@InjectMocks
	private EdcServiceImpl edcService;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
	}

	@Test
	public void fetchLatLongTest1() throws Exception {

		ResponseEntity<String> responseEntity = new ResponseEntity<>("{\n" +
				"    \"created_at\": \"2023-10-26T07:44:12Z\",\n" +
				"    \"updated_at\": \"2023-10-26T07:44:49Z\",\n" +
				"    \"subject\": \"P4B Callmeback\",\n" +
				"    \"description\": \"<div>Ticket related to P4B callmeback EDC</div>\",\n" +
				"    \"status\": \"5\",\n" +
				"    \"priority\": 1,\n" +
				"    \"source\": \"110\",\n" +
				"    \"product_id\": \"1082000000989\",\n" +
				"    \"custom_fields\": {\n" +
				"        \"cf_bot_params\": \"{\\\"uniqueKey\\\":\\\"P4B-WogAHt47348113552868\\\",\\\"cstMetadata\\\":\\\"{\\\\\\\"cstentity\\\\\\\":\\\\\\\"p4bCallmeback\\\\\\\"}\\\"}\",\n" +
				"        \"cf_client_id\": \"P4B\",\n" +
				"        \"cf_issue_category_l1\": \"Call back request\",\n" +
				"        \"cf_merchant_id\": \"P4B-WogAHt47348113552868\"\n" +
				"    }\n" +
				"}", HttpStatus.OK);

		when(commonProperties.getProperty(any())).thenReturn("testUrl");
		when(restProcessorDelegate.executeEosAddressRequestHystrix(anyString(), anyString(), any(), any(), any(), eq(String.class))).thenReturn(responseEntity);

		Map<String, Object> response = edcService.fetchLatLong("deviceId", "token");

		assertNotNull(response);
	}

	@Test
	public void fetchLatLongTest2() throws Exception {
		when(restProcessorDelegate.executeEosAddressRequestHystrix(anyString(), anyString(), any(), any(), any(), eq(String.class))).thenThrow(RuntimeException.class);

		assertThrows(RuntimeException.class, () -> {
			edcService.fetchLatLong("deviceId", "token");
		});
	}

	@Test
	public void fetchLatLongTest3() throws Exception {
		ResponseEntity<String> responseEntity = new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		when(restProcessorDelegate.executeEosAddressRequestHystrix(anyString(), anyString(), any(), any(), any(), eq(String.class))).thenReturn(responseEntity);

		assertThrows(RuntimeException.class, () -> {
			edcService.fetchLatLong("deviceId", "token");
		});
	}

}
