package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.dao.P4bNudgesDao;
import com.paytm.aggregatorgateway.dto.MLCLimitVo;
import com.paytm.aggregatorgateway.dto.MerchantInfoDto;
import com.paytm.aggregatorgateway.dto.P4bNudges;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.service.NotificationService;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class NudgeApiServiceImplTest {

    @InjectMocks
    private NudgeApiServiceImpl nudgeApiService;

    @Mock
    private UPSService upsService;

    @Mock
    private P4bNudgesDao p4bNudgesDao;

    @Mock
    private RedisHelper redisHelper;

    @Mock
    MerchantProfileService merchantProfileService;

    @Mock
    private NotificationService notificationService;

    @Test
    public void updateNudgeTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW\",\n" +
                "  \"consultDate\": \"2023-05-12\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW \"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        Map<String,Boolean> map = new HashMap<>();
        map.put("ocl.boss.merchant.edc",true);
        when(upsService.getDevicePreferences(anyString())).thenReturn(map);
        when( redisHelper.getLastNotificationSentOn(any())).thenReturn(null);
        doNothing().when(p4bNudgesDao).addP4BNudge(any(),any(),any(),any(),any(),any(),any(),any(),any(),any());
        MerchantInfoDto merchantInfoDto = new MerchantInfoDto();
        merchantInfoDto.setMerchantName("123");
        merchantInfoDto.setPrimaryMobileNumber("1234");
        when(merchantProfileService.fetchMerchantDetailsByMid(any())).thenReturn(merchantInfoDto);
        doNothing().when(notificationService).notifyPush(any(), any(), any());
        doNothing().when(notificationService).notifyWhatsappPush(any(), any(), any(), any());
        when(redisHelper.setLastNotificationSentOn(any())).thenReturn("123");
        nudgeApiService.updateNudge(requestMap);
    }
    @Test
    public void updateNudgeNonEdcTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW\",\n" +
                "  \"consultDate\": \"2023-05-12\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW \"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        Map<String,Boolean> map = new HashMap<>();
        map.put("ocl.boss.merchant.edc",false);
        when(upsService.getDevicePreferences(anyString())).thenReturn(map);
        when( redisHelper.getLastNotificationSentOn(any())).thenReturn(null);
        MerchantInfoDto merchantInfoDto = new MerchantInfoDto();
        merchantInfoDto.setMerchantName("123");
        merchantInfoDto.setPrimaryMobileNumber("1234");
        when(merchantProfileService.fetchMerchantDetailsByMid(any())).thenReturn(merchantInfoDto);
        doNothing().when(notificationService).notifyPush(any(), any(), any());
        doNothing().when(notificationService).notifyWhatsappPush(any(), any(), any(), any());
        when(redisHelper.setLastNotificationSentOn(any())).thenReturn("123");
        nudgeApiService.updateNudge(requestMap);
    }

    @Test
    public void updateNudgeTestNotificationSentIn24hrs() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW\",\n" +
                "  \"consultDate\": \"2023-05-12\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        Map<String,Boolean> map = new HashMap<>();
        map.put("ocl.boss.merchant.edc",true);
        when(upsService.getDevicePreferences(anyString())).thenReturn(map);
        when( redisHelper.getLastNotificationSentOn(any())).thenReturn("2023-09-29T12:23:12.707");
        when(redisHelper.getQueuedNotification(any())).thenReturn(null);//queue
        doNothing().when(redisHelper).evictLastSentOnKey(any());
        //doNothing().when(redisHelper).evictQueue(any());
        doNothing().when(p4bNudgesDao).addP4BNudge(any(),any(),any(),any(),any(),any(),any(),any(),any(),any());
        MerchantInfoDto merchantInfoDto = new MerchantInfoDto();
        merchantInfoDto.setMerchantName("123");
        merchantInfoDto.setPrimaryMobileNumber("1234");
        when(merchantProfileService.fetchMerchantDetailsByMid(any())).thenReturn(merchantInfoDto);
        doNothing().when(notificationService).notifyPush(any(), any(), any());
        doNothing().when(notificationService).notifyWhatsappPush(any(), any(), any(), any());
        when(redisHelper.setLastNotificationSentOn(any())).thenReturn("123");
        nudgeApiService.updateNudge(requestMap);
    }

    //@Test
    public void updateNudgeTestNotificationSentIn24hrsQueueExist() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW\",\n" +
                "  \"consultDate\": \"2023-05-12\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        Map<String,Boolean> map = new HashMap<>();
        map.put("ocl.boss.merchant.edc",true);
        when(upsService.getDevicePreferences(anyString())).thenReturn(map);
        when( redisHelper.getLastNotificationSentOn(any())).thenReturn("2023-09-29T12:23:12.707");
        String queue ="{\n" +
                "  \"notification\": {\n" +
                "    \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "    \"identifier\": \"CC_DC\",\n" +
                "    \"consultId\": \"b4dcce5e-29d9-4ba0-9015-98591eec0871\",\n" +
                "    \"status\": \"BLOCK\",\n" +
                "    \"comment\": \"The limit could not be upgraded because of low merchant vintage\",\n" +
                "    \"consultDate\": \"2023-09-21\",\n" +
                "    \"clientId\": \"66c02d3e-ebc0-4117-ba85-7f523ac8d424\",\n" +
                "    \"timestamp\": \"2023-09-21\",\n" +
                "    \"requestPayload\": {\n" +
                "      \"mid\": \"FkxQsb79598246150123\",\n" +
                "      \"triggerValueMonthly\": 90,\n" +
                "      \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "      \"triggerInstrumentMonthly\": \"NA\",\n" +
                "      \"merchantType\": \"QR\",\n" +
                "      \"currentCumulativeLimitMonthly\": 10000000,\n" +
                "      \"currentInstrumentLimitMonthly\": null,\n" +
                "      \"dailyLimitFlag\": false,\n" +
                "      \"currentCumulativeLimitDaily\": 10000000,\n" +
                "      \"currentInstrumentLimitDaily\": null,\n" +
                "      \"triggerValueDaily\": 71.03,\n" +
                "      \"triggerTypeDaily\": \"Cumulative\",\n" +
                "      \"triggerInstrumentDaily\": \"NA\"\n" +
                "    },\n" +
                "    \"responsePayload\": {\n" +
                "      \"newInstrumentMonthlyLimit\": null,\n" +
                "      \"newInstrumentDailyLimit\": null,\n" +
                "      \"newInstrumentTransactionalLimit\": null,\n" +
                "      \"newCumulativeMonthlyLimit\": null,\n" +
                "      \"newCumulativeDailyLimit\": null,\n" +
                "      \"newCumulativeTransactionalLimit\": null,\n" +
                "      \"limitUpgradingClient\": \"66c02d3e-ebc0-4117-ba85-7f523ac8d424\",\n" +
                "      \"message\": \"The limit could not be upgraded because of low merchant vintage\"\n" +
                "    }\n" +
                "  },\n" +
                "\"receivedAt\":\"2023-09-21\"\n" +
                "}";
        when(redisHelper.getQueuedNotification(any())).thenReturn(queue);//queue
        doNothing().when(redisHelper).evictLastSentOnKey(any());
        doNothing().when(redisHelper).evictQueue(any());
        doNothing().when(p4bNudgesDao).addP4BNudge(any(),any(),any(),any(),any(),any(),any(),any(),any(),any());
        MLCLimitVo expectedMLCLimitVo = new MLCLimitVo();
        expectedMLCLimitVo.setIdentifier("UPI");
        MerchantInfoDto merchantInfoDto = new MerchantInfoDto();
        merchantInfoDto.setMerchantName("123");
        merchantInfoDto.setPrimaryMobileNumber("1234");
        when(merchantProfileService.fetchMerchantDetailsByMid(any())).thenReturn(merchantInfoDto);
        doNothing().when(notificationService).notifyPush(any(), any(), any());
        doNothing().when(notificationService).notifyWhatsappPush(any(), any(), any(), any());
        when(redisHelper.setLastNotificationSentOn(any())).thenReturn("123");
        nudgeApiService.updateNudge(requestMap);
    }


    @Test
    public void updateNudgeIfAlreadyExistTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW\",\n" +
                "  \"consultDate\": \"2023-05-12\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        Map<String,Boolean> map = new HashMap<>();
        map.put("ocl.boss.merchant.edc",true);
        when(upsService.getDevicePreferences(anyString())).thenReturn(map);
        when(p4bNudgesDao.getP4BNudge(anyString(), anyString(), anyString())).thenReturn(new P4bNudges());
        doNothing().when(p4bNudgesDao).updateP4BNudgeExpiry(any(),any());
        when( redisHelper.getLastNotificationSentOn(any())).thenReturn("2023-09-29T12:23:12.707");
        when(redisHelper.getQueuedNotification(any())).thenReturn(null);//queue
        doNothing().when(redisHelper).evictLastSentOnKey(any());
        //doNothing().when(redisHelper).evictQueue(any());
        //doNothing().when(p4bNudgesDao).addP4BNudge(any(),any(),any(),any(),any(),any(),any(),any(),any(),any());
        MLCLimitVo expectedMLCLimitVo = new MLCLimitVo();
        expectedMLCLimitVo.setIdentifier("UPI");
        MerchantInfoDto merchantInfoDto = new MerchantInfoDto();
        merchantInfoDto.setMerchantName("123");
        merchantInfoDto.setPrimaryMobileNumber("1234");
        when(merchantProfileService.fetchMerchantDetailsByMid(any())).thenReturn(merchantInfoDto);
        doNothing().when(notificationService).notifyPush(any(), any(), any());
        doNothing().when(notificationService).notifyWhatsappPush(any(), any(), any(), any());
        when(redisHelper.setLastNotificationSentOn(any())).thenReturn("123");
        nudgeApiService.updateNudge(requestMap);
    }

    @Test
    public void updateNudgeStatusMissingTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW reason: merchant PAN is not submitted.\",\n" +
                "  \"consultDate\": \"2023-05-12T13:25:05.364+05:30\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12T13:25:05.362+05:30\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW reason: merchant PAN is not submitted.\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        assertThrows(ValidationException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }

    @Test
    public void updateNudgeFeatureTypeMissingTest() throws Exception {
        String request = "{\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW reason: merchant PAN is not submitted.\",\n" +
                "  \"consultDate\": \"2023-05-12T13:25:05.364+05:30\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12T13:25:05.362+05:30\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW reason: merchant PAN is not submitted.\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        assertThrows(ValidationException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }

    @Test
    public void updateNudgeTypeMissingTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW reason: merchant PAN is not submitted.\",\n" +
                "  \"consultDate\": \"2023-05-12T13:25:05.364+05:30\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12T13:25:05.362+05:30\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW reason: merchant PAN is not submitted.\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        assertThrows(ValidationException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }
    @Test
    public void updateNudgeWrongFeatureTypeTest() throws Exception {
        String request = "{\"featureType\": \"ABCD\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW reason: merchant PAN is not submitted.\",\n" +
                "  \"consultDate\": \"2023-05-12T13:25:05.364+05:30\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12T13:25:05.362+05:30\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW reason: merchant PAN is not submitted.\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        assertThrows(ValidationException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }

    @Test
    public void updateNudgeStatusInvalidTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"TOACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW reason: merchant PAN is not submitted.\",\n" +
                "  \"consultDate\": \"2023-05-12T13:25:05.364+05:30\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12T13:25:05.362+05:30\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW reason: merchant PAN is not submitted.\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        assertThrows(ValidationException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }

    @Test
    public void updateNudgeTTLmissingTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW reason: merchant PAN is not submitted.\",\n" +
                "  \"consultDate\": \"2023-05-12T13:25:05.364+05:30\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12T13:25:05.362+05:30\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW reason: merchant PAN is not submitted.\"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        assertThrows(ValidationException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }

    @Test
    public void updateNudgeResponseUmpExceptionTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW::\",\n" +
                "  \"comment\": \"REVIEW\",\n" +
                "  \"consultDate\": \"2023-05-12\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW \"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        Map<String,Boolean> map = new HashMap<>();
        map.put("ocl.boss.merchant.edc",true);
        //when(upsService.getDevicePreferences(anyString())).thenReturn(map);
        //when( redisHelper.getLastNotificationSentOn(any())).thenReturn(null);
        //doNothing().when(p4bNudgesDao).addP4BNudge(any(),any(),any(),any(),any(),any(),any(),any(),any(),any());
        MerchantInfoDto merchantInfoDto = new MerchantInfoDto();
        merchantInfoDto.setMerchantName("123");
        merchantInfoDto.setPrimaryMobileNumber("1234");
        //when(merchantProfileService.fetchMerchantDetailsByMid(any())).thenReturn(merchantInfoDto);
        //doNothing().when(notificationService).notifyPush(any(), any(), any());
        //doNothing().when(notificationService).notifyWhatsappPush(any(), any(), any(), any());
        //when(redisHelper.setLastNotificationSentOn(any())).thenReturn("123");
        assertThrows(ResponseUmpException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }
    @Test
    public void updateNudgePrimaryMobileNumberIsBlankTest() throws Exception {
        String request = "{\"featureType\": \"LIMIT_UPGRADE\",\n" +
                "\"status\": \"ACTIVE\",\n" +
                "\"mid\":\"FkxQsb79598246150123\",\n" +
                "\"ttl\":30,\n" +
                "\"type\":\"bottomSheet\",\n" +
                "\"metaData\": {\n" +
                "  \"merchantId\": \"FkxQsb79598246150123\",\n" +
                "  \"identifier\": \"UPI\",\n" +
                "  \"consultId\": \"62670652-9282-4588-967c-5930ec4718c2\",\n" +
                "  \"status\": \"REVIEW\",\n" +
                "  \"comment\": \"REVIEW\",\n" +
                "  \"consultDate\": \"2023-05-12\",\n" +
                "  \"clientId\": \"RISK\",\n" +
                "  \"timestamp\": \"2023-05-12\",\n" +
                "  \"requestPayload\": {\n" +
                "    \"mid\": \"a9801\",\n" +
                "    \"triggerValueMonthly\": 90,\n" +
                "    \"triggerTypeMonthly\": \"Cumulative\",\n" +
                "    \"triggerInstrumentMonthly\": \"NA\",\n" +
                "    \"merchantType\": \"ONLINE\",\n" +
                "    \"currentCumulativeLimitMonthly\": -1,\n" +
                "    \"currentInstrumentLimitMonthly\": null,\n" +
                "    \"dailyLimitFlag\": false,\n" +
                "    \"currentCumulativeLimitDaily\": -1,\n" +
                "    \"currentInstrumentLimitDaily\": null,\n" +
                "    \"triggerValueDaily\": 0.0,\n" +
                "    \"triggerTypeDaily\": \"Cumulative\",\n" +
                "    \"triggerInstrumentDaily\": \"NA\"\n" +
                "  },\n" +
                "  \"responsePayload\": {\n" +
                "    \"newInstrumentMonthlyLimit\": null,\n" +
                "    \"newInstrumentDailyLimit\": null,\n" +
                "    \"newInstrumentTransactionalLimit\": null,\n" +
                "    \"newCumulativeMonthlyLimit\": null,\n" +
                "    \"newCumulativeDailyLimit\": null,\n" +
                "    \"newCumulativeTransactionalLimit\": null,\n" +
                "    \"limitUpgradingClient\": \"RISK\",\n" +
                "    \"message\": \"REVIEW \"\n" +
                "  }\n" +
                "}\n" +
                "\n" +
                "\n" +
                "}";
        ObjectMapper mapper = new ObjectMapper();
        Map<String,Object> requestMap = mapper.readValue(request,Map.class);
        Map<String,Boolean> map = new HashMap<>();
        map.put("ocl.boss.merchant.edc",true);
        when(upsService.getDevicePreferences(anyString())).thenReturn(map);
        when( redisHelper.getLastNotificationSentOn(any())).thenReturn(null);
        doNothing().when(p4bNudgesDao).addP4BNudge(any(),any(),any(),any(),any(),any(),any(),any(),any(),any());
        MerchantInfoDto merchantInfoDto = new MerchantInfoDto();
        merchantInfoDto.setMerchantName("123");
        merchantInfoDto.setPrimaryMobileNumber("");
        when(merchantProfileService.fetchMerchantDetailsByMid(any())).thenReturn(merchantInfoDto);
        //doNothing().when(notificationService).notifyPush(any(), any(), any());
        //doNothing().when(notificationService).notifyWhatsappPush(any(), any(), any(), any());
        //when(redisHelper.setLastNotificationSentOn(any())).thenReturn("123");
        assertThrows(ResponseUmpException.class, () -> {
            nudgeApiService.updateNudge(requestMap);
        });
    }
}