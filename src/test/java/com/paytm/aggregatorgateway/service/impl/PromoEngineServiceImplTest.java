package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.PromoRequestVO;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;
import org.mockito.ArgumentMatchers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class PromoEngineServiceImplTest {

    private Authentication authentication;

    @Mock
    private Environment commonproperties;

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @InjectMocks
    private PromoEngineServiceImpl promoEngineService;

    @BeforeEach
    public void init() throws Exception{
        MockitoAnnotations.openMocks(this);

        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        ReflectionTestUtils.setField(promoEngineService, "objectMapper", new ObjectMapper());
        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put(AWSSecrets.PROMO_ENGINE_SECRET_KEY.getValue(), "80879ypiyuyidnijlkjnDo2780hILxdvvQXu9sh");
    }

    @Test
    public void testGetGameList() throws Exception{
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        requestVO.setStatus("Yes");
        requestVO.setPage_number(1);
        requestVO.setPage_offset(1);
        requestVO.setPage_size(1);
        requestVO.setAfter_id("11");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.getGameList(requestVO);
        assertEquals(mockResponse,res);
    }

    @Test
	public void testGetGameListInvalid() throws InterruptedException, Exception {
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
            promoEngineService.getGameList(requestVO);
        });
    }

    @Test
    public void testGetGameListV2() throws Exception{
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        requestVO.setStatus("Yes");
        requestVO.setPage_number(1);
        requestVO.setPage_offset(1);
        requestVO.setPage_size(1);
        requestVO.setAfter_id("11");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.getGameListV2(requestVO);
        assertEquals(mockResponse,res);
    }

    @Test
	public void testGetGameListV2Invalid() throws InterruptedException, Exception {
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
            promoEngineService.getGameListV2(requestVO);
        });
    }
    @Test
    public void testGetTxnDetail() throws Exception{
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        requestVO.setGame_id("1");
        requestVO.setMerchant_id("mid");
        requestVO.setOldest_txn_time("1");
        requestVO.setStage("stage");
        requestVO.setPage_size(1);
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.getTxnDetail(requestVO);
        assertEquals(mockResponse,res);
    }

    @Test
	public void testGetTxnDetailInvalid() throws InterruptedException, Exception {
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
            promoEngineService.getTxnDetail(requestVO);
        });
    }

    @Test
    public void testGetAllOffers() throws Exception{
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        requestVO.setMerchant_id("Yes");
        requestVO.setPage_number(1);
        requestVO.setPage_offset(1);
        requestVO.setPage_size(1);
        requestVO.setAfter_id("11");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.getAllOffers(requestVO);
        assertEquals(mockResponse,res);
    }

    @Test
	public void testGetAllOffersInvalid() throws InterruptedException, Exception {
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
            promoEngineService.getAllOffers(requestVO);
        });
    }

    @Test
    public void testActivateOffer() throws Exception{
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        requestVO.setMerchant_id("Yes");
        requestVO.setCampaign_id("11");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.activateOffer(requestVO);
        assertEquals(mockResponse,res);
    }

    @Test
    public void testActivateOfferInvalid() throws Exception {
        PromoRequestVO requestVO=new PromoRequestVO();
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
            promoEngineService.activateOffer(requestVO);
        });
    }

    @Test
    public void testGetCampaignGameV2() throws Exception{
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.getCampaignGameV2("123","456");
        assertEquals(mockResponse,res);
    }

    @Test
    public void testGetCampaignGameV2Invalid() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
                    promoEngineService.getCampaignGameV2("123", "456");
        });
    }

    @Test
    public void testSelectOffer() throws Exception{
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.selectOffer("123","456","789");
        assertEquals(mockResponse,res);
    }

    @Test
    public void testGameDetails() throws Exception{
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        String res=promoEngineService.gameDetails("123","456");
        assertEquals(mockResponse,res);
    }

    @Test
    public void testGameDetailsInvalid() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
            promoEngineService.gameDetails("123","456");
        });
    }

    @Test
    public void testCampaignGames() throws Exception{
        UtsHelper.mockUserAuthentication(authentication);
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("scret-key");
        when(commonproperties.getProperty(anyString())).thenReturn("scret-key");
        String mockResponse="123";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(mockResponse,HttpStatus.OK));
        Map<String,Object> res=promoEngineService.campaignGames();
        assertEquals(mockResponse,res.get("campaignsList").toString());
        assertEquals(mockResponse,res.get("gameList").toString());
    }

    @Test
    public void activeGamesTestInvalid() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("xyz");
        when(commonproperties.getProperty(anyString())).thenReturn("abc");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenThrow(ValidationException.class);
        assertThrows(ValidationException.class, () -> {
            promoEngineService.fetchActiveGames(new HashMap<>(), "abc", "xyz");
        });
    }

    @Test
    public void activeGamesTest1() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("xyz");
        when(commonproperties.getProperty(anyString())).thenReturn("abc");
        String downstreamResponse = "{\"status\":1,\"errors\":[],\"data\":{\"tags_data\":{\"sb_lp_offers\":{\"campaign_list\":[{\"id\":1159472,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"TEST\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1159472\",\"campaign\":\"TEST_EXPIRY_MER_DAY01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T10:46:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_EXPIRY_MER_DAY01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1159472\"},{\"id\":1186236,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186236\",\"campaign\":\"TEST_MERCHANTPERFPG01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T18:09:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANTPERFPG01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186236\"},{\"id\":1187176,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"TEST\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1187176\",\"campaign\":\"TEST_MAKERCHECKER_01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-28T10:29:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MAKERCHECKER_01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1187176\"},{\"id\":1186700,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186700\",\"campaign\":\"TEST_MERCHANTWALLET\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T10:32:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANTWALLET\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186700\"},{\"id\":1186701,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186701\",\"campaign\":\"TEST_MERCHANT_WALLET01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T10:36:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANT_WALLET01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186701\"},{\"id\":1092821,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092821\",\"campaign\":\"AUTO_MERCHANTWALLET01\",\"isDeeplink\":false,\"valid_upto\":\"2024-01-09T12:37:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_MERCHANTWALLET01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092821\"},{\"id\":1092537,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092537\",\"campaign\":\"AUTO_MERCHANTPERFPG02\",\"isDeeplink\":false,\"valid_upto\":\"2024-01-08T10:35:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_MERCHANTPERFPG02\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092537\"},{\"id\":1186235,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Test\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186235\",\"campaign\":\"TEST_MERCHANTPERFPG\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T17:58:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANTPERFPG\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186235\"},{\"id\":1020286,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1020286\",\"campaign\":\"AUTO_MERCHANTPERFPG01\",\"isDeeplink\":false,\"valid_upto\":\"2024-07-31T08:00:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_MERCHANTPERFPG01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1020286\"}],\"games_list\":[{\"offer_id\":83479,\"stage\":0,\"created_at\":\"2023-01-30T06:32:51.000Z\",\"success_txn_count\":0,\"game_status\":\"INPROGRESS\",\"updated_at\":\"2023-01-30T06:32:51.000Z\",\"total_txn_count\":7,\"campaign\":{\"id\":28915,\"deeplink_url\":\"AUTO_CLM_MYOFFERClubStage\",\"offer_text_override\":\"AUTO_CLM_MYOFFERClubStage\",\"background_image_url\":\"AUTO_CLM_MYOFFERClubStage\",\"new_offers_image_url\":\"AUTO_CLM_MYOFFERClubStage\",\"offer_keyword\":\"Payment\",\"important_terms\":\"<p>AUTO_CLM_MYOFFERClubStage</p>\\n\",\"surprise_text\":\"AUTO_CLM_MYOFFERClubStage\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\",\"campaign\":\"AUTO_CLM_MYOFFERCLUBSTAGE\",\"isDeeplink\":true,\"valid_upto\":\"2024-07-31T08:47:00.000Z\",\"short_description\":\"AUTO_CLM_MYOFFERClubStage\",\"auto_activate\":true,\"cashback_process_delay\":\"5\",\"cashback_process_delay_unit\":\"1\",\"description\":\"AUTO_CLM_MYOFFERClubStage\",\"is_offus_transaction\":true,\"off_us_transaction_text\":\"\",\"offer_icon_override_url\":\"AUTO_CLM_MYOFFERClubStage\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_CLM_MYOFFERCLUBSTAGE\",\"title\":\"AUTO_CLM_MYOFFERClubStage\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\"},\"stages\":[{\"stage\":[0,1],\"stage_total_txn_count\":3,\"stage_success_txn_count\":0,\"stage_status\":\"NOT_STARTED\",\"tasks\":[{\"frontend_redemption_type\":\"bank account cashback\",\"cap_bonus_amount\":4000,\"bonus_amount_earned\":0,\"redemption_type_icon_url\":\"https://assetscdn.paytm.com/images/promo-engine/prod/promo_seller_panel_uploads/Cashback.png\",\"gratification_processed\":false,\"stage_redemption_type\":\"bank account cashback\",\"gratification_type_flat\":\"percentage\",\"crosspromo_data\":[]}],\"stage_gratification_text\":\"You will win upto ₹4000 bank account cashback\",\"stage_screen_construct1\":\"1st - 3rd payment\"},{\"stage\":[2],\"stage_total_txn_count\":2,\"stage_success_txn_count\":0,\"stage_status\":\"NOT_STARTED\",\"tasks\":[{\"frontend_redemption_type\":\"bank account cashback\",\"cap_bonus_amount\":20,\"bonus_amount_earned\":0,\"redemption_type_icon_url\":\"https://assetscdn.paytm.com/images/promo-engine/prod/promo_seller_panel_uploads/Cashback.png\",\"gratification_processed\":false,\"stage_redemption_type\":\"bank account cashback\",\"gratification_type_flat\":\"percentage\",\"crosspromo_data\":[]}],\"stage_gratification_text\":\"You will win upto ₹20 bank account cashback\",\"stage_screen_construct1\":\"4th - 5th payment\"},{\"stage\":[3],\"stage_total_txn_count\":2,\"stage_success_txn_count\":0,\"stage_status\":\"NOT_STARTED\",\"tasks\":[{\"frontend_redemption_type\":\"bank account cashback\",\"cap_bonus_amount\":10,\"bonus_amount_earned\":0,\"redemption_type_icon_url\":\"https://assetscdn.paytm.com/images/promo-engine/prod/promo_seller_panel_uploads/Cashback.png\",\"gratification_processed\":false,\"stage_redemption_type\":\"bank account cashback\",\"gratification_type_flat\":\"absolute\",\"crosspromo_data\":[]}],\"stage_gratification_text\":\"You will win ₹10 bank account cashback\",\"stage_screen_construct1\":\"6th - 7th payment\"}],\"game_expiry\":\"2023-02-01T18:29:59.000Z\",\"game_completion_time\":\"2023-01-30T06:32:51.000Z\",\"max_cap_bonus_amount_game\":4030,\"bonus_amount_earned\":0,\"status\":2,\"remaining_time\":\"1 day left to expire\",\"offer_remaining_time\":\"0 seconds left to activate offer\",\"txn_count_text\":\"0/7 Payment Received\",\"success_txn_text\":\"0 Payment done\",\"offer_expiry\":\"2023-01-30T06:33:51.000Z\",\"initialized_offer_text\":\"Activate offer and accept 6 more transaction to earn upto ₹4030 \",\"offer_progress_construct\":\"You are 3 payment away from earning upto ₹4000 bank account cashback\"}]}}}}\"";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(downstreamResponse,HttpStatus.OK));
        Map<String, Object> result = promoEngineService.fetchActiveGames(new HashMap<>(), "abc", "xyz");
        Map<String, Object> sbLpOffers = (Map<String, Object>) result.get("sb_lp_offers");
        Map<String, Object> gamesObject = (Map<String, Object>) sbLpOffers.get("games_object");
        Map<String, Object> campaignObject = (Map<String, Object>) sbLpOffers.get("campaign_object");
        assertFalse(gamesObject.isEmpty());
        assertTrue(campaignObject.isEmpty());
    }

    @Test
    public void activeGamesTest2() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("xyz");
        when(commonproperties.getProperty(anyString())).thenReturn("abc");
        String downstreamResponse = "{\"status\":1,\"errors\":[],\"data\":{\"tags_data\":{\"sb_lp_offers\":{\"campaign_list\":[{\"id\":1159472,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"TEST\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1159472\",\"campaign\":\"TEST_EXPIRY_MER_DAY01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T10:46:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_EXPIRY_MER_DAY01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1159472\"},{\"id\":1186236,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186236\",\"campaign\":\"TEST_MERCHANTPERFPG01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T18:09:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANTPERFPG01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186236\"},{\"id\":1187176,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"TEST\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1187176\",\"campaign\":\"TEST_MAKERCHECKER_01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-28T10:29:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MAKERCHECKER_01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1187176\"},{\"id\":1186700,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186700\",\"campaign\":\"TEST_MERCHANTWALLET\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T10:32:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANTWALLET\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186700\"},{\"id\":1186701,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186701\",\"campaign\":\"TEST_MERCHANT_WALLET01\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T10:36:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANT_WALLET01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186701\"},{\"id\":1092821,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092821\",\"campaign\":\"AUTO_MERCHANTWALLET01\",\"isDeeplink\":false,\"valid_upto\":\"2024-01-09T12:37:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_MERCHANTWALLET01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092821\"},{\"id\":1092537,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092537\",\"campaign\":\"AUTO_MERCHANTPERFPG02\",\"isDeeplink\":false,\"valid_upto\":\"2024-01-08T10:35:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_MERCHANTPERFPG02\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1092537\"},{\"id\":1186235,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Test\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186235\",\"campaign\":\"TEST_MERCHANTPERFPG\",\"isDeeplink\":false,\"valid_upto\":\"2023-02-04T17:58:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"TEST_MERCHANTPERFPG\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1186235\"},{\"id\":1020286,\"offer_text_override\":\"vdndfk\",\"background_image_url\":\"Merge_Gaurav76\",\"new_offers_image_url\":\"vdsnlfw\",\"offer_keyword\":\"hwei\",\"important_terms\":\"<p>fweuih</p>\\n\",\"surprise_text\":\"bjkq\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1020286\",\"campaign\":\"AUTO_MERCHANTPERFPG01\",\"isDeeplink\":false,\"valid_upto\":\"2024-07-31T08:00:00.000Z\",\"short_description\":\"snmabs\",\"auto_activate\":true,\"cashback_process_delay\":\"10\",\"cashback_process_delay_unit\":\"1\",\"description\":\"cnajkn\",\"is_offus_transaction\":false,\"off_us_transaction_text\":\"fbaueh\",\"offer_icon_override_url\":\"csdknj\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_MERCHANTPERFPG01\",\"title\":\"ckjdw\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1020286\"},{\"id\":28915,\"deeplink_url\":\"AUTO_CLM_MYOFFERClubStage\",\"offer_text_override\":\"AUTO_CLM_MYOFFERClubStage\",\"background_image_url\":\"AUTO_CLM_MYOFFERClubStage\",\"new_offers_image_url\":\"AUTO_CLM_MYOFFERClubStage\",\"offer_keyword\":\"Payment\",\"important_terms\":\"<p>AUTO_CLM_MYOFFERClubStage</p>\\n\",\"surprise_text\":\"AUTO_CLM_MYOFFERClubStage\",\"tnc\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\",\"campaign\":\"AUTO_CLM_MYOFFERCLUBSTAGE\",\"isDeeplink\":true,\"valid_upto\":\"2024-07-31T08:47:00.000Z\",\"short_description\":\"AUTO_CLM_MYOFFERClubStage\",\"auto_activate\":true,\"cashback_process_delay\":\"5\",\"cashback_process_delay_unit\":\"1\",\"description\":\"AUTO_CLM_MYOFFERClubStage\",\"is_offus_transaction\":true,\"off_us_transaction_text\":\"\",\"offer_icon_override_url\":\"AUTO_CLM_MYOFFERClubStage\",\"offer_type_text\":\"\",\"promocode\":\"AUTO_CLM_MYOFFERCLUBSTAGE\",\"title\":\"AUTO_CLM_MYOFFERClubStage\",\"tnc_url\":\"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\"}],\"games_list\":[]}}}}\"";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(downstreamResponse,HttpStatus.OK));
        Map<String, Object> result = promoEngineService.fetchActiveGames(new HashMap<>(), "abc", "xyz");
        Map<String, Object> sbLpOffers = (Map<String, Object>) result.get("sb_lp_offers");
        Map<String, Object> gamesObject = (Map<String, Object>) sbLpOffers.get("games_object");
        Map<String, Object> campaignObject = (Map<String, Object>) sbLpOffers.get("campaign_object");
        assertTrue(gamesObject.isEmpty());
        assertFalse(campaignObject.isEmpty());
    }

    @Test
    public void activeGamesTest3() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("xyz");
        when(commonproperties.getProperty(anyString())).thenReturn("abc");
        String downstreamResponse = "{\n" +
                "   \"status\": 1,\n" +
                "   \"errors\": [],\n" +
                "   \"data\": {\n" +
                "      \"tags_data\": {\n" +
                "         \"sb_lp_offers\": {\n" +
                "            \"campaign_list\": [],\n" +
                "            \"games_list\": []\n" +
                "         }\n" +
                "      }\n" +
                "   }\n" +
                "}";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>(downstreamResponse,HttpStatus.OK));
        Map<String, Object> result = promoEngineService.fetchActiveGames(new HashMap<>(), "abc", "xyz");
        Map<String, Object> sbLpOffers = (Map<String, Object>) result.get("sb_lp_offers");
        Map<String, Object> gamesObject = (Map<String, Object>) sbLpOffers.get("games_object");
        Map<String, Object> campaignObject = (Map<String, Object>) sbLpOffers.get("campaign_object");
        assertTrue(gamesObject.isEmpty());
        assertTrue(campaignObject.isEmpty());
    }

    @Test
    public void activeGamesTest4() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("xyz");
        when(commonproperties.getProperty(anyString())).thenReturn("abc");
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),any())).thenReturn(new ResponseEntity<>("downstreamResponse",HttpStatus.INTERNAL_SERVER_ERROR));
        assertThrows(RuntimeException.class, () -> {
            promoEngineService.fetchActiveGames(new HashMap<>(), "abc", "xyz");
        });
    }

    @Test
    public void activeGamesTest5() throws Exception {
        when(commonproperties.getRequiredProperty(anyString())).thenReturn("xyz");
        when(commonproperties.getProperty(anyString())).thenReturn("abc");
        String downstreamResponse = "{\n" +
                "   \"status\": 1,\n" +
                "   \"errors\": [],\n" +
                "   \"data\": {\n" +
                "      \"tags_data\": {\n" +
                "         \"edc_lp_offers\": {\n" +
                "            \"campaign_list\": [],\n" +
                "            \"games_list\": []\n" +
                "         }\n" +
                "      }\n" +
                "   }\n" +
                "}";
        when(restProcessorDelegate.executePromoRequestHystrix(anyString(),any(),any(),any(),any(),ArgumentMatchers.<Class<String>>any())).thenReturn(new ResponseEntity<>(downstreamResponse,HttpStatus.OK));
        Map<String, Object> result = promoEngineService.fetchActiveGames(new HashMap<>(), "abc", "xyz");
        Map<String, Object> sbLpOffers = (Map<String, Object>) result.get("edc_lp_offers");
        List<Map<String, Object>> gamesObject = (List<Map<String, Object>>) sbLpOffers.get("games_object");
        List<Map<String, Object>> campaignObject = (List<Map<String, Object>>) sbLpOffers.get("campaign_object");
        assertTrue(gamesObject.isEmpty());
        assertTrue(campaignObject.isEmpty());
    }

    private String fetchMockResponse() {
        return "{\n" +
                "  \"status\": 1,\n" +
                "  \"errors\": [],\n" +
                "  \"data\": {\n" +
                "    \"tags_data\": {\n" +
                "      \"sb_lp_offers\": {\n" +
                "        \"campaign_list\": [\n" +
                "          {\n" +
                "            \"id\": 1020286,\n" +
                "            \"offer_text_override\": \"vdndfk\",\n" +
                "            \"background_image_url\": \"Merge_Gaurav76\",\n" +
                "            \"new_offers_image_url\": \"vdsnlfw\",\n" +
                "            \"offer_keyword\": \"hwei\",\n" +
                "            \"important_terms\": \"<p>fweuih</p>\\n\",\n" +
                "            \"surprise_text\": \"bjkq\",\n" +
                "            \"tnc\": \"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1020286\",\n" +
                "            \"campaign\": \"AUTO_MERCHANTPERFPG01\",\n" +
                "            \"isDeeplink\": false,\n" +
                "            \"valid_upto\": \"2024-07-31T08:00:00.000Z\",\n" +
                "            \"short_description\": \"snmabs\",\n" +
                "            \"auto_activate\": true,\n" +
                "            \"cashback_process_delay\": \"10\",\n" +
                "            \"cashback_process_delay_unit\": \"1\",\n" +
                "            \"description\": \"cnajkn\",\n" +
                "            \"is_offus_transaction\": false,\n" +
                "            \"off_us_transaction_text\": \"fbaueh\",\n" +
                "            \"offer_icon_override_url\": \"csdknj\",\n" +
                "            \"offer_type_text\": \"\",\n" +
                "            \"promocode\": \"AUTO_MERCHANTPERFPG01\",\n" +
                "            \"title\": \"ckjdw\",\n" +
                "            \"tnc_url\": \"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/1020286\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"games_list\": [\n" +
                "          {\n" +
                "            \"offer_id\": 83479,\n" +
                "            \"stage\": 0,\n" +
                "            \"created_at\": \"2023-01-30T06:32:51.000Z\",\n" +
                "            \"success_txn_count\": 0,\n" +
                "            \"game_status\": \"INPROGRESS\",\n" +
                "            \"updated_at\": \"2023-01-30T06:32:51.000Z\",\n" +
                "            \"total_txn_count\": 7,\n" +
                "            \"campaign\": {\n" +
                "              \"id\": 28915,\n" +
                "              \"deeplink_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"offer_text_override\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"background_image_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"new_offers_image_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"offer_keyword\": \"Payment\",\n" +
                "              \"important_terms\": \"<p>AUTO_CLM_MYOFFERClubStage</p>\\n\",\n" +
                "              \"surprise_text\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"tnc\": \"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\",\n" +
                "              \"campaign\": \"AUTO_CLM_MYOFFERCLUBSTAGE\",\n" +
                "              \"isDeeplink\": true,\n" +
                "              \"valid_upto\": \"2024-07-31T08:47:00.000Z\",\n" +
                "              \"short_description\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"auto_activate\": true,\n" +
                "              \"cashback_process_delay\": \"5\",\n" +
                "              \"cashback_process_delay_unit\": \"1\",\n" +
                "              \"description\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"is_offus_transaction\": true,\n" +
                "              \"off_us_transaction_text\": \"\",\n" +
                "              \"offer_icon_override_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"offer_type_text\": \"\",\n" +
                "              \"promocode\": \"AUTO_CLM_MYOFFERCLUBSTAGE\",\n" +
                "              \"title\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "              \"tnc_url\": \"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\"\n" +
                "            },\n" +
                "            \"stages\": [\n" +
                "              {\n" +
                "                \"stage\": [\n" +
                "                  0,\n" +
                "                  1\n" +
                "                ],\n" +
                "                \"stage_total_txn_count\": 3,\n" +
                "                \"stage_success_txn_count\": 0,\n" +
                "                \"stage_status\": \"NOT_STARTED\",\n" +
                "                \"tasks\": [\n" +
                "                  {\n" +
                "                    \"frontend_redemption_type\": \"bank account cashback\",\n" +
                "                    \"cap_bonus_amount\": 4000,\n" +
                "                    \"bonus_amount_earned\": 0,\n" +
                "                    \"redemption_type_icon_url\": \"https://assetscdn.paytm.com/images/promo-engine/prod/promo_seller_panel_uploads/Cashback.png\",\n" +
                "                    \"gratification_processed\": false,\n" +
                "                    \"stage_redemption_type\": \"bank account cashback\",\n" +
                "                    \"gratification_type_flat\": \"percentage\",\n" +
                "                    \"crosspromo_data\": []\n" +
                "                  }\n" +
                "                ],\n" +
                "                \"stage_gratification_text\": \"You will win upto ₹4000 bank account cashback\",\n" +
                "                \"stage_screen_construct1\": \"1st - 3rd payment\"\n" +
                "              },\n" +
                "              {\n" +
                "                \"stage\": [\n" +
                "                  2\n" +
                "                ],\n" +
                "                \"stage_total_txn_count\": 2,\n" +
                "                \"stage_success_txn_count\": 0,\n" +
                "                \"stage_status\": \"NOT_STARTED\",\n" +
                "                \"tasks\": [\n" +
                "                  {\n" +
                "                    \"frontend_redemption_type\": \"bank account cashback\",\n" +
                "                    \"cap_bonus_amount\": 20,\n" +
                "                    \"bonus_amount_earned\": 0,\n" +
                "                    \"redemption_type_icon_url\": \"https://assetscdn.paytm.com/images/promo-engine/prod/promo_seller_panel_uploads/Cashback.png\",\n" +
                "                    \"gratification_processed\": false,\n" +
                "                    \"stage_redemption_type\": \"bank account cashback\",\n" +
                "                    \"gratification_type_flat\": \"percentage\",\n" +
                "                    \"crosspromo_data\": []\n" +
                "                  }\n" +
                "                ],\n" +
                "                \"stage_gratification_text\": \"You will win upto ₹20 bank account cashback\",\n" +
                "                \"stage_screen_construct1\": \"4th - 5th payment\"\n" +
                "              },\n" +
                "              {\n" +
                "                \"stage\": [\n" +
                "                  3\n" +
                "                ],\n" +
                "                \"stage_total_txn_count\": 2,\n" +
                "                \"stage_success_txn_count\": 0,\n" +
                "                \"stage_status\": \"NOT_STARTED\",\n" +
                "                \"tasks\": [\n" +
                "                  {\n" +
                "                    \"frontend_redemption_type\": \"bank account cashback\",\n" +
                "                    \"cap_bonus_amount\": 10,\n" +
                "                    \"bonus_amount_earned\": 0,\n" +
                "                    \"redemption_type_icon_url\": \"https://assetscdn.paytm.com/images/promo-engine/prod/promo_seller_panel_uploads/Cashback.png\",\n" +
                "                    \"gratification_processed\": false,\n" +
                "                    \"stage_redemption_type\": \"bank account cashback\",\n" +
                "                    \"gratification_type_flat\": \"absolute\",\n" +
                "                    \"crosspromo_data\": []\n" +
                "                  }\n" +
                "                ],\n" +
                "                \"stage_gratification_text\": \"You will win ₹10 bank account cashback\",\n" +
                "                \"stage_screen_construct1\": \"6th - 7th payment\"\n" +
                "              }\n" +
                "            ],\n" +
                "            \"game_expiry\": \"2023-02-01T18:29:59.000Z\",\n" +
                "            \"game_completion_time\": \"2023-01-30T06:32:51.000Z\",\n" +
                "            \"max_cap_bonus_amount_game\": 4030,\n" +
                "            \"bonus_amount_earned\": 0,\n" +
                "            \"status\": 2,\n" +
                "            \"remaining_time\": \"1 day left to expire\",\n" +
                "            \"offer_remaining_time\": \"0 seconds left to activate offer\",\n" +
                "            \"txn_count_text\": \"0/7 Payment Received\",\n" +
                "            \"success_txn_text\": \"0 Payment done\",\n" +
                "            \"offer_expiry\": \"2023-01-30T06:33:51.000Z\",\n" +
                "            \"initialized_offer_text\": \"Activate offer and accept 6 more transaction to earn upto ₹4030 \",\n" +
                "            \"offer_progress_construct\": \"You are 3 payment away from earning upto ₹4000 bank account cashback\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }

    private String fetchMockReponseWithoutGameList() {
        return "{\n" +
                "  \"status\": 1,\n" +
                "  \"errors\": [],\n" +
                "  \"data\": {\n" +
                "    \"tags_data\": {\n" +
                "      \"sb_lp_offers\": {\n" +
                "        \"campaign_list\": [\n" +
                "          {\n" +
                "            \"id\": 28915,\n" +
                "            \"deeplink_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"offer_text_override\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"background_image_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"new_offers_image_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"offer_keyword\": \"Payment\",\n" +
                "            \"important_terms\": \"<p>AUTO_CLM_MYOFFERClubStage</p>\\n\",\n" +
                "            \"surprise_text\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"tnc\": \"htatps://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\",\n" +
                "            \"campaign\": \"AUTO_CLM_MYOFFERCLUBSTAGE\",\n" +
                "            \"isDeeplink\": true,\n" +
                "            \"valid_upto\": \"2025-07-31T08:47:00.000Z\",\n" +
                "            \"short_description\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"auto_activate\": true,\n" +
                "            \"cashback_process_delay\": \"5\",\n" +
                "            \"cashback_process_delay_unit\": \"1\",\n" +
                "            \"description\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"is_offus_transaction\": true,\n" +
                "            \"off_us_transaction_text\": \"\",\n" +
                "            \"offer_icon_override_url\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"offer_type_text\": \"\",\n" +
                "            \"promocode\": \"AUTO_CLM_MYOFFERCLUBSTAGE\",\n" +
                "            \"title\": \"AUTO_CLM_MYOFFERClubStage\",\n" +
                "            \"tnc_url\": \"https://gateway.paytm.com/papi/v2/promocard/supercash/campaign/tnc/28915\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"games_list\": []\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }

}
