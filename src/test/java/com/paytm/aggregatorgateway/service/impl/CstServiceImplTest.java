package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dto.FAQVideosDto;
import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CstServiceImplTest {

	@Mock
	private RestProcessorDelegate restProcessorDelegate;

	@Mock
	private ObjectMapper objectMapper;

	@Mock
	private Environment commonProperties;

	@InjectMocks
	private CstServiceImpl cstServiceImpl;

	private Authentication authentication;

	@Mock
	AWSSecretManager awsSecretManager;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		ReflectionTestUtils.setField(cstServiceImpl, "objectMapper", new ObjectMapper());
		HashSet<String> l1IssueCategorySB = new HashSet<>();
		l1IssueCategorySB.add("SB Damaged/Lost");
		l1IssueCategorySB.add("SB Deactivation Request");
		l1IssueCategorySB.add("SB Dynamic QR+");
		ReflectionTestUtils.setField(cstServiceImpl, "l1IssueCategorySB", l1IssueCategorySB);
		Map<String, String> awsSecretsMapMock = new HashMap<>();
		awsSecretsMapMock.put(AWSSecrets.CST_MGW_TOKEN.getValue(), "BearereyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VfiIn5qOIgpg8qJvXNX4d3ZwNMOGnudt9CTO4kFDwptZXdge07fQ_G3jUmcLbKloU7Z6JkYPyNEkMbPdGx5KIudy_WhUc4cTpNAWXwJcDYo9mPYW8oQIuBf-XaelrdmAYcS-uwP6O3VbK0DaU7fis941ybNWhd-zTHxfQuW68-K32dH1G71Dk_LShKJmzx-yW1ECvWVy0XfRXg0E9vxgLyw7LJqpwx0Oij_zVOzm6MEjVcQAcjxAec6ixB0HguDnr7d_QlX6hpBfLwVvi9S8MJof0XEMEp6X4me4cxc_Ei3RtUf_TYMVUzbMcW55uMk7AchYQePyMF5IUoFD9Q_9AA");
		awsSecretsMapMock.put(AWSSecrets.CST_SECRET_KEY.getValue(), "JlWk5ypip5ZJtehF1dod0bwL35hxroypoT9nbuibSegTx851Hdo2Pjb7X2GYQQD2hfqPeQWFW1D/6qFMqxbZwA==");
		awsSecretsMapMock.put(AWSSecrets.CST_CALL_SERVICE_SECRET_KEY.getValue(), "bSegTx851Hdo2Pjb7X2GYQQD2hfqPeQWFW1D/6qFMqxbZwA==");
		ReflectionTestUtils.setField(awsSecretManager, "awsSecretsMap", awsSecretsMapMock);
	}

	@Test
	public void getTrendingTopicsTestMissingParams() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		when(commonProperties.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.BAD_REQUEST));

		assertThrows(ValidationException.class, () -> {
			cstServiceImpl.getTrendingTopics("", "", "7", null);
		});
	}

	@Test
	public void getTrendingTopicsTestCSTFailure() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		when(commonProperties.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.BAD_REQUEST));

		assertThrows(RuntimeException.class, () -> {
			cstServiceImpl.getTrendingTopics("lang", "src", "7", "");
		});
	}


	@Test
	public void getTrendingTopicsTestMissingVideoWidget() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		when(commonProperties.getRequiredProperty(anyString())).thenReturn("mockProperty");
		Mockito.when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(), any(), any())).thenReturn(new ResponseEntity<>(getMockResponseForTrendingTopicsMissingWidget(), HttpStatus.OK));

		ResponseUmp response = cstServiceImpl.getTrendingTopics("lang", "src", "7", "");
		assertNotNull(response.getResults());
		List<Object> faqsList = MappingUtils.convertObjectToType(response.getResults(), List.class);
		assertTrue(faqsList.isEmpty());
	}

	@Test
	public void getTrendingTopicsTestVideoWidgetsPresent() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		when(commonProperties.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(), any(), any())).thenReturn(new ResponseEntity<>(getMockResponseForTrendingTopicsWidgetPresent(), HttpStatus.OK));

		ResponseUmp response = cstServiceImpl.getTrendingTopics("lang", "src", "7", "");
		assertNotNull(response.getResults());
		List<FAQVideosDto> faqsList = MappingUtils.convertObjectToType(response.getResults(), List.class);
		assertEquals(2, faqsList.size());
		FAQVideosDto faq1 = MappingUtils.convertObjectToType(faqsList.get(0), FAQVideosDto.class);
		FAQVideosDto faq2 = MappingUtils.convertObjectToType(faqsList.get(1), FAQVideosDto.class);

		assertEquals("https://www.youtube.com/embed/NFFFONqFYOw", faq1.getSrc());
		assertEquals("https://business.paytm.com/s3assets/images/sb_my_devices/sb_4.jpg", faq1.getThumbnail());
		assertEquals("Static Yellow Light", faq1.getTitle());
		assertEquals("https://www.youtube.com/embed/hDA2_nkoWSs", faq2.getSrc());
		assertEquals("https://business.paytm.com/s3assets/images/sb_my_devices/sb_1.jpg", faq2.getThumbnail());
		assertEquals("Static Red Light", faq2.getTitle());
	}

    private String getMockResponseForTrendingTopicsWidgetPresent() {
        return "{\n" +
                "  \"response\": {\n" +
                "    \"template\": [\n" +
                "     {\n" +
                "        \"card\": [\n" +
                "          {\n" +
                "           \"tile\": [\n" +
                "              {\n" +
                "              \"widget\": [\n" +
                "                  {\n" +
                "                    \"id\": \"4_4_14_21_1229\",\n" +
                "                    \"type\": \"video\",\n" +
                "                    \"dataType\": \"static\",\n" +
                "                    \"displayOrder\": 5,\n" +
                "                    \"metaData\": {\n" +
                "                      \"video1\": \"https://www.youtube.com/embed/NFFFONqFYOw\",\n" +
                "                      \"video2\": \"Static Yellow Light\",\n" +
                "                      \"video5\": \"Static Red Light\",\n" +
                "                      \"video6\": \"https://business.paytm.com/s3assets/images/sb_my_devices/sb_1.jpg\",\n" +
                "                      \"video3\": \"https://business.paytm.com/s3assets/images/sb_my_devices/sb_4.jpg\",\n" +
                "                      \"video4\": \"https://www.youtube.com/embed/hDA2_nkoWSs\"\n" +
                "                      }\n" +
                "                  }\n" +
                "                ]\n" +
                "              }\n" +
                "            ]\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }

    private String getMockResponseForTrendingTopicsMissingWidget() {
        return "{\n" +
                "  \"response\": {\n" +
                "    \"pageType\": \"TOPIC_VIEW_ALL_PAGE\",\n" +
                "    \"template\": [\n" +
                "      {\n" +
                "        \"card\": [\n" +
                "          {\n" +
                "            \"header\": {\n" +
                "              \"widget\": [\n" +
                "                \n" +
                "              ]\n" +
                "            },\n" +
                "            \"tile\": [\n" +
                "              {\n" +
                "                \"widget\": [\n" +
                "                  {\n" +
                "                    \"id\": \"4_4_14_21_1229\",\n" +
                "                    \"type\": \"header\",\n" +
                "                    \"dataType\": \"static\",\n" +
                "                    \"displayOrder\": 1,\n" +
                "                    \"metaData\": {\n" +
                "                      \"text\": \"How do I make on Entry?\"\n" +
                "                    }\n" +
                "                  }\n" +
                "                ]\n" +
                "              }\n" +
                "            ]\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }

	/**
	 * Unit testCases for method updateTicket
	 * Case 1: 200 response from CST
	 * Expectation: updates ticket at FD
	 */
	//@Test(expected = Test.None.class) //ToDo: check why this is failing
	public void updateTicketCase1() throws Exception {
		// Method args
		String ticketNumber = "";
		Map<String, Object> requestBody = new HashMap<>();

		ResponseEntity<Object> httpResponse = new ResponseEntity<>("{\n" +
				"    \"created_at\": \"2023-10-26T07:44:12Z\",\n" +
				"    \"updated_at\": \"2023-10-26T07:44:49Z\",\n" +
				"    \"subject\": \"P4B Callmeback\",\n" +
				"    \"description\": \"<div>Ticket related to P4B callmeback EDC</div>\",\n" +
				"    \"status\": \"5\",\n" +
				"    \"priority\": 1,\n" +
				"    \"source\": \"110\",\n" +
				"    \"product_id\": \"1082000000989\",\n" +
				"    \"custom_fields\": {\n" +
				"        \"cf_bot_params\": \"{\\\"uniqueKey\\\":\\\"P4B-WogAHt47348113552868\\\",\\\"cstMetadata\\\":\\\"{\\\\\\\"cstentity\\\\\\\":\\\\\\\"p4bCallmeback\\\\\\\"}\\\"}\",\n" +
				"        \"cf_client_id\": \"P4B\",\n" +
				"        \"cf_issue_category_l1\": \"Call back request\",\n" +
				"        \"cf_merchant_id\": \"P4B-WogAHt47348113552868\"\n" +
				"    }\n" +
				"}", HttpStatus.OK);
		when(commonProperties.getRequiredProperty(DomainConstants.CST_URL)).thenReturn("https://cst.paytm.com");
		when(restProcessorDelegate.executeCstRequestHystrix(any(), any(), any(), any(), any(), any())).thenReturn(httpResponse);

		cstServiceImpl.updateTicket(ticketNumber, requestBody);
	}
	@Test
	public void updateTicketCase2() throws Exception {
		// Method args
		String ticketNumber = "";
		Map<String, Object> requestBody = new HashMap<>();

		ResponseEntity<Object> httpResponse = new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_URL)).thenReturn("https://cst.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(httpResponse);

		assertThrows(RuntimeException.class, () -> {
			cstServiceImpl.updateTicket(ticketNumber, requestBody);
		});
	}

	/**
	 * Unit testCases for method getTickets()
	 * Case 1: 200 response from CST
	 * Expectation: clean flow, returns response
	 */
	@Test
	public void getTicketsCase1() throws InterruptedException, UMPIntegrationException {
		String mid = "abc";
		Map<String, String> queryParams = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);
		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_URL)).thenReturn("https://cst.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(), any(), any())).thenReturn(new ResponseEntity<>("[\n" +
				"    {\n" +
				"        \"priority\": 1,\n" +
				"        \"source\": 110,\n" +
				"        \"status\": 72,\n" +
				"        \"subject\": \"P4B Callmeback\",\n" +
				"        \"id\": 38786,\n" +
				"        \"requester_id\": 14000006730929,\n" +
				"        \"due_by\": \"2023-10-30T21:00:00Z\",\n" +
				"        \"fr_due_by\": \"2023-10-26T21:00:00Z\",\n" +
				"        \"is_escalated\": false,\n" +
				"        \"group_id\": \"1082000383056\",\n" +
				"        \"custom_fields\": {\n" +
				"            \"cf_transaction_id\": \"Callback request\",\n" +
				"            \"cf_cst_entity\": \"p4bCallmeback\",\n" +
				"            \"cf_issue_category_l1\": \"Call back request\",\n" +
				"            \"cf_bot_params\": \"{\\\"uniqueKey\\\":\\\"P4B-dipadd46834559641560\\\",\\\"cstMetadata\\\":\\\"{\\\\\\\"cstentity\\\\\\\":\\\\\\\"p4bCallmeback\\\\\\\"}\\\"}\",\n" +
				"            \"cf_customer_issue_category_l1\": \"Call back request\",\n" +
				"            \"cf_merchant_id\": \"P4B-dipadd46834559641560\"\n" +
				"        },\n" +
				"        \"created_at\": \"2023-10-26T09:09:39Z\",\n" +
				"        \"updated_at\": \"2023-10-26T09:09:40Z\",\n" +
				"        \"tags\": [],\n" +
				"        \"product_id\": \"1082000000989\"\n" +
				"    },\n" +
				"    {\n" +
				"        \"priority\": 1,\n" +
				"        \"source\": 110,\n" +
				"        \"status\": 5,\n" +
				"        \"subject\": \"P4B Callmeback\",\n" +
				"        \"id\": 38485,\n" +
				"        \"statusTxt\": \"Closed\",\n" +
				"        \"requester_id\": 14000006730929,\n" +
				"        \"due_by\": \"2023-10-17T12:18:19Z\",\n" +
				"        \"fr_due_by\": \"2023-10-13T12:18:19Z\",\n" +
				"        \"is_escalated\": true,\n" +
				"        \"group_id\": \"1082000383056\",\n" +
				"        \"custom_fields\": {\n" +
				"            \"cf_transaction_id\": \"Callback request\",\n" +
				"            \"cf_cst_entity\": \"p4bCallmeback\",\n" +
				"            \"cf_issue_category_l1\": \"Call back request\",\n" +
				"            \"cf_bot_params\": \"{\\\"uniqueKey\\\":\\\"P4B-dipadd46834559641560\\\",\\\"cstMetadata\\\":\\\"{\\\\\\\"cstentity\\\\\\\":\\\\\\\"p4bCallmeback\\\\\\\"}\\\"}\",\n" +
				"            \"cf_merchant_id\": \"P4B-dipadd46834559641560\"\n" +
				"        },\n" +
				"        \"created_at\": \"2023-10-12T12:18:19Z\",\n" +
				"        \"updated_at\": \"2023-10-26T09:09:14Z\",\n" +
				"        \"tags\": [],\n" +
				"        \"product_id\": \"1082000000989\"\n" +
				"    }\n" +
				"]", HttpStatus.OK));
		Mockito.when(commonProperties.getRequiredProperty(PayTmPGConstants.CST_SERVICE_CLIENT_ID)).thenReturn("p4b_CST_client_staging");

		assertNotNull(cstServiceImpl.getTickets(mid, queryParams));
	}

	/**
	 * Case 2: 500 response from CST
	 * Expectation: throws UMPIntegrationException
	 */
	@Test
	public void getTicketsCase2() throws InterruptedException, UMPIntegrationException {
		String mid = "abc";
		Map<String, String> queryParams = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);
		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_URL)).thenReturn("https://cst.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(), any(), any())).thenReturn(new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR));
		Mockito.when(commonProperties.getRequiredProperty(PayTmPGConstants.CST_SERVICE_CLIENT_ID)).thenReturn("p4b_CST_client_staging");

		assertThrows(UMPIntegrationException.class, () -> {
			cstServiceImpl.getTickets(mid, queryParams);
		});
	}

	/**
	 * Unit testCases for method createTicket()
	 * Case 1: 200 response from CST
	 * Expectation: clean flow, returns response
	 */
	@Test
	public void createTicketCase1() throws InterruptedException, UMPIntegrationException {
		// Method args
		String mid = "";
		Map<String, String> queryParams = new HashMap<>();
		Map<String, Object> requestBody = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);

		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_URL)).thenReturn("https://cst.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>("{\n" +
				"    \"responseCode\": \"BT_200\",\n" +
				"    \"message\": \"success\",\n" +
				"    \"cstentity\": \"p4bCallmeback\",\n" +
				"    \"ticketNumber\": \"38784\",\n" +
				"    \"createdAt\": \"2023-10-26 13:14:12\",\n" +
				"    \"updatedAt\": \"2023-10-26 13:14:12\",\n" +
				"    \"viewType\": \"FreshBot\",\n" +
				"    \"freshDeskUrl\": \"https://paytm-merchantsandbox.freshdesk.com/\",\n" +
				"    \"requesterId\": 14000006778279,\n" +
				"    \"botParams\": {\n" +
				"        \"uniqueKey\": \"P4B-WogAHt47348113552868\",\n" +
				"        \"cstMetadata\": \"{\\\"cstentity\\\":\\\"p4bCallmeback\\\"}\"\n" +
				"    }\n" +
				"}", HttpStatus.OK));
		Mockito.when(commonProperties.getRequiredProperty(PayTmPGConstants.CST_SERVICE_CLIENT_ID)).thenReturn("p4b_CST_client_staging");

		assertNotNull(cstServiceImpl.createTicket(mid, requestBody, queryParams));
	}
	/**
	 * Case 2: 500 response from CST
	 * Expectation: throws UMPIntegrationException
	 */
	@Test
	public void createTicketCase2() throws InterruptedException, UMPIntegrationException {
		// Method args
		String mid = "";
		Map<String, String> queryParams = new HashMap<>();
		Map<String, Object> requestBody = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);

		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_URL)).thenReturn("https://cst.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(Object.class), any(Class.class))).thenReturn(new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR));
		Mockito.when(commonProperties.getRequiredProperty(PayTmPGConstants.CST_SERVICE_CLIENT_ID)).thenReturn("p4b_CST_client_staging");

		assertThrows(UMPIntegrationException.class, () -> {
			cstServiceImpl.createTicket(mid, requestBody, queryParams);
		});
	}

	/**
	 * Unit testCases for method callMeIVR()
	 * Case 1: 200 response from IVR
	 * Expectation: clean flow, returns SUCCESS response
	 */
	@Test
	public void callMeIVRCase1() throws InterruptedException, UMPIntegrationException {
		// Method args
		Map<String, Object> requestBody = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);

		ResponseEntity<Object> httpResponse = new ResponseEntity<>("SUCCESS",HttpStatus.OK);
		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_MGW_URL)).thenReturn("https://cst-mgw.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(httpResponse);

		assertNotNull(cstServiceImpl.callMeIVR(requestBody));
	}
	/**
	 * Case 2: 500 response from IVR
	 * Expectation: throws UMPIntegrationException
	 */
	@Test
	public void callMeIVRCase2() throws InterruptedException, UMPIntegrationException {
		// Method args
		Map<String, Object> requestBody = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);

		ResponseEntity<Object> httpResponse = new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_MGW_URL)).thenReturn("https://cst-mgw.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(httpResponse);

		assertThrows(UMPIntegrationException.class, () -> {
			cstServiceImpl.callMeIVR(requestBody);
		});
	}

	/**
	 * Unit testCases for method getCallDetailIVR()
	 * Case 1: 200 response from IVR
	 * Expectation: clean flow, returns response
	 */
	@Test
	public void getCallDetailIVRCase1() throws InterruptedException, UMPIntegrationException {
		// Method args
		Map<String, Object> requestBody = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);
		ResponseEntity<Object> httpResponse = new ResponseEntity<>("\"{\\\"Status\\\":\\\"SUCCESS\\\",\\\"CustDetail\\\":[{\\\"CustId\\\":\\\"1435967372\\\",\\\"MID\\\":\\\"qa11VE51594478161509\\\",\\\"CallStatus\\\":\\\"NONCONNECTED\\\",\\\"CampaignName\\\":\\\"CallMeBack_EDC\\\",\\\"Attempt\\\":1,\\\"IsExist\\\":1,\\\"AttemptDateTime\\\":\\\"2023-09-27T17:49:34.303\\\",\\\"ExpectedNextCallTime\\\":\\\"2023-09-27T18:49:34.303\\\"}],\\\"ERRMSG\\\":\\\"NONE\\\"}\"",HttpStatus.OK);
		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_MGW_URL)).thenReturn("https://cst-mgw.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(httpResponse);

		assertNotNull(cstServiceImpl.getCallDetailIVR(requestBody));
	}
	/**
	 * Case 2: 500 response from IVR
	 * Expectation: throws UMPIntegrationException
	 */
	@Test
	public void getCallDetailIVRCase2() throws InterruptedException, UMPIntegrationException {
		// Method args
		Map<String, Object> requestBody = new HashMap<>();

		UtsHelper.mockUserAuthentication(authentication);
		ResponseEntity<Object> httpResponse = new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		Mockito.when(commonProperties.getRequiredProperty(DomainConstants.CST_MGW_URL)).thenReturn("https://cst-mgw.paytm.com");
		Mockito.when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(), any(), any(), any())).thenReturn(httpResponse);

		assertThrows(UMPIntegrationException.class, () -> {
			cstServiceImpl.getCallDetailIVR(requestBody);
		});
		cstServiceImpl.getCallDetailIVR(requestBody);
	}

	@Test
	public void testGetTicketDetails() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);
		String encryptedTicketNumber = "encryptedTicketNumber";

		String mockResponse1 = "{\n" +
				"  \"data\": {\n" +
				"    \"DecryptedData\": {\n" +
				"      \"ticketNumber\": \"123\"\n" +
				"    }\n" +
				"  }\n" +
				"}";

		String mockResponse2 = "{\n" +
				"  \"source\": \"FreshDesk\",\n" +
				"  \"createdAt\": \"2024-06-19 00:06:34\",\n" +
				"  \"updatedAt\": \"2024-06-21 10:05:23\",\n" +
				"  \"cstentity\": \"p4bAIBot\",\n" +
				"  \"botParams\": {\n" +
				"    \"uniqueKey\": \"P4B-hkoHXD3132820\",\n" +
				"    \"cstMetadata\": \"{\\\"cstentity\\\":\\\"p4bAIBot\\\"}\"\n" +
				"  },\n" +
				"  \"tags\": [\n" +
				"    \n" +
				"  ],\n" +
				"  \"requesterId\": 82000007100,\n" +
				"  \"caseCreationDate\": \"2024-06-19 00:06:34\",\n" +
				"  \"l1IssueCategory\": \"SB Deactivation Request\",\n" +
				"  \"l2IssueCategory\": \"High rental charges\",\n" +
				"  \"ticketNumber\": \"5090000530\",\n" +
				"  \"status\": \"Open\",\n" +
				"  \"dueBy\": \"2024-06-21T11:30:00Z\",\n" +
				"  \"subject\": \"Soundbox Hardware Related\",\n" +
				"  \"umpMid\": \"P4B-hkoHXD3132820\",\n" +
				"  \"l3IssueCategory\": \"Park to retention desk\",\n" +
				"  \"ticketStatus\": 40,\n" +
				"  \"rawL1IssueCategory\": \"SB Deactivation Request\",\n" +
				"  \"rawL2IssueCategory\": \"High rental charges\",\n" +
				"  \"rawL3IssueCategory\": \"Park to retention desk\",\n" +
				"  \"productId\": \"82000001001\",\n" +
				"  \"origin\": 100,\n" +
				"  \"customerIssueCategoryL1\": \"Soundbox Hardware Related\",\n" +
				"  \"entityType\": \"Soundbox Issue\",\n" +
				"  \"cfMidHive\": \"hkoHXD3132820\",\n" +
				"  \"viewType\": \"InHouse\",\n" +
				"  \"freshDeskUrl\": \"https://paytm-merchant.freshdesk.com/\"\n" +
				"}";

		ResponseEntity<String> responseEntity1 = new ResponseEntity<>(mockResponse1, HttpStatus.OK);
		ResponseEntity<String> responseEntity2 = new ResponseEntity<>(mockResponse2, HttpStatus.OK);

		Mockito.when(restProcessorDelegate.executeCstSeviceCallRequestHystrix(anyString(), anyString(), any(), any(), any(), any(Class.class))).thenReturn(responseEntity1);
		Mockito.when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), anyMap(), any(), any(), any(Class.class))).thenReturn(responseEntity2);
		ResponseUmp actualResponse = cstServiceImpl.getTicketDetails(encryptedTicketNumber);

		Map<String,Object> responseMap = (Map<String,Object>) actualResponse.getResults();
		assertEquals("Soundbox Issue", responseMap.get("entityType"));
	}

	@Test
	public void testGetTicketDetailsThrowsValidationException() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);
		String encryptedTicketNumber = "";

		assertThrows(ValidationException.class, () -> {
			cstServiceImpl.getTicketDetails(encryptedTicketNumber);
		});
	}

	@Test
	public void testGetAllTicketDetails() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);
		String expectedResponse = "expectedResponse";
		ResponseEntity<String> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);

		when(restProcessorDelegate.executeCstRequestHystrix(anyString(), anyString(), any(Map.class), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);

		ResponseEntity<String> actualResponse = cstServiceImpl.getAllTicketDetails();

		assertEquals(expectedResponse, actualResponse.getBody());
	}

	@Test
	public void testGetTicketTimeLine() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);
		String ticketNumber = "ticketNumber";
		TicketInfoDTO ticketInfoDTO = new TicketInfoDTO();
		String expectedResponse = "expectedResponse";
		ResponseEntity<String> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);

		when(restProcessorDelegate.executeCstMGWRequestHystrix(anyString(), anyString(), any(Map.class), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);

		ResponseEntity<String> actualResponse = cstServiceImpl.getTicketTimeLine(ticketNumber, ticketInfoDTO);

		assertEquals(expectedResponse, actualResponse.getBody());
	}
}
