package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.FsmService;
import com.paytm.aggregatorgateway.service.NotificationService;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class WidgetApiServiceImplTest {

	@InjectMocks
	private WidgetApiServiceImpl widgetApiService;

	@Mock
	private HomepageWidgetDao homepageWidgetDao;

	@Mock
	private NotificationService notificationService;

	@Mock
	private FsmService fsmService;

    private Authentication authentication;

	@Mock
	private ObjectMapper objectMapper;

	@Mock
	private RedisHelper redisHelper;

	@BeforeEach
	public void init() {
		MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
	}

	/**
	 * Case : missing mandatory request parameters status
	 * Expectation : Validation Exception - Mandatory params not passed
	 */
	@Test
	public void updateWidgetTest1() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("cardType", "mockCard");

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());

		assertThrows(ValidationException.class, () -> {
			widgetApiService.updateWidget(mockRequestBody);
		});

	}

	/**
	 * Case : missing mandatory request parameters cardType
	 * Expectation : Validation Exception - Mandatory params not passed
	 */
	@Test
	public void updateWidgetTest2() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "active");

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());

		assertThrows(ValidationException.class, () -> {
			widgetApiService.updateWidget(mockRequestBody);
		});
	}

	/**
	 * Case : card in request is not a whitelisted card
	 * Expectation : Validation Exception - Card is not WhileListed
	 */
	@Test
	public void updateWidgetTest3() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "active");
		mockRequestBody.put("cardType", "abc");

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());

		assertThrows(ValidationException.class, () -> {
			widgetApiService.updateWidget(mockRequestBody);
		});
	}

	/**
	 * Case : request for deactivating whitelisted card for which there is no entry in DB
	 * Expectation : ResponseUmp Exception - No Active card exists so INACTIVE card can't be pushed
	 */
	@Test
	public void updateWidgetTest4() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "INACTIVE");
		mockRequestBody.put("cardType", "UPDATE_TICKET_ADDRESS");

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());

		assertThrows(ResponseUmpException.class, () -> {
			widgetApiService.updateWidget(mockRequestBody);
		});
	}
	@Test
	public void updateWidgetTestBusinessProof() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "INACTIVE");
		mockRequestBody.put("cardType", "BUSINESS_PROOF_COLLECTION");
		List<WidgetInfoDTO> mockWidgetInfoList = new ArrayList<>();
		WidgetInfoDTO widgetInfoDTO = new WidgetInfoDTO();
		widgetInfoDTO.setMid("abc");
		widgetInfoDTO.setFeatureType("BUSINESS_PROOF_COLLECTION");
		widgetInfoDTO.setStatus("ACTIVE");
		widgetInfoDTO.setExpiryTime(new Date());
		mockWidgetInfoList.add(widgetInfoDTO);

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(mockWidgetInfoList);

		ResponseUmp responseUmp = widgetApiService.updateWidget(mockRequestBody);
		assertNotNull(responseUmp);
	}

	@Test
	public void updateWidgetTest() throws Exception {
		String request = "{\n" +
				"  \"featureType\": \"PAYMENT_HOLD\",\n" +
				"  \"status\": \"ACTIVE\",\n" +
				"  \"mid\": \"123\",\n" +
				"  \"identifierKey\": \"TICKET_ID\",\n" +
				"  \"identifierValue\": \"234\",\n" +
				"  \"ttl\": -1,\n" +
				"  \"metaData\": {\n" +
				"    \"issue_category_l1\": \"offline risk monitoring\",\n" +
				"    \"issue_category_l2\": \"Transaction investigation\",\n" +
				"    \"issue_category_l3\": \"Payment to be held\",\n" +
				"    \"status\": \"Details Pending from merchant\",\n" +
				"    \"sub_status\": \"abc\",\n" +
				"    \"ticket_id\": \"111\"\n" +
				"  }\n" +
				"}";
		ObjectMapper objectMapper = new ObjectMapper();
		Map<String,Object> requestMap = objectMapper.readValue(request,Map.class);
		List<WidgetInfoDTO> mockWidgetInfoList = new ArrayList<>();
		WidgetInfoDTO widgetInfoDTO = new WidgetInfoDTO();
		widgetInfoDTO.setMid("abc");
		widgetInfoDTO.setFeatureType("UPDATE_TICKET_ADDRESS");
		widgetInfoDTO.setIdentifierKey("TICKET_ID");
		widgetInfoDTO.setIdentifierValue("123");
		widgetInfoDTO.setStatus("ACTIVE");
		widgetInfoDTO.setExpiryTime(new Date());
		mockWidgetInfoList.add(widgetInfoDTO);
		ReflectionTestUtils.setField(widgetApiService, "isPaymentHoldEnabled", true);
		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(mockWidgetInfoList);
		ResponseUmp actualResp = widgetApiService.updateWidget(requestMap);
		assertNotNull(actualResp);
	}

	@Test
	public void updateWidgetSettlementUpdateTest() throws Exception {
		String request = "{\n" +
				"        \"featureType\": \"SETTLEMENT_STATUS_UPDATE_CARD\",\n" +
				"        \"status\": \"INACTIVE\",\n" +
				"        \"mid\": \"PJfJJs90006511546037\",\n" +
				"        \"ttl\": 3600,\n" +
				"        \"identifierKey\": \"SETTLEMENT_BILL_ID\",\n" +
				"        \"identifierValue\": \"ONSPG************FUNCEM51406669278318\",\n" +
				"        \"metaData\":\"{\\\"billDateTime\\\":\\\"************\\\",\\\"pushedTime\\\":*************,\\\"utrNo\\\":null,\\\"bankResponseCode\\\":\\\"FGW_ACCOUNT_CLOSED\\\",\\\"bankResponseCodeDescription\\\":\\\"Pending response from bene bank\\\",\\\"extSerialNo\\\":\\\"220929132454852173347\\\",\\\"withdrawMode\\\":\\\"IMPS\\\",\\\"holdStatus\\\":null,\\\"holdReason\\\":null,\\\"holdTime\\\":null,\\\"settleStatus\\\":\\\"PENDING\\\",\\\"bankStatus\\\":null,\\\"isReversal\\\":null,\\\"settleBillId\\\":\\\"ONSPG************FUNCEM51406669278318\\\",\\\"maskedCardNo\\\":\\\"393****0088\\\",\\\"instUnionCode\\\":\\\"SBIN0020169\\\",\\\"instName\\\":\\\"SBI\\\",\\\"settleCompletedTime\\\":null,\\\"settleTransferTime\\\":null,\\\"accountNo\\\":null,\\\"utrUpdatedTime\\\":*************,\\\"issuingBankName\\\":\\\"PPBL\\\",\\\"amountSettled\\\":{\\\"currencyCode\\\":\\\"INR\\\",\\\"currencyValue\\\":\\\"356\\\",\\\"cent\\\":\\\"9009009\\\"},\\\"nextRetryableTime\\\":null,\\\"retryCount\\\":0,\\\"errorCode\\\":\\\"FGW_ACCOUNT_CLOSED\\\",\\\"modifiedTime\\\":*************,\\\"settleStrategy\\\":\\\"ONLINE_SETTLEMENT\\\",\\\"merchantSolutionType\\\":\\\"ONLINE\\\",\\\"pplusMerchantId\\\":\\\"PJfJJs90006511546037\\\"}\"\n" +
				"}";
		ObjectMapper objectMapper = new ObjectMapper();
		Map<String,Object> requestMap = objectMapper.readValue(request,Map.class);
		List<WidgetInfoDTO> mockWidgetInfoList = new ArrayList<>();
		WidgetInfoDTO widgetInfoDTO = new WidgetInfoDTO();
		widgetInfoDTO.setMid("PJfJJs90006511546037");
		widgetInfoDTO.setFeatureType("SETTLEMENT_STATUS_UPDATE_CARD");
		widgetInfoDTO.setIdentifierKey("SETTLEMENT_BILL_ID");
		widgetInfoDTO.setIdentifierValue("ONSPG************FUNCEM51406669278318");
		widgetInfoDTO.setStatus("ACTIVE");
		widgetInfoDTO.setExpiryTime(new Date());
		mockWidgetInfoList.add(widgetInfoDTO);
		//ReflectionTestUtils.setField(widgetApiService, "isPaymentHoldEnabled", true);
		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(mockWidgetInfoList);
		ResponseUmp actualResp = widgetApiService.updateWidget(requestMap);
		assertNotNull(actualResp);
	}

	/**
	 * Case : request for deactivating whitelisted card for which there is entry in DB
	 * Expectation : Success Response
	 */
	@Test
	public void updateWidgetTest5() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "INACTIVE");
		mockRequestBody.put("cardType", "UPDATE_TICKET_ADDRESS");
		mockRequestBody.put("identifierValue", "1235");

		List<WidgetInfoDTO> mockWidgetInfoList = new ArrayList<>();
		WidgetInfoDTO widgetInfoDTO = new WidgetInfoDTO();
		widgetInfoDTO.setMid("abc");
		widgetInfoDTO.setFeatureType("UPDATE_TICKET_ADDRESS");
		widgetInfoDTO.setIdentifierKey("TICKET_ID");
		widgetInfoDTO.setIdentifierValue("123");
		widgetInfoDTO.setStatus("ACTIVE");
		widgetInfoDTO.setExpiryTime(new Date());
		mockWidgetInfoList.add(widgetInfoDTO);

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(mockWidgetInfoList);

		ResponseUmp actualResp = widgetApiService.updateWidget(mockRequestBody);
		assertNotNull(actualResp);
		assertNotNull(actualResp.getStatusCode());
		assertNotNull(actualResp.getStatus());
		assertNotNull(actualResp.getStatusMessage());
		assertNull(actualResp.getResults());
		assertEquals("200", actualResp.getStatus());
		assertEquals("SUCCESS", actualResp.getStatusCode());
		assertEquals("Widget pushed successfully", actualResp.getStatusMessage());
	}

	/**
	 * case : Activating whitelisted card and ttl is not passed
	 * Expectation : Validation Expection : ttl value is missing or is 0 for activating widget
	 */
	@Test
	public void updateWidgetTest6() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "ACTIVE");
		mockRequestBody.put("cardType", "UPDATE_TICKET_ADDRESS");
		mockRequestBody.put("identifierValue", "1235");

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());
		doThrow(new RuntimeException("Received failure from NOTIFICATIONS")).when(notificationService).notifyPush(any(), any(), any());

		assertThrows(ValidationException.class, () -> {
			widgetApiService.updateWidget(mockRequestBody);
		});
	}

	/**
	 * case : Activating UPDATE_TICKET_ADDRESS and receive failure from Notification or
	 * error in addOrUpdateWidgetInfoInDB
	 * Expectation : ResponseUmp Exception : Error while updating widget
	 */
	@Test
	public void updateWidgetTest7() throws Exception {
		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "ACTIVE");
		mockRequestBody.put("cardType", "UPDATE_TICKET_ADDRESS");
		mockRequestBody.put("identifierValue", "1235");
		mockRequestBody.put("ttl", 30);
		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());
		doThrow(new RuntimeException("Received failure from NOTIFICATIONS")).when(notificationService).notifyPush(any(), any(), any());
		assertThrows(ResponseUmpException.class, () -> {
			widgetApiService.updateWidget(mockRequestBody);
		});
	}

	/**
	 * case : Activating UPDATE_TICKET_ADDRESS and receive success from Notification and
	 * no error in addOrUpdateWidgetInfoInDB
	 * Expectation : Success Response
	 */
	@Test
	public void updateWidgetTest8() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "ACTIVE");
		mockRequestBody.put("cardType", "UPDATE_TICKET_ADDRESS");
		mockRequestBody.put("identifierValue", "1235");
		mockRequestBody.put("ttl", 30);

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());

		ResponseUmp actualResp = widgetApiService.updateWidget(mockRequestBody);
		assertNotNull(actualResp);
		assertNotNull(actualResp.getStatusCode());
		assertNotNull(actualResp.getStatus());
		assertNotNull(actualResp.getStatusMessage());
		assertNull(actualResp.getResults());
		assertEquals("200", actualResp.getStatus());
		assertEquals("SUCCESS", actualResp.getStatusCode());
		assertEquals("Widget pushed successfully", actualResp.getStatusMessage());
	}

	/**
	 * case : Activating CM_TRANSACTION_BLOCKED and openBeats exists at fsm
	 * Expectation : Success Response with status code 201
	 */
	@Test
	public void updateWidgetTest9() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "ACTIVE");
		mockRequestBody.put("cardType", "CM_TRANSACTION_BLOCKED");
		mockRequestBody.put("identifierValue", "1235");
		mockRequestBody.put("ttl", 30);

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());
		when(fsmService.getRelevantOpenBeat(any(), any())).thenReturn(new HashMap<>());

		ResponseUmp actualResp = widgetApiService.updateWidget(mockRequestBody);
		assertNotNull(actualResp);
		assertNotNull(actualResp.getStatusCode());
		assertNotNull(actualResp.getStatus());
		assertNotNull(actualResp.getStatusMessage());
		assertNull(actualResp.getResults());
		assertEquals("201", actualResp.getStatus());
		assertEquals("SUCCESS", actualResp.getStatusCode());
		assertEquals("Card is not eligible to push", actualResp.getStatusMessage());
	}

	/**
	 * case : Activating CM_TRANSACTION_BLOCKED and no openBeats exists at fsm and no error in push notify
	 * Expectation : Success Response with status code 200
	 */
	@Test
	public void updateWidgetTest10() throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "abc");
		mockRequestBody.put("status", "ACTIVE");
		mockRequestBody.put("cardType", "CM_TRANSACTION_BLOCKED");
		mockRequestBody.put("identifierValue", "1235");
		mockRequestBody.put("ttl", 30);

		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());
		when(fsmService.getRelevantOpenBeat(any(), any())).thenReturn(null);

		ResponseUmp actualResp = widgetApiService.updateWidget(mockRequestBody);
		assertNotNull(actualResp);
		assertNotNull(actualResp.getStatusCode());
		assertNotNull(actualResp.getStatus());
		assertNotNull(actualResp.getStatusMessage());
		assertNull(actualResp.getResults());
		assertEquals("200", actualResp.getStatus());
		assertEquals("SUCCESS", actualResp.getStatusCode());
		assertEquals("Widget pushed successfully", actualResp.getStatusMessage());
	}

	/**
	 * case : Activating CM_OUT_OF_PRINTING_PAPER
	 */
	@Test
	public void updateWidgetTest11() throws Exception {
		activateCard(PayTmPGConstants.CM_OUT_OF_PRINTING_PAPER);
	}

	/**
	 * case : Activating CM_OUT_OF_NETWORK
	 */
	@Test
	public void updateWidgetTest12() throws Exception {
		activateCard(PayTmPGConstants.CM_OUT_OF_NETWORK);
	}

	/**
	 * case : Activating CM_OUT_OF_BATTERY
	 */
	@Test
	public void updateWidgetTest13() throws Exception {
		activateCard(PayTmPGConstants.CM_OUT_OF_BATTERY);
	}

	/**
	 * case : Activating SB_OUT_OF_BATTERY_5
	 */
	@Test
	public void updateWidgetTest14() throws Exception {
		activateCard(PayTmPGConstants.SB_OUT_OF_BATTERY_5);
	}

	/**
	 * case : Activating SB_OUT_OF_BATTERY_10
	 */
	@Test
	public void updateWidgetTest15() throws Exception {
		activateCard(PayTmPGConstants.SB_OUT_OF_BATTERY_10);
	}

	/**
	 * case : Activating CM_SIM_NETWORK_25
	 */
	@Test
	public void updateWidgetTest16() throws Exception {
		activateCard(PayTmPGConstants.CM_SIM_NETWORK_25);
	}

	/**
	 * case : Activating CM_WIFI_NETWORK_25
	 */
	@Test
	public void updateWidgetTest17() throws Exception {
		activateCard(PayTmPGConstants.CM_WIFI_NETWORK_25);
	}

	/**
	 * case : Activating CM_BATTERY_10TO15
	 */
	@Test
	public void updateWidgetTest18() throws Exception {
		activateCard(PayTmPGConstants.CM_BATTERY_10TO15);
	}

	@Test
	public void updateWidgetTest19() throws Exception {
		activateCard(PayTmPGConstants.SB_BATTERY_5_AND_CHARGING);
	}

	@Test
	public void updateWidgetTest20() throws Exception {
		activateCard(PayTmPGConstants.SB_BATTERY_10_AND_CHARGING);
	}

	/*@Test
	public void updateWidgetTest21() throws Exception {
		activateCard(PayTmPGConstants.SB_LOW_BATTERY_20);
	}*/

	/*@Test
	public void updateWidgetTest22() throws Exception {
		activateCard(PayTmPGConstants.SB_BATTERY_20_AND_CHARGING);
	}*/

	@Test
	public void updateWidgetTest23() throws Exception {
		activateCard(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_CHARGING);
	}

	@Test
	public void updateWidgetTest24() throws Exception {
		activateCard(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_NOT_CHARGING);
	}

	@Test
	public void updateWidgetTest25() throws Exception {
		activateCard(PayTmPGConstants.SB_CHARGER_DISCONNECTED);
	}

	@Test
	public void updateWidgetTest26() throws Exception {
		activateCard(PayTmPGConstants.SB_CHARGER_DISCONNECTED_MULTIPLE);
	}

	@Test
	public void updateWidgetTest27() throws Exception {
		activateCard(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_NOT_CHARGING);
	}

	@Test
	public void updateWidgetTest28() throws Exception {
		activateCard(PayTmPGConstants.SB_MANUAL_SWITCH_OFF);
	}

	@Test
	public void updateWidgetTest29() throws Exception {
		activateCard(PayTmPGConstants.SB_MANUAL_SWITCH_ON);
	}

	/*@Test
	public void updateWidgetTest30() throws Exception {
		activateCard(PayTmPGConstants.SB_BATTERY_20_AND_CHARGING);
	}*/

	/**
	 * case : Activating CM_BATTERY_10TO20
	 */
	@Test
	public void updateWidgetTest31() throws Exception {
		activateCard(PayTmPGConstants.CM_BATTERY_10TO20);
	}

	@Test
	public void updateWidgetTest32() throws Exception {
		activateCard("EDC_ORDER_DELIVERED");
	}

	@Test
	public void updateWidgetTest33() throws Exception {
		Mockito.doNothing().when(redisHelper).evictBusinessProof(anyString());
		when(redisHelper.setBusinessProofFlagActive(anyString(), anyString())).thenReturn("SUCCESS");
		activateCard("BUSINESS_PROOF_COLLECTION");
	}

	@Test
	public void updateWidgetTest34() throws Exception {
		activateCard("AGENT_VISIT_SCHEDULED");
	}

	@Test
	public void updateWidgetTest35() throws Exception {
		activateCard("DOCUMENT_VERIFICATION_FAILED");
	}

	@Test
	public void updateWidgetTest36() throws Exception {
		activateCard("EDC_ORDER_PLACED");
	}

	private void activateCard(String cardType) throws Exception {

		Map<String, Object> mockRequestBody = new HashMap<>();
		mockRequestBody.put("mid", "1234");
		mockRequestBody.put("status", "ACTIVE");
		mockRequestBody.put("cardType", cardType);
		mockRequestBody.put("identifierValue", "1235");
		mockRequestBody.put("ttl", 30);
		when(homepageWidgetDao.fetchWidgetDetails(any(), any(), any(), any(), any(), any())).thenReturn(new ArrayList<>());
		List<String> allowedLastDigits = new ArrayList<>(Arrays.asList("0","1","2","3","4","5"));
		ReflectionTestUtils.setField(widgetApiService, "allowedLastDigitsForCM10TO20", allowedLastDigits);
		ResponseUmp actualResp = widgetApiService.updateWidget(mockRequestBody);
		assertNotNull(actualResp);
		assertNotNull(actualResp.getStatusCode());
		assertNotNull(actualResp.getStatus());
		assertNotNull(actualResp.getStatusMessage());
		assertNull(actualResp.getResults());
		assertEquals("200", actualResp.getStatus());
		assertEquals("SUCCESS", actualResp.getStatusCode());
		assertEquals("Widget pushed successfully", actualResp.getStatusMessage());
	}
}
