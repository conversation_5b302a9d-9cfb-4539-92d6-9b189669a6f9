package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.helper.UtsHelper;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.verify;

public class FsmServiceImplTest {

	@Mock
	private RestProcessorDelegate restProcessorDelegate;

	@Mock
	private Environment commonProperties;

	@Mock
	private ObjectMapper objectMapper;

	@Mock
	AWSSecretManager awsSecretManager;

	@InjectMocks
	private FsmServiceImpl fsmService;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		ReflectionTestUtils.setField(fsmService, "objectMapper", new ObjectMapper());
		Map<String, String> awsSecretsMapMock = new HashMap<>();
		awsSecretsMapMock.put(AWSSecrets.FSM_CLIENT_SECRET.getValue(), "JlWk5ypip5ZJtehF1dod0bwL35hxroypoT9nbuibSegTx851Hdo2Pjb7X2GYQQD2hfqPeQWFW1D/6qFMqxbZwA==");
		ReflectionTestUtils.setField(awsSecretManager, "awsSecretsMap", awsSecretsMapMock);
	}

	@Test
	public void createBeatShouldCreateBeatSuccessfully() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("addressLine1", "Address Line 1");
		requestBody.put("addressLine2", "Address Line 2");
		requestBody.put("city", "City");
		requestBody.put("state", "State");
		requestBody.put("postalCode", "Postal Code");

		Map<String, String> createTicketResponse = new HashMap<>();
		createTicketResponse.put("ticketNumber", "12345");
		createTicketResponse.put("created_at", "2022-01-01 00:00:00");

		when(commonProperties.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class)))
				.thenReturn(new ResponseEntity<>("{}", HttpStatus.OK));

		fsmService.createBeat(requestBody, "mid", "deviceId", createTicketResponse);

		verify(restProcessorDelegate).executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class));
	}

	@Test
	public void createBeatShouldThrowExceptionWhenResponseIsNotSuccessful() throws Exception {
		UtsHelper.mockUserAuthentication(authentication);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("addressLine1", "Address Line 1");
		requestBody.put("addressLine2", "Address Line 2");
		requestBody.put("city", "City");
		requestBody.put("state", "State");
		requestBody.put("postalCode", "Postal Code");

		Map<String, String> createTicketResponse = new HashMap<>();
		createTicketResponse.put("ticketNumber", "12345");
		createTicketResponse.put("created_at", "2022-01-01 00:00:00");

		when(commonProperties.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class)))
				.thenReturn(new ResponseEntity<>("{}", HttpStatus.BAD_REQUEST));

		assertThrows(UMPIntegrationException.class, () -> {
			fsmService.createBeat(requestBody, "mid", "deviceId", createTicketResponse);
		});
	}

	@Test
	public void getRelevantOpenBeatShouldReturnOpenBeatSuccessfully() throws Exception {
		String mid = "mid123";
		String deviceId = "device123";

		when(commonProperties.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class)))
				.thenReturn(new ResponseEntity<>("{\n" +
						"  \"tagDetails\": [\n" +
						"    {\n" +
						"      \"name\": \"EDC_address_update\"\n" +
						"    }\n" +
						"  ]\n" +
						"}", HttpStatus.OK));

		Map<String, Object> result = fsmService.getRelevantOpenBeat(mid, deviceId);

		verify(restProcessorDelegate).executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class));
		assertNotNull(result);
	}

	@Test
	public void getRelevantOpenBeatShouldThrowExceptionWhenResponseIsNotSuccessful() throws Exception {
		String mid = "mid123";
		String deviceId = "device123";

		when(commonProperties.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class)))
				.thenReturn(new ResponseEntity<>("{}", HttpStatus.BAD_REQUEST));

		assertThrows(UMPIntegrationException.class, () -> {
			fsmService.getRelevantOpenBeat(mid, deviceId);
		});
	}

	@Test
	public void getRelevantOpenBeatShouldReturnNullWhenResponseIsNotSuccessful() throws Exception {
		String mid = "mid123";
		String deviceId = "device123";

		when(commonProperties.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class)))
				.thenReturn(new ResponseEntity<>("{\n" +
						"  \"error\": {\n" +
						"    \"errorCode\": \"FSE-5416\",\n" +
						"    \"errorMsg\": \"No Active Beat Found For The Device Serial Number and PgMid.\"\n" +
						"  }\n" +
						"}", HttpStatus.BAD_REQUEST));

		assertNull(fsmService.getRelevantOpenBeat(mid, deviceId));
	}

	@Test
	public void getRelevantOpenBeatShouldReturnNullWhenNoActiveBeatFound() throws Exception {
		String mid = "mid123";
		String deviceId = "device123";

		when(commonProperties.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class)))
				.thenReturn(new ResponseEntity<>("{\n" +
						"  \"tagDetails\": [\n" +
						"    {\n" +
						"      \"name\": \"asdf\"\n" +
						"    }\n" +
						"  ]\n" +
						"}", HttpStatus.OK));

		Map<String, Object> result = fsmService.getRelevantOpenBeat(mid, deviceId);

		verify(restProcessorDelegate).executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class));
		assertNull(result);
	}

	@Test
	public void getRelevantOpenBeatShouldReturnNullWhenNoActiveBeatFoundError() throws Exception {
		String mid = "mid123";
		String deviceId = "device123";

		when(commonProperties.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeFsmRequestHystrix(any(), any(), any(), any(), any(), eq(String.class)))
				.thenReturn(new ResponseEntity<>("{\n" +
						"  ]\n" +
						"}", HttpStatus.OK));

		assertThrows(Exception.class, () -> {
			fsmService.getRelevantOpenBeat(mid, deviceId);
		});
	}
}
