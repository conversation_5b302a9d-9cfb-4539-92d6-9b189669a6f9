package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.dto.MerchantInfoDto;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.dashboard.security.UserAuthentication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class NotificationServiceImplTest {

	@Mock
	private Environment commonProperties;

	@Mock
	private RestProcessorDelegate restProcessorDelegate;

	@InjectMocks
	private NotificationServiceImpl notificationServiceImpl;

	private Authentication authentication;

	@BeforeEach
	public void init() throws Exception {
		MockitoAnnotations.openMocks(this);
		authentication = mock(UserAuthentication.class);
		SecurityContext securityContext = mock(SecurityContext.class);
		when(securityContext.getAuthentication()).thenReturn(authentication);
		SecurityContextHolder.setContext(securityContext);
		AWSSecretManager.awsSecretsMap = new HashMap<>();
		AWSSecretManager.awsSecretsMap.put("notification.client.id", "clientId");
		AWSSecretManager.awsSecretsMap.put("notification.client.secret", "clientSecret");
	}

	@Test
	public void notifyPushFailureTest() throws Exception {
		when(commonProperties.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeNotificationRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(), any())).thenReturn(new ResponseEntity<>("", HttpStatus.BAD_REQUEST));
		assertThrows(RuntimeException.class, () -> {
			notificationServiceImpl.notifyPush("", "", "cardType");
		});
	}

	@Test
	public void notifyPushSuccessTest() throws Exception {
		when(commonProperties.getRequiredProperty(anyString())).thenReturn("mockProperty");
		when(restProcessorDelegate.executeNotificationRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(), any())).thenReturn(new ResponseEntity<>("", HttpStatus.OK));
		assertThrows(RuntimeException.class, () -> {
			notificationServiceImpl.notifyPush("", "", "UPDATE_TICKET_ADDRESS");
		});
	}

	@Test
	public void testNotifyWhatsappPush() throws Exception {
		String mid = "mid";
		String featureType_notification = "LIMIT_UPGRADE_INSTRUMENT_85";
		MerchantInfoDto merchantDetails = new MerchantInfoDto();
		merchantDetails.setMerchantName("merchantName");
		merchantDetails.setPrimaryMobileNumber("1234567890");
		String triggerInstrumentMonthly = "triggerInstrumentMonthly";

		Map<String, Object> expectedResponse = new HashMap<>();
		expectedResponse.put("status", "SUCCESS");

		notificationServiceImpl.notifyWhatsappPush(mid, featureType_notification, merchantDetails, triggerInstrumentMonthly);

		assertNotNull(expectedResponse);
	}

}
