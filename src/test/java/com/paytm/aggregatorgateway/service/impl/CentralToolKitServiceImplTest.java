package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class CentralToolKitServiceImplTest {

    @Mock
    private Environment environment;

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @InjectMocks
    private CentralToolKitServiceImpl centralToolKitService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
		AWSSecretManager.awsSecretsMap = new HashMap<>();
    }

    @Test
    public void testGetAddressFromLatLong() throws Exception {
        when(environment.getProperty(anyString())).thenReturn("testUrl");
        when(restProcessorDelegate.executeCentralToolKitRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class)))
                .thenReturn(new ResponseEntity<>("{\"address\": \"testAddress\"}", HttpStatus.OK));

        Map<String, Object> result = centralToolKitService.getAddressFromLatLong("12.34", "56.78");

        assertEquals("testAddress", result.get("address"));
    }

	@Test
	public void testGetAddressFromLatLongEmptyBody() throws Exception {
		when(environment.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeCentralToolKitRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class)))
				.thenReturn(new ResponseEntity<>("", HttpStatus.OK));

		assertThrows(ValidationException.class, () -> {
			centralToolKitService.getAddressFromLatLong("12.34", "56.78");
		});
	}

	@Test
	public void testGetAddressFromLatLongNotOK() throws Exception {
		when(environment.getProperty(anyString())).thenReturn("testUrl");
		when(restProcessorDelegate.executeCentralToolKitRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class)))
				.thenReturn(new ResponseEntity<>("", HttpStatus.BAD_REQUEST));

		assertThrows(RuntimeException.class, () -> {
			centralToolKitService.getAddressFromLatLong("12.34", "56.78");
		});
	}

	@Test
	public void testGetAddressFromLatLongFailure() throws Exception {
		when(restProcessorDelegate.executeCentralToolKitRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class)))
				.thenReturn(new ResponseEntity<>("{\"address\": \"testAddress\"}", HttpStatus.OK));

		assertThrows(Exception.class, () -> {
			centralToolKitService.getAddressFromLatLong("12.34", "56.78");
		});
	}

}