package com.paytm.aggregatorgateway.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dto.MerchantInfoDto;
import com.paytm.aggregatorgateway.dto.SmsSubscriptionDTO;
import com.paytm.aggregatorgateway.dto.UpdateSubscriptionStatusDTO;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.impl.MerchantProfileServiceImpl;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.metrics.MetricUtils;
import com.paytm.dashboard.security.UserAuthentication;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

import static com.paytm.aggregatorgateway.helper.UtsHelper.mockUserAuthentication;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class MerchantProfileServiceImplTest {

    @Mock
    private Environment commonProperties;

    @Mock
    private ObjectMapper jacksonObjectMapper;

    @Mock
    private RestProcessorDelegate restProcessorDelegate;

    @Mock
    private SmsSubscriptionService smsSubscriptionService;

    @Mock
    private SubscriptionService subscriptionService;

    @Mock
    private MetricUtils metricUtils;

    @InjectMocks
    private MerchantProfileServiceImpl merchantProfileServiceImpl;

    private Authentication authentication;

    @BeforeEach
    public void init() throws Exception {
        MockitoAnnotations.openMocks(this);
        authentication = mock(UserAuthentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        ReflectionTestUtils.setField(merchantProfileServiceImpl, "jacksonObjectMapper", new ObjectMapper());
        ReflectionTestUtils.setField(merchantProfileServiceImpl, "segmentId", "sampleId");
        ReflectionTestUtils.setField(merchantProfileServiceImpl, "commissionValue", "sampleCommissionValue");
        AWSSecretManager.awsSecretsMap = new HashMap<>();
        AWSSecretManager.awsSecretsMap.put("boss.client.key", "80879ypiyuyidnijlkjnDo2780hILxdvvQXu9sh");
        AWSSecretManager.awsSecretsMap.put("boss.client.idt", "mockClientId");
    }

    @Test
    public void getCommunicationConfigurationTest() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.OK));

        when(commonProperties.getProperty(any())).thenReturn("123");

        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        when(commonProperties.getProperty(anyString(), anyString())).thenReturn("123");
        when(restProcessorDelegate.executeBOSSAppRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"merchantName\":\"123\"}", HttpStatus.OK));
        when(smsSubscriptionService.fetchIdFromCleverTap(anyString(), anyString(), anyString())).thenReturn("954682");
        Map<String, Object> response = merchantProfileServiceImpl.getCommunicationConfiguration("123", "123", "androidapp");
		assertNotNull(response);
    }

    @Test
    public void getCommunicationConfigurationTestErrorFromBoss() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.BAD_REQUEST));
        when(commonProperties.getProperty(any())).thenReturn("123");
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        when(commonProperties.getProperty(anyString(), anyString())).thenReturn("123");
        when(restProcessorDelegate.executeBOSSAppRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<String>("{\"merchantName\":\"123\"}", HttpStatus.OK));
        assertThrows(ValidationException.class, () -> {
            merchantProfileServiceImpl.getCommunicationConfiguration("123", "123", "androidapp");
        });

    }

    @Test
    public void setCommunicationConfigurationTest() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.BAD_REQUEST));
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("smsAllowed", true);
        map.put("transaction", map1);
        HttpServletRequest httpRequest = mock(HttpServletRequest.class);

        assertThrows(ValidationException.class, () -> {
            merchantProfileServiceImpl.setCommunicationConfiguration(map, "123", httpRequest);
        });
    }

    @Test
    public void setCommunicationConfigurationTest200() throws Exception {
        Map<String, Object> fetchResponse = getFetchResponse("200");
        String updateResponse = "success";

        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"" +
                "van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.BAD_REQUEST));
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        when(subscriptionService.fetchSubscription(anyString(), anyString(), anyString(), eq(null), eq(null), any())).thenReturn(fetchResponse);
        when(smsSubscriptionService.updateSubscriptionStatus(any(UpdateSubscriptionStatusDTO.class), eq(false), eq("androidapp"))).thenReturn(updateResponse);

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("smsAllowed", true);
        map.put("transaction", map1);
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("client", "androidapp");
        httpRequest.addHeader("appversion", "4.23.0");

        assertThrows(ValidationException.class, () -> {
            merchantProfileServiceImpl.setCommunicationConfiguration(map, "123", httpRequest);
        });
    }

    private static Map<String, Object> getFetchResponse(String number) {
        Map<String, Object> fetchResponse = new HashMap<>();
        fetchResponse.put("statusCode", "200");

        Map<String, Object> results = new HashMap<>();
        List<Map<String, Object>> subscriptions = new ArrayList<>();
        Map<String, Object> subscription = new HashMap<>();
        subscription.put("usn", "testUsn");
        subscription.put("subscriptionType", "testSubscriptionType");
        subscriptions.add(subscription);
        results.put("subscriptions", subscriptions);
        fetchResponse.put("results", results);
        fetchResponse.put("statusCode", number);
        return fetchResponse;
    }

    @Test
    public void setCommunicationConfigurationTest204() throws Exception {
        Map<String, Object> fetchResponse = getFetchResponse("204");
        String updateResponse = "success";

        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.BAD_REQUEST));
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        when(subscriptionService.fetchSubscription(anyString(), anyString(), anyString(), eq(null), eq(null), any())).thenReturn(fetchResponse);
        when(smsSubscriptionService.updateSubscriptionStatus(any(UpdateSubscriptionStatusDTO.class), eq(false), eq("androidapp"))).thenReturn(updateResponse);
        when(smsSubscriptionService.fetchIdFromCleverTap(anyString(), anyString(), anyString())).thenReturn("sampleId");
        when(smsSubscriptionService.createSubscription(any(SmsSubscriptionDTO.class))).thenReturn(updateResponse);

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("smsAllowed", true);
        map.put("transaction", map1);
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("client", "androidapp");
        httpRequest.addHeader("appversion", "4.23.0");

        assertThrows(ValidationException.class, () -> {
            merchantProfileServiceImpl.setCommunicationConfiguration(map, "123", httpRequest);
        });
    }

    @Test
    public void setCommunicationConfigurationTestSmsAllowedFalse() throws Exception {

        Map<String, Object> fetchResponse = getFetchResponse("200");
        String updateResponse = "success";

        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"" +
                "van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.BAD_REQUEST));
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        when(subscriptionService.fetchSubscription(anyString(), anyString(), anyString(), eq(null), eq(null), any())).thenReturn(fetchResponse);
        when(smsSubscriptionService.updateSubscriptionStatus(any(UpdateSubscriptionStatusDTO.class), eq(false), eq("androidapp"))).thenReturn(updateResponse);
        when(smsSubscriptionService.fetchIdFromCleverTap(anyString(), anyString(), anyString())).thenReturn("sampleId");
        when(smsSubscriptionService.createSubscription(any(SmsSubscriptionDTO.class))).thenReturn(updateResponse);

        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("smsAllowed", false);
        map.put("transaction", map1);
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("client", "androidapp");
        httpRequest.addHeader("appversion", "4.23.0");

        assertThrows(ValidationException.class, () -> {
            merchantProfileServiceImpl.setCommunicationConfiguration(map, "123", httpRequest);
        });
    }

    @Test
    public void forceUpdateMerchantsTest() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.OK));
        when(commonProperties.getProperty(any())).thenReturn("123");
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        MockHttpServletResponse mockHttpServletResponse = new MockHttpServletResponse();
        ResponseUmp response = merchantProfileServiceImpl.forceUpdateMerchants("123", "P4B_ANDROID", "NonSD", mockHttpServletResponse);
		assertNotNull(response);
    }

    @Test
    public void forceUpdateMerchantsOldVersionTest() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"van\": null,\n" +
                "  \"isReseller\":false\n" +
                "}", HttpStatus.OK));
        when(commonProperties.getProperty(any())).thenReturn("123");
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        MockHttpServletResponse mockHttpServletResponse = new MockHttpServletResponse();
        when(commonProperties.getProperty(anyString(), anyString())).thenReturn("2.01.012");
        ResponseUmp response = merchantProfileServiceImpl.forceUpdateMerchants("2.01.011", "P4B_ANDROID", "NonSD", mockHttpServletResponse);
		assertNotNull(response);
    }

    @Test
    public void forceUpdateMerchantsSameVersionTest() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(any(), any(), any(), any(), any(), eq(String.class))).thenReturn(new ResponseEntity<>("{\"van\": null,\n" + "  \"isReseller\":false\n" + "}", HttpStatus.OK));
        when(commonProperties.getProperty(any())).thenReturn("123");
        when(commonProperties.getRequiredProperty(PayTmPGConstants.BOSS_CLIENT_KEY)).thenReturn("123456ab1234");
        when(jacksonObjectMapper.readValue(anyString(), eq(Map.class))).thenReturn(new HashMap<>());
        MockHttpServletResponse mockHttpServletResponse = new MockHttpServletResponse();
        when(commonProperties.getProperty(anyString(), anyString())).thenReturn("2.01.011");
        ResponseUmp response = merchantProfileServiceImpl.forceUpdateMerchants("2.01.011", "P4B_ANDROID", "NonSD", mockHttpServletResponse);
        assertNotNull(response);
    }

    @Test
    public void handleSMSEmailAlertsOnBossTest1() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.BAD_REQUEST));
        Map<String, Object> requestBody = new HashMap<>();
        assertThrows(ValidationException.class, () -> {
            merchantProfileServiceImpl.handleSMSEmailAlertsOnBoss(requestBody, "url");
        });
    }

    @Test
    public void handleSMSEmailAlertOnBossTest2() throws Exception {
        mockUserAuthentication(authentication);
        when(restProcessorDelegate.executeBOSSRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(new ResponseEntity<>("", HttpStatus.INTERNAL_SERVER_ERROR));
        Map<String, Object> requestBody = new HashMap<>();
        assertThrows(RuntimeException.class, () -> {
            merchantProfileServiceImpl.handleSMSEmailAlertsOnBoss(requestBody, "url");
        });
    }

    @Test
    public void testFetchMerchantDetailsByMidSuccess() throws Exception {
        String mid = "XCaEOY55207999033701";

        String responseBody = "{\n" +
                "  \"mid\": \"XCaEOY55207999033701\",\n" +
                "  \"ppiLimit\": \"1\",\n" +
                "  \"merchantName\": \"********\",\n" +
                "  \"status\": \"INACTIVE\",\n" +
                "  \"aggregator\": false,\n" +
                "  \"firstName\": \"********\",\n" +
                "  \"lastName\": \"********\",\n" +
                "  \"kycDetails\": {\n" +
                "    \"bankAccountNo\": \"********\",\n" +
                "    \"businessIfscNo\": \"********\",\n" +
                "    \"bankAccountHolderName\": \"********\",\n" +
                "    \"bankName\": \"********\",\n" +
                "    \"businessPanNo\": \"********\",\n" +
                "    \"personalPanNo\": \"********\",\n" +
                "    \"gstin\": \"********\"\n" +
                "  },\n" +
                "  \"kybId\": \"B0covxrg0ur2030\",\n" +
                "  \"businessType\": \"INDIVIDUAL\",\n" +
                "  \"businessDetails\": {\n" +
                "    \"category\": \"Food\",\n" +
                "    \"subCategory\": \"Restaurant\",\n" +
                "    \"businessName\": \"********\",\n" +
                "    \"address\": \"********\",\n" +
                "    \"commAddress\": \"********\"\n" +
                "  },\n" +
                "  \"primaryMobileNumber\": \"********\"\n" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restProcessorDelegate.executeBOSSRequestHystrix(anyString(), anyString(), any(), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);
        when(commonProperties.getProperty(any())).thenReturn("123");

        MerchantInfoDto merchantInfo = merchantProfileServiceImpl.fetchMerchantDetailsByMid(mid);

        assertNotNull(merchantInfo);
        assertEquals(mid, merchantInfo.getMid());
        assertEquals("********", merchantInfo.getMerchantName());
        assertEquals("1", merchantInfo.getPpiLimit());
        assertEquals("INACTIVE", merchantInfo.getStatus());
        assertEquals("false", merchantInfo.getAggregator());
        assertEquals("B0covxrg0ur2030", merchantInfo.getKybId());
        assertEquals("B0covxrg0ur2030", merchantInfo.getKybId());
        assertEquals("INDIVIDUAL", merchantInfo.getBusinessType());
    }

    @Test
    public void testFetchMerchantDetailsByMidFailure() throws Exception {
        String mid = "XCaEOY55207999033701";

        String responseBody = "{\n" +
                "  \"status\": \"BO_401\",\n" +
                "  \"ppiLimit\": \"1\",\n" +
                "  \"merchantName\": \"********\",\n" +
                "  \"status\": \"INACTIVE\",\n" +
                "  \"aggregator\": false,\n" +
                "  \"firstName\": \"********\",\n" +
                "  \"lastName\": \"********\",\n" +
                "  \"kycDetails\": {\n" +
                "    \"bankAccountNo\": \"********\",\n" +
                "    \"businessIfscNo\": \"********\",\n" +
                "    \"bankAccountHolderName\": \"********\",\n" +
                "    \"bankName\": \"********\",\n" +
                "    \"businessPanNo\": \"********\",\n" +
                "    \"personalPanNo\": \"********\",\n" +
                "    \"gstin\": \"********\"\n" +
                "  },\n" +
                "  \"kybId\": \"B0covxrg0ur2030\",\n" +
                "  \"businessType\": \"INDIVIDUAL\",\n" +
                "  \"businessDetails\": {\n" +
                "    \"category\": \"Food\",\n" +
                "    \"subCategory\": \"Restaurant\",\n" +
                "    \"businessName\": \"********\",\n" +
                "    \"address\": \"********\",\n" +
                "    \"commAddress\": \"********\"\n" +
                "  },\n" +
                "  \"primaryMobileNumber\": \"********\"\n" +
                "}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restProcessorDelegate.executeBOSSRequestHystrix(anyString(), anyString(), anyMap(), any(HttpHeaders.class), any(), any(Class.class))).thenReturn(responseEntity);
        when(commonProperties.getProperty(any())).thenReturn("123");

        MerchantInfoDto merchantInfo = merchantProfileServiceImpl.fetchMerchantDetailsByMid(mid);
        assertNull(merchantInfo);
    }

}
