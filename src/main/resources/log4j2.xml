<?xml version="1.0" encoding="UTF-8" ?>

<Configuration>

	<Appenders>

		<!--Pattern Layout for UMP Logs-->
		<!--<Console name="umpConsole">
			<PatternLayout pattern="UMP_LOGS || %d{DATE} - [%X{traceId}] - [%t] - %C{1}.%M(%L) - %p: %m%ex%n"/>
		</Console>-->
		<!--JSON Layout for UMP Logs-->
		<Console name="umpConsole">
			<JsonLayout eventEol="true" compact="true" locationInfo="true" stacktraceAsString="true" includeTimeMillis="true">
				<KeyValuePair key="loggerType" value="UMP_LOGS"/>
				<KeyValuePair key="timestamp" value="$${date:dd MMM yyyy HH:mm:ss,SSS}"/>
				<KeyValuePair key="traceId" value="$${ctx:traceId}"/>
			</JsonLayout>
		</Console>

		<!--Pattern Layout for Monitor Logs-->
		<!--<Console name="monitorConsole">
			<PatternLayout pattern="MONITOR_LOGS || %d{DATE} - [%t] - %C{1}.%M(%L) - %p: %m%ex%n"/>
		</Console>-->
		<!--JSON Layout for Monitor Logs-->
		<Console name="monitorConsole">
			<JsonLayout eventEol="true" compact="true" locationInfo="true" stacktraceAsString="true" includeTimeMillis="true">
				<KeyValuePair key="loggerType" value="MONITOR_LOGS"/>
				<KeyValuePair key="timestamp" value="$${date:dd MMM yyyy HH:mm:ss,SSS}"/>
			</JsonLayout>
		</Console>

	</Appenders>

	<Loggers>

		<!--Root level logs for ERROR log level-->
		<Root level="ERROR">
			<AppenderRef ref="umpConsole"/>
		</Root>

		<!--All the Classes under com.paytm package will print logs to below logger-->
		<logger name="com.paytm" level="info" additivity="false">
			<AppenderRef ref="umpConsole"/>
		</logger>

		<!--MonitorLogger logger will print logs to below logger-->
		<logger name="MonitorLogger" level="info" additivity="false">
			<AppenderRef ref="monitorConsole"/>
		</logger>

	</Loggers>
</Configuration>
