#statsd
metric-client-config.prefix=merchant_bff_v2
metric-client-config.hostName=telegraf.monitoring.svc.cluster.local
metric-client-config.port=8125

enable.session.creation=true

#server.port=8081
spring.http.multipart.maxFileSize= 5MB
spring.http.multipart.maxRequestSize= 20MB

#tomcat properties
server.tomcat.threads.max=200
server.connection-timeout=10000

#To disbale cron to fetch expired session and delete them
spring.session.cleanup.cron.expression=0 0 5 28 2 ?
url.mapping=/bffv2/*

spring.redis.cluster.enable=false
spring.redis.cluster.nodes=
spring.redis.cluster.maxRedirects=3

edc.client.id=

spring.datasource.master.jdbc-url=***************************************************************
spring.datasource.master.username=app_staging
spring.datasource.master.password=PAaA#p48.>?<;:dG6
spring.datasource.master.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.master.maximum-pool-size=30
spring.datasource.master.minimum-idle=5
spring.datasource.master.idle-timeout=160000
spring.datasource.master.testWhileIdle=true
spring.datasource.master.timeBetweenEvictionRunsMillis=60000
spring.datasource.master.validationQuery=SELECT 1

#slave db config
spring.datasource.slave.jdbc-url=***************************************************************
spring.datasource.slave.username=app_staging
spring.datasource.slave.password=PAaA#p48.>?<;:dG6
spring.datasource.slave.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.slave.maximum-pool-size=30
spring.datasource.slave.minimum-idle=5
spring.datasource.slave.idle-timeout=160000
spring.datasource.slave.testWhileIdle=true
spring.datasource.slave.timeBetweenEvictionRunsMillis=60000
spring.datasource.slave.validationQuery=SELECT 1
logger.monitor.sensitive.key=clientId,clientSecret,extendInfo,aadhar,aadharNumber,accountNumber,ADDITIONAL_MOBILE_NUMBER,additionalMobileNumber,address,bankAccountHolderName,bankAccountNo,bankAccountNumber,bankIfscCode,bankName,businessIfscNo,businessName,businessPanNo,channelId,city,CMS_IMAGE_URL,commAddress,country,creditScore,deviceId,deviceIdentifier,deviceModel,deviceType,displayName,DOB,dob,docId (Aadhar/Pan),docValue,downloadLink,Email,email,emailId,fatherName,firstName,gstin,gstinId,ifsc,image,imei,isPreferred,kycAddress,lastName,loanAcountNo.,merchantName,middleName,mobileNo,mobNum,Name,name,nameAsPerPan,nickName,oppositePhone,pan,PanID,panNo,panNumber,personalPanNo,phone,primaryMailId,primaryMobileNumber,registrationName,secondaryPhoneNumber,settlementAccountNo,settlementBankIFSC,settlementBankName,SHOP_NAME,signatoryName,simNumber,Street address,upiAccountId,userMobile,uuidNo,Zipcode,legalName,stateJurisdiction,tradeName,posProvider,brandReq,loanAccountNumber,nameOnPan,businessId,gender,maritalStatus,docName,businessOwnerName,alternateNumber,cardLast4Digit,bankReferenceNo,extSerialNo,payMethod,issuingBankName,primaryVpa,mobile,bank,displayAccountNo,mpinLastUsed,last4digits,bossPan,merchantAccount,officialName,englishName,localName,mobileInfoId,contactTelephone,merchantBankName,cf_state,cf_postal_code,cf_district,cf_address_line_2,address,city,pincode,phone,shopName,selfieUrl,address1,address2,mostProbableAddress,line1,line3,district,postalCode,formattedAddress,propertyLandmark,propertyNumber,upi,kybId,shipping_address,poi_dist,street_dist,subSubLocality,formatted_address,area,houseNumber,houseName,poi,street,subLocality,locality,subDistrict,countryCode,subUserEmail,fseName,merchant_name,fname,uname,initialMerchant,areaName,zipCode,billingAddress,businessEntity,registeredAddress,merchantTransactionLimitType,userId,Identity,MERCHANT_NAME,line2,pin,state,paytmSSOToken,walletSSOToken,userToken
l1.Issue.Category.EDC=SR-EDC Hardware,EDC Rental,EDC Hardware,SR- EDC Rental
l1.Issue.Category.SB=SB Damaged/Lost,SB Deactivation Request,SB Dynamic QR+,SB FSE Related issues,SB General Query,SB Hardware,SB Pre Live Queries,SB Refund,SB Rental
management.endpoints.web.exposure.include=*

management.endpoint.sessions.enabled=false
management.endpoint.httptrace.enabled=false

spring.threads.virtual.enabled=true