merchant.superright.id = **********
merchant.demo.user.id=**********

allow.cors.origins=http://localhost:3000 https://pg-staging.paytm.in https://dev.paytm.com https://dev.paytm.com https://s3.ap-south-1.amazonaws.com https://staging-merchant.paytm.com http://localhost:8080 https://icicibank-staging.paytm.com https://ump-staging.paytm.com
allow.csrf.origins=
allow.iframe.origins=https://icicibank-staging.paytm.com https://ump-staging.paytm.com
allow.post.request=/api/v1/context/merchants,/api/v1/reports/txn/download,/api/v1/reports/settlement/download,/api/v1/reports/refund/download,/api/v1/reports/chargeback/download,/api/v1/reports/wallet/cashback/download,/api/v1/download/notifications,/api/v1/subwallet/download,/api/v1/reports/wallet/refund/download,/api/v1/reports/wallet/settlement/download,/api/v1/reports/wallet/txn/download,/api/v2/order/list,/api/v2/order/list/download,/api/v2/order/summary,/api/v2/settlement/bill/list,/api/v2/settlement/bill/summary,/api/v2/settlement/txn/list,/api/v2/settlement/download,/api/v1/dispute/list/download,/api/v3/order/detail
csrf.enable=false
enable.session.creation=true

monitor.log.body.length=10000
spring.redis.sentinel.master=cluster
spring.redis.sentinel.nodes=localhost
user.cache.ttl=1800
is.sentinel.enable=no
spring.redis.host=*************
spring.redis.port=6379
spring.jedispool.connect.wait.time=5000
spring.jedispool.minIdleConnections=5
spring.jedispool.maxIdleConnections=20
spring.jedispool.maxTotalConnections=20
spring.redis.socket.readtimeout=500

ump.user.context=/api/v1/context
ump.base.url=https://ump-staging.paytm.com

promo.engine.base.url=https://promo-msupercash-staging.paytm.com
promo.engine.base.path=/v1/mpromocard/s2s/
promo.engine.issuer=promotion_team

subscription.base.url=https://subscriptions-staging.paytm.com

sms.subscription.segment.id=975785
sms.subscription.commission.value=25

#cleverTap details
cleverTap.base.url=https://in1-paytm.api.clevertap.com
cleverTap.context=override|paytm|315642
freeze.sms.banner.id=889812

#OCR Configuration
ocr.client.id=perf-ocr-client-id
ocr.base.url=https://bank-ocr-app.internal.ap-south-1.staging.osmose.risk.pai.mypaytm.com
bank.proof.cache.ttl=3600

#OCR HTTP Configuration
http.timeout.ocr=5000
http.connect.timeout.ocr=5000
http.max.conn.per-route.ocr=200
http.max.conn.total.ocr=200

#App Base URL for callbacks
app.base.url=https://perf.paytm.com

//boss.base.url=https://bo-staging.paytm.in
boss.base.url=https://boss-qa-int.paytm.com

#ups Integration property
ups.base.url=https://ups-staging-external.paytm.com

kyb.base.url=https://cif-staging.paytm.in
kyb.client.id=ump
kyb.client.uid=**********

pg.reward.base.url=http://app-stg.loyalty-internal-stg-cloud.paytm.com
internal.user.permission=PSEUDOMERCHANT
allow.pseudo.merchant.post.request=/api/v1/van/search,/api/v1/van/query,/api/v1/pseudomerchant,/api/v1/context/merchants,/api/v1/reports/txn/download,/api/v1/reports/settlement/download,/api/v1/reports/refund/download,/api/v1/reports/chargeback/download,/api/v1/reports/wallet/cashback/download,/api/v1/download/notifications,/api/v1/subwallet/download,/api/v1/reports/wallet/refund/download,/api/v1/reports/wallet/settlement/download,/api/v1/reports/wallet/txn/download,/api/v1/wallet/dashboard/summary/list,/api/v1/wallet/dashboard/summary,/api/v1/subwallet/txnhistory,/api/v2/order/aggregator/download,/api/v2/subwallet/txnhistory,/api/v2/subwallet/download,/api/v2/order/summary,/api/v2/settlement/bill/summary,/api/v2/settlement/bill/list,/api/v2/order/list/download,/api/v2/subwallet/txnhistory,/api/v1/payment/files,/api/v2/invoice/fetch,/api/v2/merchantprofile/commission,/api/v2/order/list,/api/v2/settlement/txn/list,/api/v2/order/detail,/api/v2/order/count,/api/v3/order/detail,/api/v2/settlement/download,/api/v3/settlement/download,/api/v3/subwallet/txnhistory,/api/v3/subwallet/download,/api/v2/analytics/txn/query,/api/v2/analytics/bank/query,/api/v2/settlement/download/custom,/api/v2/settings/report/update,/api/v1/dispute/list/download,/api/v2/order/list/download,/api/v3/settlement/download,/api/v1/subscription/fetch,/api/v1/subscription/payment/list,/api/v1/analytics/reports/download,/api/v1/nlevelagg/fetch/store/mids

aws.secret.manager.path=/test/merchant-aggregator/aggregator-gateway/perf
aws.region=ap-south-1

#Force Update properties
NonSD_CAPP_IOS=8.14.2
50K_CAPP_IOS=8.14.2
500K_CAPP_IOS=8.14.2
100K_CAPP_IOS=8.14.2
UNLIMITED_SD_CAPP_IOS=8.14.2
NonSD_CAPP_ANDROID=8.14.4
50K_CAPP_ANDROID=8.14.4
500K_CAPP_ANDROID=8.14.4
100K_CAPP_ANDROID=8.14.4
UNLIMITED_SD_CAPP_ANDROID=8.14.4

NonSD_P4B_IOS=4.17.0
50K_P4B_IOS=4.17.0
500K_P4B_IOS=4.17.0
100K_P4B_IOS=4.17.0
UNLIMITED_SD_P4B_IOS=4.17.0

msupercash.base.url=https://promo-msupercash-staging.paytm.com
msupercash.jwt.client=MSUPERCASH

digitalproxy.base.url=https://digitalproxy-staging.paytm.com

ump.client.info=/api/v1/client
notifications.base.url=http://notifications-platformproducer-staging.paytm.com
address.update.push.notification.template.name=Address-Capture-P4B
deeplink.base.url = https://ump-staging.paytm.com

ivr.base.url = http://***********

fresh.desk.url.callback = https://paytm-merchantsandbox.freshdesk.com/
product.id.callback = 1082000000989
source.cst.callback = 110

status.cst.call.scheduler.api.failed = 75
status.cst.pending.for.callback = 72
status.cst.closed = 5
status.cst.resolved = 4

customer.issue.category.l1.callback = 081

edc10To20.allowed.last.digits = 0,1,2,3,4,5,6,7,8,9
p4b.force.update.check=true
p4b.force.update.mids.last.Digit=0,1
p4b.force.update.lower.version=5.8.0
p4b.force.update.upper.version=8.3.1
force.update.ppsl.lower.version=5.8.0
force.update.ppsl.upper.version=8.6.0
p4b.force.update.check.ppsl=true
force.update.generic.subHeading=To get live settlement status and other latest features
force.update.p4b.lower.and.upper.limit.subHeading=To get live settlement status and other latest features
force.update.undefined.merchant.p4b.lower.and.upper.limit.subHeading=To get live settlement status and other latest features
notify.outOfBattery10=false
notify.lowBattery20=false
notify.manualSwitchOff=false
p4b.nudges.cache.ttl:86400
devicePreference.cache.ttl=900
consent.cache.ttl=86400
ios.invalid.token.version.threshold=9.2.0
