#statsd
metric-client-config.prefix=merchant_bff_v2
metric-client-config.hostName=telegraf.monitoring.svc.cluster.local
metric-client-config.port=8125

enable.session.creation=true

#server.port=8081
spring.http.multipart.maxFileSize= 5MB
spring.http.multipart.maxRequestSize= 20MB

#tomcat properties
server.tomcat.threads.max=200
server.connection-timeout=10000

#To disbale cron to fetch expired session and delete them
spring.session.cleanup.cron.expression=0 0 5 28 2 ?
url.mapping=/bffv2/*