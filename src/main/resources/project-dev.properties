merchant.superright.id=**********
merchant.demo.user.id=**********

allow.cors.origins=http://localhost:3000 http://localhost:8080 https://dev.paytm.com http://dev.paytm.com https://s3.ap-south-1.amazonaws.com http://staging-merchant.paytm.com https://staging-merchant.paytm.com https://icicibank-staging.paytm.com https://ump-staging.paytm.com
allow.csrf.origins=
allow.iframe.origins=https://icicibank-staging.paytm.com https://ump-staging.paytm.com
allow.post.request=/api/v1/context/merchants,/api/v1/reports/txn/download,/api/v1/reports/settlement/download,/api/v1/reports/refund/download,/api/v1/reports/chargeback/download,/api/v1/reports/wallet/cashback/download,/api/v1/download/notifications,/api/v1/subwallet/download,/api/v1/reports/wallet/refund/download,/api/v1/reports/wallet/settlement/download,/api/v1/reports/wallet/txn/download,/api/v2/order/list,/api/v2/order/list/download,/api/v2/order/summary,/api/v2/settlement/bill/list,/api/v2/settlement/bill/summary,/api/v2/settlement/txn/list,/api/v2/settlement/download,/api/v1/dispute/list/download,/api/v3/order/detail
api.skip.merchant.association.url=
csrf.enable=true
enable.session.creation=true
monitor.log.body.length=10000
origin.filter=false

#redis
spring.redis.sentinel.master=cluster
spring.redis.sentinel.nodes=localhost
user.cache.ttl=1800
is.sentinel.enable=no
spring.redis.host=localhost
spring.redis.port=6379
spring.jedispool.connect.wait.time=5000
spring.jedispool.minIdleConnections=5
spring.jedispool.maxIdleConnections=20
spring.jedispool.maxTotalConnections=20
spring.redis.socket.readtimeout=500

# Cache TTLs
consent.cache.ttl=86400

ump.user.context=/api/v1/context
ump.base.url=https://ump-staging.paytm.com


subscription.base.url=https://subscriptions-staging.paytm.com
subscription.rental.jwt.secret=s6R5p36e6a8nRJOjlxt6O6uRWUbf2Q1KW1bV2od2+gdudrFfkAPHkhBRos5fPcanE/OuS3VQZoU=


sms.subscription.segment.id=975785
sms.subscription.commission.value=25

#cleverTap details
cleverTap.base.url=https://in1-paytm.api.clevertap.com

#OCR Configuration
ocr.client.id=dev-ocr-client-id
ocr.base.url=https://bank-ocr-app.internal.ap-south-1.staging.osmose.risk.pai.mypaytm.com
bank.proof.cache.ttl=3600

#OCR HTTP Configuration
http.timeout.ocr=5000
http.connect.timeout.ocr=5000
http.max.conn.per-route.ocr=200
http.max.conn.total.ocr=200

#App Base URL for callbacks
app.base.url=https://dev.paytm.com
cleverTap.accountId=WWK-K4Z-995Z
cleverTap.context=override|paytm|315642
freeze.sms.banner.id=889812

boss.client.key=WidnixnDo2780hILxdvvQXu9shJ9tIZnSsX4aEe9aKoOg5n7CtkijYqCb0ijNe7SE1qOu38JVU+gfx8G89oWvQ==
#ups Integration property
ups.base.url=https://ups-staging-external.paytm.com

promo.engine.base.url=http://*************
promo.engine.base.path=/v1/mpromocard/s2s/
promo.engine.issuer=promotion_team
//boss.base.url=https://bo-staging.paytm.in
boss.base.url=https://boss-qa-int.paytm.com

kyb.base.url=https://cif-staging.paytm.in
kyb.client.id=ump
kyb.client.uid=**********

pg.reward.base.url=http://app-stg.loyalty-internal-stg-cloud.paytm.com

cst.base.url=https://digitalproxy-staging.paytm.com
internal.user.permission=PSEUDOMERCHANT
allow.pseudo.merchant.post.request=/api/v1/van/search,/api/v1/van/query,/api/v1/pseudomerchant,/api/v1/context/merchants,/api/v1/reports/txn/download,/api/v1/reports/settlement/download,/api/v1/reports/refund/download,/api/v1/reports/chargeback/download,/api/v1/reports/wallet/cashback/download,/api/v1/download/notifications,/api/v1/subwallet/download,/api/v1/reports/wallet/refund/download,/api/v1/reports/wallet/settlement/download,/api/v1/reports/wallet/txn/download,/api/v1/wallet/dashboard/summary/list,/api/v1/wallet/dashboard/summary,/api/v1/subwallet/txnhistory,/api/v2/order/aggregator/download,/api/v2/subwallet/txnhistory,/api/v2/subwallet/download,/api/v2/order/summary,/api/v2/settlement/bill/summary,/api/v2/settlement/bill/list,/api/v2/order/list/download,/api/v2/subwallet/txnhistory,/api/v1/payment/files,/api/v2/invoice/fetch,/api/v2/merchantprofile/commission,/api/v2/order/list,/api/v2/settlement/txn/list,/api/v2/order/detail,/api/v2/order/count,/api/v3/order/detail,/api/v2/settlement/download,/api/v3/settlement/download,/api/v3/subwallet/txnhistory,/api/v3/subwallet/download,/api/v2/analytics/txn/query,/api/v2/analytics/bank/query,/api/v2/settlement/download/custom,/api/v2/settings/report/update,/api/v1/dispute/list/download,/api/v2/order/list/download,/api/v3/settlement/download,/api/v1/subscription/fetch,/api/v1/subscription/payment/list,/api/v1/analytics/reports/download,/api/v1/nlevelagg/fetch/store/mids

cst.mgw.base.url=https://cststaging.paytm.com
fresh.desk.url=https://paytm-merchantsandbox.freshdesk.com/
fsm.base.url=https://fse-staging.paytm.com
product.id=*************

aws.secret.manager.path=/merchant-aggregator/aggregator-gateway/qa
aws.region=ap-south-1

#Force Update properties
NonSD_CAPP_IOS=8.14.2
50K_CAPP_IOS=8.14.2
500K_CAPP_IOS=8.14.2
100K_CAPP_IOS=8.14.2
UNLIMITED_SD_CAPP_IOS=8.14.2
NonSD_CAPP_ANDROID=8.14.4
50K_CAPP_ANDROID=8.14.4
500K_CAPP_ANDROID=8.14.4
100K_CAPP_ANDROID=8.14.4
UNLIMITED_SD_CAPP_ANDROID=8.14.4

NonSD_P4B_IOS=4.17.0
50K_P4B_IOS=4.17.0
500K_P4B_IOS=4.17.0
100K_P4B_IOS=4.17.0
UNLIMITED_SD_P4B_IOS=4.17.0

msupercash.base.url=http://*************
msupercash.jwt.client=MSUPERCASH
digitalproxy.base.url=https://digitalproxy-staging.paytm.com

maquette.base.url=https://maquette-api.internal.ap-south-1.staging.osmose.risk.pai.mypaytm.com

ump.client.info=/api/v1/client
pg.reward.client.id=234545653456562366578
pg.reward.client.id=234545653456562366578
freshdesk.url=https://paytm-merchantsandbox.freshdesk.com
notifications.base.url=http://notifications-platformproducer-staging.paytm.com
address.update.push.notification.template.name=Address-Capture-P4B
deeplink.base.url = https://ump-staging.paytm.com

pg.reward.client.id=234545653456562366578
freshdesk.url=https://paytm-merchantsandbox.freshdesk.com

jwt.client.id=p4b_CST_client_staging
jwt.secret=JlWk5ypip5ZJtehF1dod0bwL35hxroypoT9nbuibSegTx851Hdo2Pjb7X2GYQQD2hfqPeQWFW1D/6qFMqxbZwA==
jdbc.master.url=**************************************************?useSSL=false&allowPublicKeyRetrieval=true
jdbc.slave.url=**************************************************
jdbc.user=app_staging
jdbc.password=PAaA#p48.>?<;:dG6

spring.redis.cluster.enable=false
spring.redis.cluster.enableForContext=false
spring.redis.cluster.nodes=
spring.redis.cluster.nodesForContext=
spring.redis.cluster.maxRedirects=3
spring.redis.cluster.maxRedirectsForContext=3

pg.reward.client.id=

client.cache.ttl=180


kyb.address.base.url=https://address-stage-int.paytm.com
central.tool.kit.base.url=https://central-tools-staging.paytm.com
eos.base.url=https://pgp-staging.paytm.in

edc.client=P4BIN004473
edc.jwt.secret=yQhDXG9zr8yXq4473
central.toolkit.client.id=p4b
central.toolkit.secret=n8q9yf3iNgnahjtFy3yu

cst.l1.IssueCategory=Merchant Profile
cst.l2.IssueCategory=Geofencing Issue
cst.l3.IssueCategory=DIY address update

cst.service.client.id=merchant_bff
cst.service.client.secret=vPARhc5c7jxZRW.7XpBa9WJkfTpsuAS2tz7Wxu3FP.AKwt4VtrpHDhHjxcXGQhe3
cst.secret.key=

cst.callService.base.url=https://cststaging.paytm.com/cst-mgw-auth/cst-call-service/staging
cst.call.service.client.id=merchant-bff-call

cst.client.id.v2=p4b-geofence-client
fsm.cust.id=*************
fsm.client.id=p4b


#support homepage
soundbox.storefront.id=1407710
card.machine.storefront.id=1407714
payment.settlement.storefront.id=1407711
bussiness.loan.storefront.id=1407712
account.setting.storefront.id=1407713
deals.storefront.id=1407715
others.storefront.id=1407716
store.front.base.url=http://storefront-staging.paytm.internal
survey.base.url=https://survey-staging.paytm.com
store.front.secret=imaJUqhPmCT9ktVR
store.front.client=p4b
store.front.issuer=storefront
freshwork.client.id=
freshwork.jwt.secret=
open.ticket.status=2,17,80,67,78,77,36,51,49,56,9,40,21,97,25,82,62,89,11,70,47,7,53,50,99,106,52,14,44,68,38,6,24,42,12,35,27,108,81,83,72,46,13,3,63,100,73,48,45,60,65,85,8,26,71,18,33,57,88,86,76,101,32,59,41,10,39,90,54,23,64,66,79,103,28
resolved.ticket.status=4,95,30,31,29,15,96,84,61,34,22,55
closed.ticket.status=5
whiteListed.mid.list=

fresh.desk.url.callback = https://paytm-merchantsandbox.freshdesk.com/
product.id.callback = *************
source.cst.callback = 110

status.cst.call.scheduler.api.failed = 75
status.cst.pending.for.callback = 72
status.cst.closed = 5
status.cst.resolved = 4

customer.issue.category.l1.callback = 081

#limit upgrade task
#24hr
limit.upgrade.cache.ttl=86400
limit.upgrade.queue.cache.ttl=86400
onboarding.engine.base.url=

edc10To20.allowed.last.digits = 0,1,2,3,4,5,6,7,8,9
refresh.scope.testing=data coming from code dev
p4b.force.update.check=true
p4b.force.update.mids.last.Digit=0,1
p4b.force.update.lower.version=5.8.0
p4b.force.update.upper.version=8.3.1
force.update.ppsl.lower.version=5.8.0
force.update.ppsl.upper.version=8.6.0
p4b.force.update.check.ppsl=true
force.update.generic.subHeading=To get live settlement status and other latest features
force.update.p4b.lower.and.upper.limit.subHeading=To get live settlement status and other latest features
force.update.undefined.merchant.p4b.lower.and.upper.limit.subHeading=To get live settlement status and other latest features
notify.outOfBattery10=false
notify.lowBattery20=false
notify.manualSwitchOff=false
ios.invalid.token.version.threshold=9.2.0
deepdischarge.time.limit=24
switchoff.time.limit=120

payment.hold.cache.ttl=1
payment.hold.close.cache.ttl=1
business.proof.cache.ttl=1
business.proof.active.cache.ttl=1
payment.hold.enable=true
success.settlement.card.ttl=1
success.settlement.card.partial.ttl=1
dms.base.url=https://dms-staging.paytm.com