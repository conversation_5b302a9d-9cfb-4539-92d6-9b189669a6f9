merchant.superright.id=********
merchant.demo.user.id=**********

allow.cors.origins=https://developer.paytm.com https://business.paytm.com https://merchant.paytm.com https://fse-mgmt-frontend.paytm.com https://fse-beta-mgmt-frontend.paytm.com https://beta.paytm.com https://paytm.com
allow.iframe.origins=https://developer.paytm.com https://business.paytm.com https://accounts.paytm.com
allow.post.request=/api/v1/context/merchants,/api/v1/reports/txn/download,/api/v1/reports/settlement/download,/api/v1/reports/refund/download,/api/v1/reports/chargeback/download,/api/v1/reports/wallet/cashback/download,/api/v1/download/notifications,/api/v1/subwallet/download,/api/v1/reports/wallet/refund/download,/api/v1/reports/wallet/settlement/download,/api/v1/reports/wallet/txn/download,/api/v2/subusers/consent,/api/v2/order/summary,/api/v2/settlement/bill/list,/api/v2/settlement/bill/summary,/api/v2/settlement/txn/list,/api/v2/settlement/download,/api/v1/dispute/list/download,/api/v3/order/detail,/api/v1/app/home,/api/v2/app/home,/api/v2/app/home/<USER>/api/v2/app/home/<USER>/api/v2/settlement/download/custom,/api/v2/settings/report/update,/api/v3/settlement/download,/api/v2/order/list/download,/api/v1/onboard/upgrade,/api/v3/app/notification/tag,/api/v3/app/notification/untag
csrf.enable=true

spring.redis.sentinel.master=cluster
spring.redis.sentinel.nodes=*************:26379,*************:26379,*************:26379
user.cache.ttl=3600
is.sentinel.enable=no
#spring.redis.host=prod-comm-ump-aggregatorredis-aws.0xtf5b.ng.0001.aps1.cache.amazonaws.com
spring.redis.host=
spring.redis.port=6379
spring.jedispool.connect.wait.time=100
spring.jedispool.minIdleConnections=10
spring.jedispool.maxIdleConnections=60
spring.jedispool.maxTotalConnections=60
spring.redis.socket.readtimeout=50

# Cache TTLs
consent.cache.ttl=86400

ump.user.context=/api/v1/context
ump.base.url=https://ump-internal.paytm.com

subscription.base.url=https://subscriptions-internal.paytm.com

sms.subscription.segment.id=975785
sms.subscription.commission.value=25

#cleverTap details
cleverTap.base.url=https://in1-paytm.api.clevertap.com
cleverTap.context=override|paytm|315642
freeze.sms.banner.id=889812

#OCR Configuration
ocr.client.id=prod-ocr-client-id
ocr.base.url=https://bank-ocr-app.internal.ap-south-1.production.oclre2.risk.pai.mypaytm.com
bank.proof.cache.ttl=3600

#OCR HTTP Configuration
http.timeout.ocr=5000
http.connect.timeout.ocr=5000
http.max.conn.per-route.ocr=200
http.max.conn.total.ocr=200

#App Base URL for callbacks
app.base.url=https://paytm.com

#boss.base.url=https://boss-internal.paytm.in
boss.base.url=https://boss-int-exp.paytm.com

#ups Integration property
ups.base.url=https://ups-internal.paytm.com



promo.engine.base.url=https://msupercash-serving.paytm.com
promo.engine.base.path=/v1/mpromocard/s2s/
promo.engine.issuer=promotion_team

kyb.base.url=https://cif-int.paytm.com
kyb.client.id=ump
kyb.client.uid=*********

#pg.reward.base.url=http://app.loyalty-internal-cloud.paytm.com
pg.reward.base.url=https://loyalty-points-prod-int.paytm.com
pg.reward.client.id=665634452343453446542
cst.base.url = https://digitalproxy.paytm.com
internal.user.permission=PSEUDOMERCHANT
allow.pseudo.merchant.post.request=/api/v1/van/search,/api/v1/van/query,/api/v1/pseudomerchant,/api/v1/context/merchants,/api/v1/reports/txn/download,/api/v1/reports/settlement/download,/api/v1/reports/refund/download,/api/v1/reports/chargeback/download,/api/v1/reports/wallet/cashback/download,/api/v1/download/notifications,/api/v1/subwallet/download,/api/v1/reports/wallet/refund/download,/api/v1/reports/wallet/settlement/download,/api/v1/reports/wallet/txn/download,/api/v1/wallet/dashboard/summary/list,/api/v1/wallet/dashboard/summary,/api/v1/subwallet/txnhistory,/api/v2/order/aggregator/download,/api/v2/subwallet/txnhistory,/api/v2/subwallet/download,/api/v2/order/summary,/api/v2/settlement/bill/summary,/api/v2/settlement/bill/list,/api/v2/order/list/download,/api/v2/subwallet/txnhistory,/api/v1/payment/files,/api/v2/invoice/fetch,/api/v2/merchantprofile/commission,/api/v2/order/list,/api/v2/settlement/txn/list,/api/v2/order/detail,/api/v2/order/count,/api/v3/order/detail,/api/v2/settlement/download,/api/v3/settlement/download,/api/v3/subwallet/txnhistory,/api/v3/subwallet/download,/api/v2/analytics/txn/query,/api/v2/analytics/bank/query,/api/v2/settlement/download/custom,/api/v2/settings/report/update,/api/v1/dispute/list/download,/api/v2/order/list/download,/api/v3/settlement/download,/api/v1/subscription/fetch,/api/v1/subscription/payment/list,/api/v1/analytics/reports/download,/api/v1/nlevelagg/fetch/store/mids

cst.mgw.base.url=https://cst-mgw.paytm.com
fresh.desk.url=https://paytm-merchant.freshdesk.com/
product.id=***********
fsm.base.url=https://fse.paytm.com

aws.secret.manager.path=/merchant-aggregator/aggregator-gateway/prod
aws.region=ap-south-1

#Force Update properties
NonSD_CAPP_IOS=8.14.2
50K_CAPP_IOS=8.14.2
500K_CAPP_IOS=8.14.2
100K_CAPP_IOS=8.14.2
UNLIMITED_SD_CAPP_IOS=8.14.2
NonSD_CAPP_ANDROID=8.14.4
50K_CAPP_ANDROID=8.14.4
500K_CAPP_ANDROID=8.14.4
100K_CAPP_ANDROID=8.14.4
UNLIMITED_SD_CAPP_ANDROID=8.14.4


NonSD_P4B_IOS=4.26.0
50K_P4B_IOS=4.26.0
500K_P4B_IOS=4.26.0
100K_P4B_IOS=4.26.0
UNLIMITED_SD_P4B_IOS=4.26.0

NonSD_P4B_ANDROID=4.26.0
50K_P4B_ANDROID=4.26.0
500K_P4B_ANDROID=4.26.0
100K_P4B_ANDROID=4.26.0
UNLIMITED_SD_P4B_ANDROID=4.26.0

msupercash.base.url=https://msupercash-serving.paytm.com
msupercash.jwt.client=MSUPERCASH

digitalproxy.base.url=http://bwp.channels.prod.channels.paytm.com

#maquette.base.url=https://maquette-api.internal.ap-south-1.production.ocl.risk.pai.mypaytm.com
#maquette.base.url=https://maquette-api.internal.ap-south-1.production.osmose.risk.pai.mypaytm.com
maquette.base.url=https://risk-internal.paytm.com

ump.client.info=/api/v1/client

spring.redis.cluster.enable=true
spring.redis.cluster.enableForContext=true
spring.redis.cluster.nodes=prod-comm-ump-aggregatorrediscluster-aws.0xtf5b.clustercfg.aps1.cache.amazonaws.com:6379
#spring.redis.cluster.nodesForContext=*************:7000,*************:7000,*************:7000,*************:7000,*************:7000
spring.redis.cluster.nodesForContext=agg-v2-redis.prod.paytmdgt.io:7000
spring.redis.cluster.maxRedirects=3
spring.redis.cluster.maxRedirectsForContext=3

jdbc.master.url=****************************************************
jdbc.user=p4bdb_appuser
jdbc.slave.url=***************************************************

notifications.base.url= http://notificationplatform-internal.paytm.com
address.update.push.notification.template.name=Address-Capture-P4B
deeplink.base.url=https://dashboard.paytm.com
client.cache.ttl=21600
cst.service.client.id=merchant_bff
cst.base.url.class=https://cst-mgw.paytm.com/cst-mgw-auth/cst-call-service/

cst.callService.base.url=https://cst-mgw.paytm.com/cst-mgw-auth/cst-call-service
cst.call.service.client.id=merchant-bff-call

freshdesk.url=https://paytm-merchant.freshdesk.com/


kyb.address.base.url=https://address-int.paytm.com
central.tool.kit.base.url=https://central-tools-internal.paytm.com
eos.base.url=https://securegw-edc-int-alb.paytm.com/eos
edc.client=P4BIN004445
central.toolkit.client.id=p4b

cst.l1.IssueCategory=Merchant Profile
cst.l2.IssueCategory=Geofencing Issue
cst.l3.IssueCategory=DIY address update

cst.client.id.v2=p4b-geofence-client

#support homepage
soundbox.storefront.id=1407710
card.machine.storefront.id=1407714
payment.settlement.storefront.id=1407711
bussiness.loan.storefront.id=1407712
account.setting.storefront.id=1407713
deals.storefront.id=1407715
others.storefront.id=1407716
store.front.base.url=http://storefront-internal.paytm.com
survey.base.url=https://survey.paytm.com
store.front.client=p4b
store.front.issuer=storefront
open.ticket.status=2,17,80,67,78,77,36,51,49,56,9,40,21,97,25,82,62,89,11,70,47,7,53,50,99,106,52,14,44,68,38,6,24,42,12,35,27,108,81,83,72,46,13,3,63,100,73,48,45,60,65,85,8,26,71,18,33,57,88,86,76,101,32,59,41,10,39,90,54,23,64,66,79,103,28
resolved.ticket.status=4,95,30,31,29,15,96,84,61,34,22,55
closed.ticket.status=5
fsm.cust.id=*************
fsm.client.id=p_4_b

#limit upgrade task
#24hr
limit.upgrade.cache.ttl=86400
limit.upgrade.queue.cache.ttl=86400
onboarding.engine.base.url=https://onboarding-internal.paytm.com

fresh.desk.url.callback = https://paytm-merchant.freshdesk.com/
product.id.callback = ***********
source.cst.callback = 113

status.cst.call.scheduler.api.failed = 132
status.cst.pending.for.callback = 129
status.cst.closed = 5
status.cst.resolved = 4

customer.issue.category.l1.callback = 087

edc10To20.allowed.last.digits = 0,1,2,3,4,5,6,7,8,9
issue.category.l1.callback = 087

#1 year
payment.hold.cache.ttl =31536000
#2 days
payment.hold.close.cache.ttl=172800
payment.hold.enable=true

#3 day
business.proof.cache.ttl=359200
#90 days
business.proof.active.cache.ttl=7776000
refresh.scope.testing=data coming from code prod
p4b.force.update.check=false
p4b.force.update.mids.last.Digit=0,1,2,3,4,5,6,7,8,9
p4b.force.update.lower.version=5.8.0
p4b.force.update.upper.version=8.6.6
force.update.ppsl.lower.version=5.8.0
force.update.ppsl.upper.version=8.6.0
p4b.force.update.check.ppsl=true
force.update.generic.subHeading=To get live settlement status and other latest features
force.update.p4b.lower.and.upper.limit.subHeading=To get live settlement status and other latest features
force.update.undefined.merchant.p4b.lower.and.upper.limit.subHeading=To get live settlement status and other latest features
notify.outOfBattery10=false
notify.lowBattery20=false
notify.manualSwitchOff=false
p4b.nudges.cache.ttl:86400
devicePreference.cache.ttl=900
ios.invalid.token.version.threshold=9.2.0

#14days
success.settlement.card.ttl=20160
#2days
success.settlement.card.partial.ttl=2880
lower.threshold.version.limit=8.6.6
upper.threshold.version.limit=9.14.3
force.update.p4b.lower.and.upper.limit.subHeading.new=To get live payments & settlement status and other latest features
force.update.enabled=true
sb.deepdischarge.time.limit=24
sb.switchoff.time.limit=120
sb.deepdischarge.enabled=true
sb.switchOff.enabled=true
sb.switchOff.troubleshoot.enabled=true
dms.base.url=https://dms.paytm.com
voucher.base.url=https://paytm.com
ai.chatbot.deeplink=paytmba://business-app/ump-web?url=https://dashboard.paytm.com/app?redirectUrl=merchant-support/soundbox/hardware-issue?src=p4b&channel=p4b&deviceId=
unused.feature.card=CM_WIFI_NETWORK_25,CM_BATTERY_10TO15,CM_BATTERY_10TO20
checkout.base.url=https://checkout-internal.paytm.com
checkout.client.key=P4B
oms.base.url=http://order-internal.paytm.com
checkout.callback.client=P4B_Checkout_client_prod_fsid_147