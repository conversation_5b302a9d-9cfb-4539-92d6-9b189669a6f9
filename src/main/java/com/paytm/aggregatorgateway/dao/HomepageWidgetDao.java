package com.paytm.aggregatorgateway.dao;

import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;

import java.util.Date;
import java.util.List;

public interface HomepageWidgetDao {

    void addWidget(String mid, String identifierKey, Long custid, String status, String featureType, String metaData, Date cardExpiryTime, String identifierValue, int ttl, String type) throws Exception;

    List<WidgetInfoDTO> fetchWidgetDetails(String mid, String identifierValue, Long custid, String featureType, String status, String type) throws Exception;

    void updateStatusAndFlag(String mid, String identifierValue, Long custid, String featureType, String status, Date cardExpiryTime, String statusToCheck, String type, String metaData) throws Exception ;

    void setCardInactive(String mid, String identifierValue, Long custid, String inActiveStatus, Date cardExpiryTimeToSet, String activeStatus);

    List<WidgetInfoDTO>  fetchWidgetDetailsForPaymentHold(String mid, String identifierValue, String featureType) throws Exception;

    void updatePaymentHoldCard(String mid, String identifierValue, String featureType, Date cardExpiryTime, String type, String metadata) throws Exception ;

    void updateMultipleCardType(String mid, String identifierValue, String status, Date cardExpiryTime, String statusToCheck, String type, List<String> featureTypeList) throws Exception;

    boolean isEntryExist(String mid) throws Exception;

    }
