package com.paytm.aggregatorgateway.dao.impl;

import com.paytm.aggregatorgateway.constants.Queries;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import static com.paytm.aggregatorgateway.constants.ApplicationConstants.P4B_NUDGES_MID;
@Slf4j
@Repository
public class HomepageWidgetDaoImpl implements HomepageWidgetDao {

    private static final Logger LOGGER = LogManager.getLogger(HomepageWidgetDaoImpl.class);

    @Autowired
    @Qualifier("masterJdbcTemplate")
    JdbcTemplate jdbcTemplate;
    @Autowired
    @Qualifier("slaveJdbcTemplate")
    JdbcTemplate jdbcTemplateSlave;

    @Autowired
    private RedisHelper redisHelper;
    private void removeNudgeCache(String mid){
        // invalidate nudges cache for mid
        try {
            log.info("Clearing nudge cache from redis for mid: {}.", mid);
            String customKey = P4B_NUDGES_MID + mid;
            redisHelper.evictNudges(customKey, mid);
        } catch (Exception e) {
            log.error("Error while evicting nudges cache for mid {}", mid);
        }
    }

    @Override
    public void addWidget(String mid, String identifierKey, Long custid, String status, String featureType, String metaData, Date cardExpiryTime, String identifierValue, int ttl,String type) throws Exception {
      try{
            removeNudgeCache(mid);
            jdbcTemplate.update(Queries.INSERT_WIDGET, mid, custid, cardExpiryTime, identifierKey, identifierValue, ttl, status, featureType, type,metaData);
        } catch (Exception e) {
        LOGGER.error("DB Error while adding recod {}", e);
        throw e;
         }
    }
    @Override
    public List<WidgetInfoDTO> fetchWidgetDetails(String mid, String identifierValue, Long custid, String featureType, String status, String type) throws Exception{
        try{
        if(custid!=null)
            return jdbcTemplateSlave.query(Queries.FETCH_WIDGET_DETAIL_BY_CUSTID, new Object[] {mid,custid,featureType,identifierValue,status, type},widgetInfoMapper);
        else
            return jdbcTemplateSlave.query(Queries.FETCH_WIDGET_DETAIL, new Object[] {mid,featureType,identifierValue,status, type},widgetInfoMapper);
        } catch (Exception e) {
            LOGGER.error("DB Error while fetching record {}", e);
            throw e;
        }
    }

    public List<WidgetInfoDTO> fetchWidgetDetailsForPaymentHold(String mid, String identifierValue, String featureType) throws Exception{
        try{
                return jdbcTemplateSlave.query(Queries.FETCH_WIDGET_DETAIL_PAYMENT_HOLD, new Object[] {mid},widgetInfoMapper); //identifierValue
        } catch (Exception e) {
            LOGGER.error("DB Error while fetching record {}", e);
            throw e;
        }
    }

    RowMapper<WidgetInfoDTO> widgetInfoMapper = new RowMapper<WidgetInfoDTO>() {
        @Override
        public WidgetInfoDTO mapRow(ResultSet rs, int rowNum) throws SQLException {
            WidgetInfoDTO widgetInfo = new WidgetInfoDTO();
            widgetInfo.setStatus(rs.getString("STATUS"));
            widgetInfo.setFeatureType(rs.getString("FEATURE_TYPE"));
            widgetInfo.setExpiryTime(rs.getTimestamp("EXPIRY_TIME"));
            widgetInfo.setType(rs.getString("TYPE"));
            widgetInfo.setIdentifierValue(rs.getString("IDENTIFIER_VALUE"));
            widgetInfo.setMetadata(rs.getString("METADATA"));
            LOGGER.info("Widget info from DB "+widgetInfo.getStatus()+" "+widgetInfo.getExpiryTime()+" "+widgetInfo.getFeatureType());
            return widgetInfo;
        }
    };

    public void updateStatusAndFlag(String mid, String identifierValue, Long custid, String featureType, String status, Date cardExpiryTime, String statusToCheck, String type, String metaData) throws Exception {
        try {
            removeNudgeCache(mid);
            int count = 0;
            if (custid != null)
                count = jdbcTemplate.update(Queries.UPDATE_WIDGET_FLAG_BY_CUSTID, new Object[]{status, cardExpiryTime, metaData, mid, custid, featureType, identifierValue,statusToCheck, type});
            else
                count = jdbcTemplate.update(Queries.UPDATE_WIDGET_FLAG, new Object[]{status, cardExpiryTime, metaData, mid, featureType, identifierValue,statusToCheck, type});

            LOGGER.info("record updated count is {}", count);
        } catch (Exception e) {
            LOGGER.error("DB Error while record update {}", e);
            throw  e;
        }
    }

    public void updatePaymentHoldCard(String mid, String identifierValue, String featureType, Date cardExpiryTime, String type, String metadata) throws Exception {
        try {
            removeNudgeCache(mid);
            int count = 0;
            if (cardExpiryTime != null)
                count = jdbcTemplate.update(Queries.UPDATE_PAYMENT_HOLD_EXPIRYTIME, new Object[]{featureType,cardExpiryTime, metadata, mid, identifierValue, type});
            else
                count = jdbcTemplate.update(Queries.UPDATE_PAYMENT_HOLD, new Object[]{featureType, metadata, mid, identifierValue, type});

            LOGGER.info("record updated count is {}", count);
        } catch (Exception e) {
            LOGGER.error("DB Error while record update {}", e);
            throw  e;
        }
    }

    @Override
    public void setCardInactive(String mid, String identifierValue, Long custid, String status, Date cardExpiryTime, String statusToCheck) {

        if (custid != null)
            jdbcTemplate.update(Queries.UPDATE_WIDGET_BY_IDENTIFIER_CUSTID, new Object[]{status, cardExpiryTime, mid, custid, identifierValue,statusToCheck});
        else
          jdbcTemplate.update(Queries.UPDATE_WIDGET_BY_IDENTIFIER, new Object[]{status, cardExpiryTime, mid, identifierValue,statusToCheck});
    }

    public void updateMultipleCardType(String mid, String identifierValue, String status, Date cardExpiryTime, String statusToCheck, String type, List<String> featureTypeList) throws Exception{
        removeNudgeCache(mid);
        String paramsCount = String.join(",", Collections.nCopies(featureTypeList.size(), "?"));
        String query = String.format(Queries.UPDATE_MULTIPLE_CARD_TYPE, paramsCount);
        List<Object> keys = new ArrayList<>();
        keys.add(status);
        keys.add(cardExpiryTime);
        keys.add(mid);
        keys.add(identifierValue);
        keys.add(statusToCheck);
        keys.add(type);
        keys.addAll(featureTypeList);
        jdbcTemplate.update(query, keys.toArray());
    }

    @Override
    public boolean isEntryExist(String mid) throws Exception {

        try {
            String sql = "SELECT 1 FROM MID_ICON_ISSUE WHERE mid = ? LIMIT 1";
            List<Integer> result = jdbcTemplate.query(sql, (rs, rowNum) -> rs.getInt(1), mid);
            return !result.isEmpty();
        } catch (Exception e) {
            log.info("Error while checking entry existence for mid: {}",  mid, e);
            return false;
        }
    }
}
