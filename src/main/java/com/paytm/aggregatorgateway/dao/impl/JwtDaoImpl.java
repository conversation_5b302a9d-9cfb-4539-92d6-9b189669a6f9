package com.paytm.aggregatorgateway.dao.impl;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.Queries;
import com.paytm.aggregatorgateway.dao.JwtDao;
import com.paytm.aggregatorgateway.dto.ClientInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class JwtDaoImpl implements JwtDao {

    @Autowired
    @Qualifier("slaveJdbcTemplate")
    JdbcTemplate jdbcTemplateSlave;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Cacheable(value="clientSecret",key = "#clientId", cacheManager = "redisCacheManagerForObjectCaching", unless = "#result==null")
    public Map<String,String> fetchClientSecret(String clientId) throws Exception {
        try {
            log.info("fetching client secret- "+clientId);
            ClientInfoDTO clientInfoDTO = jdbcTemplateSlave.queryForObject(Queries.FETCH_CLIENT_SECRET, new Object[]{clientId}, clientSecretMapper);
            return objectMapper.convertValue(clientInfoDTO, new TypeReference<Map<String, String>>() {
            });
        }catch(Exception e){
            log.error("DB exception while fetching client secret- "+e);
            throw e;
        }
    }

    RowMapper<ClientInfoDTO> clientSecretMapper = new RowMapper<ClientInfoDTO>() {
        @Override
        public ClientInfoDTO mapRow(ResultSet rs, int rowNum) throws SQLException {
            ClientInfoDTO clientInfo = new ClientInfoDTO();
            clientInfo.setClientId(rs.getString("CLIENT_ID"));
            clientInfo.setSecret(rs.getString("CLIENT_SECRET"));
            clientInfo.setPermissions(rs.getString("PERMISSIONS"));
            return clientInfo;
        }
    };
}