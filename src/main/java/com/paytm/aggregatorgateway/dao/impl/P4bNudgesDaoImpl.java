package com.paytm.aggregatorgateway.dao.impl;

import com.paytm.aggregatorgateway.dao.P4bNudgesDao;
import com.paytm.aggregatorgateway.dto.P4bNudges;
import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;

import static com.paytm.aggregatorgateway.constants.ApplicationConstants.P4B_NUDGES_MID;
@Slf4j
@Repository
public class P4bNudgesDaoImpl implements P4bNudgesDao {
    private static final Logger LOGGER = LogManager.getLogger(P4bNudgesDaoImpl.class);

    private static final String GET_P4B_NUDGE_INFO_QUERY = "SELECT * FROM P4B_NUDGES where " +
            "MID = ? AND TYPE = ? AND FEATURE_TYPE = ?";

    private static final String INSERT_QRY = "INSERT INTO P4B_NUDGES " +
            "(MID, TYPE, FEATURE_TYPE, CUSTID, TTL, STATUS, IDENTIFIER_KEY, IDENTIFIER_VALUE, METADATA, EXPIRY_TIME) " +
            "VALUES (?,?,?,?,?,?,?,?,?,?)";

    private static final String UPDATE_QRY = "UPDATE P4B_NUDGES " +
            "SET EXPIRY_TIME = ? WHERE ID = ?";

    private static final String GET_P4B_NUDGES_INFO_QUERY_SORTED = "SELECT * FROM P4B_NUDGES WHERE " +
            "MID = ? AND TYPE = ? AND EXPIRY_TIME > NOW() AND STATUS = 'ACTIVE' " +
            "AND FEATURE_TYPE IN (%s) ORDER BY IDENTIFIER_VALUE, UPDATED_AT DESC";


    @Autowired
    @Qualifier("masterJdbcTemplate")
    JdbcTemplate jdbcTemplate;
    @Autowired
    @Qualifier("slaveJdbcTemplate")
    JdbcTemplate jdbcTemplateSlave;

    @Autowired
    private RedisHelper redisHelper;
    private void removeNudgeCache(String mid){
        // invalidate nudges cache for mid
        try {
            log.info("Clearing nudge cache from redis for mid: {}.", mid);
            String customKey = P4B_NUDGES_MID + mid;
            redisHelper.evictNudges(customKey, mid);
        } catch (Exception e) {
            log.error("Error while evicting nudges cache for mid {}", mid);
        }
    }

    @Override
    public P4bNudges getP4BNudge(String mid, String type, String featureType) {
        return jdbcTemplateSlave.query(GET_P4B_NUDGE_INFO_QUERY, new Object[]{mid, type, featureType},
                resultSet -> {
                    P4bNudges p4bNudges = null;
                    while (resultSet.next()) {
                        p4bNudges = new P4bNudges();
                        p4bNudges.setId(resultSet.getLong("ID"));
                        p4bNudges.setMid(resultSet.getString("MID"));
                        p4bNudges.setType(resultSet.getString("TYPE"));
                        p4bNudges.setFeatureType(resultSet.getString("FEATURE_TYPE"));
                        p4bNudges.setCustid(resultSet.getLong("CUSTID"));
                        p4bNudges.setTtl(resultSet.getString("TTL"));
                        p4bNudges.setStatus(resultSet.getString("STATUS"));
                        p4bNudges.setIdentifierKey(resultSet.getString("IDENTIFIER_KEY"));
                        p4bNudges.setIdentifierValue(resultSet.getString("IDENTIFIER_VALUE"));
                        p4bNudges.setMetadata(resultSet.getString("METADATA"));
                        p4bNudges.setExpiryTime(resultSet.getTimestamp("EXPIRY_TIME").toLocalDateTime());
                        p4bNudges.setCreatedAt(resultSet.getTimestamp("CREATED_AT").toLocalDateTime());
                        p4bNudges.setUpdatedAt(resultSet.getTimestamp("UPDATED_AT").toLocalDateTime());
                    }
                    return p4bNudges;
                });
    }

    @Override
    public void updateP4BNudgeExpiry(Long id, LocalDateTime expiryTime) {
        try {
            removeNudgeCache(SecurityUtils.getCurrentMerchant().getMid());
            jdbcTemplate.update(UPDATE_QRY, expiryTime, id);
        } catch (Exception e) {
            LOGGER.error("DB Error while adding recod {}", e);
            throw e;
        }
    }

    @Override
    public void addP4BNudge(String mid, String type, String featureType, Long custid, Integer ttl, String status, String identifierKey, String identifierValue, String metaData, LocalDateTime expiryTime) {
        try {
            removeNudgeCache(mid);
            jdbcTemplate.update(INSERT_QRY, mid, type, featureType, custid, ttl, status, identifierKey, identifierValue, metaData, expiryTime);
        } catch (Exception e) {
            LOGGER.error("DB Error while adding recod {}", e);
            throw e;
        }
    }

    @Override
    public List<WidgetInfoDTO> fetchDeviceCard(String mid, String type, List<String> featureList) throws Exception {
        log.info("fetchDeviceCard {} ",featureList );
        String paramsCount = String.join(",", Collections.nCopies(featureList.size(), "?"));
        String query = String.format(GET_P4B_NUDGES_INFO_QUERY_SORTED, paramsCount);

        List<String> keys = new ArrayList<>();
        keys.add(mid);
        keys.add(type);
        keys.addAll(featureList);

        List<WidgetInfoDTO> allCards = jdbcTemplateSlave.query(query, keys.toArray(), widgetInfoMapper);

        Map<String, WidgetInfoDTO> latestCardsMap = new LinkedHashMap<>();

        for (WidgetInfoDTO card : allCards) {
            latestCardsMap.putIfAbsent(card.getIdentifierValue(), card);
        }

        return new ArrayList<>(latestCardsMap.values());
    }


    RowMapper<WidgetInfoDTO> widgetInfoMapper = new RowMapper<WidgetInfoDTO>() {
        @Override
        public WidgetInfoDTO mapRow(ResultSet rs, int rowNum) throws SQLException {
            WidgetInfoDTO widgetInfo = new WidgetInfoDTO();
            widgetInfo.setStatus(rs.getString("STATUS"));
            widgetInfo.setFeatureType(rs.getString("FEATURE_TYPE"));
            widgetInfo.setExpiryTime(rs.getTimestamp("EXPIRY_TIME"));
            widgetInfo.setType(rs.getString("TYPE"));
            widgetInfo.setIdentifierValue(rs.getString("IDENTIFIER_VALUE"));
            widgetInfo.setMetadata(rs.getString("METADATA"));
            return widgetInfo;
        }
    };

}
