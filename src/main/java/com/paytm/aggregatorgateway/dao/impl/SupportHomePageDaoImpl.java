package com.paytm.aggregatorgateway.dao.impl;

import com.paytm.aggregatorgateway.constants.Queries;
import com.paytm.aggregatorgateway.dao.SupportHomePageDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class SupportHomePageDaoImpl implements SupportHomePageDao {

    @Autowired
    @Qualifier("masterJdbcTemplate")
    JdbcTemplate jdbcTemplate;
    @Autowired
    @Qualifier("slaveJdbcTemplate")
    JdbcTemplate jdbcTemplateSlave;

    @Override
    public Boolean checkMidWhitelistedForCallBack(String mid, String campaignId) {
        try{
            return jdbcTemplateSlave.queryForObject(Queries.CHECK_MID_WHITELISTED_FOR_CALL_BACK, Boolean.class, mid, campaignId);
        } catch (DataAccessException dataAccessException){
            log.error("DB Error while checking for mid: {}", mid);
            throw dataAccessException;
        }
    }
}
