package com.paytm.aggregatorgateway.dao;

import com.paytm.aggregatorgateway.dto.P4bNudges;
import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface P4bNudgesDao {
    P4bNudges getP4BNudge(String mid, String type, String featureType);

    void updateP4BNudgeExpiry(Long id, LocalDateTime expiryTime);

    void addP4BNudge(String mid, String type, String featureType, Long custid, Integer ttl, String status, String identifierKey, String identifierValue, String metaData, LocalDateTime expiryTime);

    List<WidgetInfoDTO> fetchDeviceCard(String mid, String type, List<String> featureList) throws Exception;
}
