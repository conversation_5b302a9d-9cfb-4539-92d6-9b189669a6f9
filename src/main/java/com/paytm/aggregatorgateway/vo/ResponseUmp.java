package com.paytm.aggregatorgateway.vo;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseUmp implements Serializable {

	private static final long serialVersionUID = 1L;

	private String status;
	private String statusCode;
	private String statusMessage;
	private transient Object results;

	public ResponseUmp() {}

	public ResponseUmp(String status, String statusCode, String statusMessage, Object results) {
		this.status = status;
		this.statusCode = statusCode;
		this.statusMessage = statusMessage;
		this.results = results;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}

	public String getStatusMessage() {
		return statusMessage;
	}

	public void setStatusMessage(String statusMessage) {
		this.statusMessage = statusMessage;
	}

	public Object getResults() {
		return results;
	}

	public void setResults(Object results) {
		this.results = results;
	}

	@Override
	public String toString() {
		return "ResponseUmp [status=" + status + ", statusCode=" + statusCode + ", statusMessage=" + statusMessage
				+ ", results=" + results + "]";
	}

}
