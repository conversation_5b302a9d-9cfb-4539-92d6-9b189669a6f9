package com.paytm.aggregatorgateway.vo;

public class UmpErrorResponse {

    private String ERROR;

    private String statusMessage;

    private String statusCode;

    private String rootErrorMsg;

    public String getERROR() {
        return ERROR;
    }

    public void setERROR(String ERROR) {
        this.ERROR = ERROR;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getRootErrorMsg() {
        return rootErrorMsg;
    }

    public void setRootErrorMsg(String rootErrorMsg) {
        this.rootErrorMsg = rootErrorMsg;
    }

    @Override
    public String toString() {
        return "UmpErrorResponse{" +
                "ERROR='" + ERROR + '\'' +
                ", statusMessage='" + statusMessage + '\'' +
                ", statusCode='" + statusCode + '\'' +
                ", rootErrorMsg='" + rootErrorMsg + '\'' +
                '}';
    }
}
