package com.paytm.aggregatorgateway.vo;


import org.springframework.http.HttpMethod;

import java.io.Serializable;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.Map;

public class MonitorLog implements Serializable{

	private static final long serialVersionUID = 6673500833911505010L;

	private LocalDateTime startTime;
	private LocalDateTime endTime;
	private URI uri;
	private HttpMethod method;
	private Object request;
	private Map<String, String> requestHeaders;
	private Map<String, String> responseHeaders;
	private Object response;
	private int httpStatus;
	private StringBuilder exception;
	private String caller;
	private String uid;
	private Long eid;
	private String traceId;

	public LocalDateTime getStartTime() {
		return startTime;
	}

	public void setStartTime(LocalDateTime startTime) {
		this.startTime = startTime;
	}

	public LocalDateTime getEndTime() {
		return endTime;
	}

	public void setEndTime(LocalDateTime endTime) {
		this.endTime = endTime;
	}

	public URI getUri() {
		return uri;
	}

	public void setUri(URI uri) {
		this.uri = uri;
	}

	public HttpMethod getMethod() {
		return method;
	}

	public void setMethod(HttpMethod method) {
		this.method = method;
	}

	public Object getRequest() {
		return request;
	}

	public void setRequest(Object request) {
		this.request = request;
	}

	public Object getResponse() {
		return response;
	}

	public void setResponse(Object response) {
		this.response = response;
	}

	public Map<String, String> getRequestHeaders() {
		return requestHeaders;
	}

	public void setRequestHeaders(Map<String, String> header) {
		this.requestHeaders = header;
	}
	
	public Map<String, String> getResponseHeaders() {
		return responseHeaders;
	}

	public void setResponseHeaders(Map<String, String> responseHeaders) {
		this.responseHeaders = responseHeaders;
	}

	public int getHttpStatus() {
		return httpStatus;
	}

	public void setHttpStatus(int httpStatus) {
		this.httpStatus = httpStatus;
	}
	
	public StringBuilder getException() {
		return exception;
	}

	public void appendException(String exception) {
		if (this.exception == null) {
			this.exception = new StringBuilder(exception);
		} else {
			this.exception.append('*').append(exception);
		}
	}

	public String getCaller() {
		return caller;
	}

	public void setCaller(String caller) {
		this.caller = caller;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public Long getEid() {
		return eid;
	}

	public void setEid(Long eid) {
		this.eid = eid;
	}

	public String getTraceId() {
		return traceId;
	}

	public void setTraceId(String traceId) {
		this.traceId = traceId;
	}
	
}
