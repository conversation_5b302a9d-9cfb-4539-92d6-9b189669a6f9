package com.paytm.aggregatorgateway.vo;

/**
 * <AUTHOR>
 *
 * 
 */
public class PromoRequestVO {
	
	String status;
	Integer page_number;
	Integer page_size;
	Integer page_offset;
	String after_id;
	String merchant_id;
	String game_id;
	String oldest_txn_time;
	String stage;
	String campaign_id;
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Integer getPage_number() {
		return page_number;
	}
	public void setPage_number(Integer page_number) {
		this.page_number = page_number;
	}
	public Integer getPage_size() {
		return page_size;
	}
	public void setPage_size(Integer page_size) {
		this.page_size = page_size;
	}
	public Integer getPage_offset() {
		return page_offset;
	}
	public void setPage_offset(Integer page_offset) {
		this.page_offset = page_offset;
	}
	public String getAfter_id() {
		return after_id;
	}
	public void setAfter_id(String after_id) {
		this.after_id = after_id;
	}
	public String getMerchant_id() {
		return merchant_id;
	}
	public void setMerchant_id(String merchant_id) {
		this.merchant_id = merchant_id;
	}
	public String getGame_id() {
		return game_id;
	}
	public void setGame_id(String game_id) {
		this.game_id = game_id;
	}
	public String getOldest_txn_time() {
		return oldest_txn_time;
	}
	public void setOldest_txn_time(String oldest_txn_time) {
		this.oldest_txn_time = oldest_txn_time;
	}
	public String getStage() {
		return stage;
	}
	public void setStage(String stage) {
		this.stage = stage;
	}
	public String getCampaign_id() {
		return campaign_id;
	}
	public void setCampaign_id(String campaign_id) {
		this.campaign_id = campaign_id;
	}
	

}
