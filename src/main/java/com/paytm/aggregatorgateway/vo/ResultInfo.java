package com.paytm.aggregatorgateway.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonInclude(Include.NON_NULL)
public class ResultInfo {

	private String resultStatus;
	private String resultCode;
	private String resultCodeId;
	private String resultMsg;

	public String getResultStatus() {
		return resultStatus;
	}

	public void setResultStatus(String resultStatus) {
		this.resultStatus = resultStatus;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getResultCodeId() {
		return resultCodeId;
	}

	public void setResultCodeId(String resultCodeId) {
		this.resultCodeId = resultCodeId;
	}

	public String getResultMsg() {
		return resultMsg;
	}

	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}

	@Override
	public String toString() {
		return "ResultInfo [resultStatus=" + resultStatus + ", resultCode=" + resultCode + ", resultCodeId="
				+ resultCodeId + ", resultMsg=" + resultMsg + "]";
	}

}
