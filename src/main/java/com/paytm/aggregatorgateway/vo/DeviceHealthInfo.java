package com.paytm.aggregatorgateway.vo;

import com.paytm.aggregatorgateway.enums.DeviceErrorType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@ToString
@Getter
@Setter
public class DeviceHealthInfo {

    private String errorType;
    private String deviceName;
    private String deviceNameDefaultValue;
    private List<String> deviceNameBodyParam;
    private String subtitle;
    private String subtitleDefaultValue;
    private List<String> subtitleBodyParam;
    private String deviceIcon;
    private String cardBackgroundColor;
    private String cardBorderColor;
    private String deviceCTATitle;
    private String deviceCTATitleDefaultValue;
    private List<String> deviceCTATitleBodyParam;
    private String deviceCTADeeplink;
    private String deviceErrorIcon;
    private String deviceErrorDesc1;
    private String deviceErrorDesc1DefaultValue;
    private List<String> deviceErrorDesc1BodyParam;
    private String deviceErrorDesc2;
    private String deviceErrorDesc2DefaultValue;
    private List<String> deviceErrorDesc2BodyParam;
    private String deviceErrorSubDesc1;
    private String deviceErrorSubDesc1DefaultValue;
    private List<String> deviceErrorSubDesc1BodyParam;
    private String deviceErrorSubDesc2;
    private String deviceErrorSubDesc2DefaultValue;
    private List<String> deviceErrorSubDesc2BodyParam;

    public DeviceHealthInfo(DeviceErrorType errorEnum) {
        this.errorType = errorEnum.name();
        this.deviceName = errorEnum.getDeviceName();
        this.deviceNameDefaultValue = errorEnum.getDeviceNameDefaultValue();
        this.deviceNameBodyParam = new ArrayList<>();
        this.subtitle = errorEnum.getSubtitle();
        this.subtitleDefaultValue = errorEnum.getSubtitleDefaultValue();
        this.subtitleBodyParam = new ArrayList<>();
        this.deviceIcon = errorEnum.getDeviceIcon();
        this.cardBackgroundColor = errorEnum.getCardBackgroundColor();
        this.cardBorderColor = errorEnum.getCardBorderColor();
        this.deviceCTATitle = errorEnum.getDeviceCTATitle();
        this.deviceCTATitleDefaultValue = errorEnum.getDeviceCTATitleDefaultValue();
        this.deviceCTATitleBodyParam = new ArrayList<>();
        this.deviceCTADeeplink = errorEnum.getDeviceCTADeeplink();
        this.deviceErrorIcon = errorEnum.getDeviceErrorIcon();
        this.deviceErrorDesc1 = errorEnum.getDeviceErrorDesc1();
        this.deviceErrorDesc1DefaultValue = errorEnum.getDeviceErrorDesc1DefaultValue();
        this.deviceErrorDesc1BodyParam = new ArrayList<>();
        this.deviceErrorDesc2 = errorEnum.getDeviceErrorDesc2();
        this.deviceErrorDesc2DefaultValue = errorEnum.getDeviceErrorDesc2DefaultValue();
        this.deviceErrorDesc2BodyParam = new ArrayList<>();
        this.deviceErrorSubDesc1 = errorEnum.getDeviceErrorSubDesc1();
        this.deviceErrorSubDesc1DefaultValue = errorEnum.getDeviceErrorSubDesc1DefaultValue();
        this.deviceErrorSubDesc1BodyParam = new ArrayList<>();
        this.deviceErrorSubDesc2 = errorEnum.getDeviceErrorSubDesc2();
        this.deviceErrorSubDesc2DefaultValue = errorEnum.getDeviceErrorSubDesc2DefaultValue();
        this.deviceErrorSubDesc2BodyParam = new ArrayList<>();
    }
}
