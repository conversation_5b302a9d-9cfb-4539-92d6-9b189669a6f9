package com.paytm.aggregatorgateway.app;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.context.annotation.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.scheduling.annotation.EnableAsync;
import javax.cache.Caching;
import javax.cache.spi.CachingProvider;

import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;

import java.net.InetAddress;
import java.net.UnknownHostException;

import org.ehcache.jsr107.EhcacheCachingProvider;
import javax.cache.CacheManager;
import java.io.IOException;

//@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class})
@ComponentScan
@Configuration
//@EnableRedisHttpSession(redisNamespace="bffv2")
//@EnableRedisIndexedHttpSession(redisNamespace="bffv2") //ToDo: to check if this is required or not
@EnableAsync
@EnableCaching
@ComponentScan("com.paytm.*")
@EnableJpaRepositories(basePackages = {"com.paytm.*"})
@EntityScan("com.paytm.*")
@PropertySource(ignoreResourceNotFound = true,
				value = {
						"classpath:project-${spring.profiles.active}.properties",
						"classpath:secrets-${spring.profiles.active}.properties",
//						"./project-${spring.profiles.active}.properties",
						"file:./project-${spring.profiles.active}.properties",
//						"./conf/secrets-${spring.profiles.active}.properties",
						"file:./conf/secrets-${spring.profiles.active}.properties"

						})
//@ImportResource("classpath:applicationContext.xml")
public class App extends SpringBootServletInitializer {

	@Autowired
	LettuceConnectionFactory lettuceConnectionFactory;

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(App.class);
	}

	@Override
	public void onStartup(ServletContext servletContext) throws ServletException {
//		try {
//			VaultSetup.loadApproleProps();
//		} catch (IOException e) {
//			logger.error(e);
//			throw new ServletException(e.getMessage());
//		}
//		WebApplicationContext rootAppContext = createRootApplicationContext(servletContext);
//		if (rootAppContext != null) {
//			servletContext.addListener(new InitiateMisContextListener());
//		}
	}

	public static void main(String[] args) throws UnknownHostException {

		System.setProperty("hostName", InetAddress.getLocalHost().getHostName());
		SpringApplication.run(App.class, args);
	}

	@Bean
	@Primary
	public JCacheCacheManager ehCacheCacheManager() {
		try {
			CachingProvider provider = Caching.getCachingProvider(EhcacheCachingProvider.class.getName());
			ClassPathResource configResource = new ClassPathResource("ehcache.xml");
			CacheManager cacheManager = provider.getCacheManager(
				configResource.getURI(),
				getClass().getClassLoader()
			);
			return new JCacheCacheManager(cacheManager);
		} catch (IOException e) {
			throw new RuntimeException("Failed to load ehcache.xml", e);
		}
	}

	//To disable subscription to redis keyspace events
	@Bean
	public RedisMessageListenerContainer redisMessageListenerContainer() {
		RedisMessageListenerContainer container = new RedisMessageListenerContainer();
		container.setConnectionFactory(lettuceConnectionFactory);
		return container;
	}

	//To disable automatic configuration of redis keyspace events
//	@Bean
//	public static ConfigureRedisAction configureRedisAction() {
//		return ConfigureRedisAction.NO_OP;
//	}

}
