package com.paytm.aggregatorgateway.app;

import com.google.common.util.concurrent.RateLimiter;
import com.paytm.aggregatorgateway.constants.WarmUpConstants;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
public class WarmUpRequests {
	
	private static final Logger LOGGER = LogManager.getLogger(WarmUpRequests.class);

	@Value("${warmup.iterations:0}")
	private int iterations;
	
	@Autowired
	private RestProcessorDelegate delegate;

	private HttpHeaders headers;

	private static final ExecutorService threadPool = Executors.newFixedThreadPool(300);

	private static final RateLimiter rateLimiter = RateLimiter.create(150.0, Duration.ofSeconds(1));

	public static final List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
	
	@EventListener({ApplicationReadyEvent.class})
	public void triggerRequests() {
		LOGGER.info("Starting WarmUp. GET APIs: {}, POST APIs: {}, Iterations: {}",
				WarmUpConstants.WARMUP_APIS_GET.size(), WarmUpConstants.WARMUP_APIS_POST.size(), iterations);

		headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(WarmUpConstants.WARMUP_HEADER, "true");
		headers.add("x-auth-ump", "dummy");

		double startTime = System.currentTimeMillis();
		for(int i=0;i<iterations;i++) {
			WarmUpConstants.WARMUP_APIS_GET.parallelStream().forEach(e -> completableFutureList.add(submitRequest(e, "GET")));
			WarmUpConstants.WARMUP_APIS_POST.parallelStream().forEach(e -> completableFutureList.add(submitRequest(e, "POST")));
		}

		double endTime = System.currentTimeMillis();
		LOGGER.info("WarmUp Completed. Time Taken: {} sec", (endTime - startTime)/1000);
	}
	
	private CompletableFuture<Void> submitRequest(String api, String method) {
		return CompletableFuture.runAsync(() -> executeRequest(api, method), threadPool);
	}

	private void executeRequest(String api, String method) {
		String url = WarmUpConstants.LOCALHOST + api;
		Object payload = method.equalsIgnoreCase("POST") ? WarmUpConstants.WARMUP_REQUEST_BODY : null;
		rateLimiter.acquire();
		try {
			delegate.executeOthersRequestHystrix(url, method, null, headers, payload, String.class);
		} catch (Exception e) {
			LOGGER.error("Error occur in executeRequest",e);
		}
	}
}
