package com.paytm.aggregatorgateway.exceptions;


import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class ValidationException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	private Map<String, String> errors = new HashMap<String, String>(3);

	public ValidationException() {
		super();
	}

	public ValidationException(String code, String message) {
		super();
		errors.put(code, message);
	}

	public ValidationException(UMPErrorCodeEnums umpError) {
		errors.put(umpError.getErrorCode(), umpError.getErrorMsg());
	}

	public ValidationException(UMPErrorCodeEnums umpError, String message) {
		errors.put(umpError.getErrorCode(), umpError.getErrorMsg() + message);
	}

	public void addError(String code, String message) {
		errors.put(code, message);
	}

	public ValidationException addErrorFluent(String code, String message) {
		errors.put(code, message);
		return this;
	}
	public Map<String, String> getErrors() {
		return errors;
	}

	public boolean hasErrors() {
		return MapUtils.isNotEmpty(errors);
	}
}
