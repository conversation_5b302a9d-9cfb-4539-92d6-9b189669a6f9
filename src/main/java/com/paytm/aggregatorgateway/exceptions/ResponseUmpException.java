package com.paytm.aggregatorgateway.exceptions;


import com.paytm.aggregatorgateway.enums.Status;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.vo.ResponseUmp;

/**
 *
 * <AUTHOR>
 *
 */

public class ResponseUmpException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private final ResponseUmp response;


	public ResponseUmpException(ResponseUmp response) {
		this.response = response;
	}

	public ResponseUmpException(UMPErrorCodeEnums response, String message) {
		this(new ResponseUmp(Status.FAILURE.name(), response.getErrorCode(), response.getErrorMsg() + message, null));
	}

	public ResponseUmpException(UMPErrorCodeEnums response) {
		this(new ResponseUmp(Status.FAILURE.name(), response.getErrorCode(), response.getErrorMsg(), null));
	}

	public ResponseUmpException(String status, String statusCode, String statusMsg, Object results) {
		this(new ResponseUmp(status, statusCode, statusMsg, results));
	}

	public ResponseUmp getResponse() {
		return response;
	}

	public String getMessage() {
		return response.toString();
	}

}

