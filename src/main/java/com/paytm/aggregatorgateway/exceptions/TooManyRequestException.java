package com.paytm.aggregatorgateway.exceptions;

import org.apache.commons.lang3.StringUtils;

public class TooManyRequestException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private String errors = new String();

    public TooManyRequestException() {
        super();
    }

    public TooManyRequestException(String message) {
        super();
        errors = message;
    }

    public String getErrors() {
        return errors;
    }

    public boolean hasErrors() {
        return StringUtils.isNotBlank(errors);
    }
}
