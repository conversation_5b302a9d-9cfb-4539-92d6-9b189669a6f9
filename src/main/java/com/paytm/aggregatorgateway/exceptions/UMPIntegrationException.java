package com.paytm.aggregatorgateway.exceptions;

import com.paytm.aggregatorgateway.enums.IntegrationErrorCodes;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UMPIntegrationException extends Exception {

    private static final long serialVersionUID = 1L;

    private IntegrationErrorCodes errorCode;

    public UMPIntegrationException(final String message) {
        super(message);
    }

    public UMPIntegrationException(Throwable cause) {
        super(cause);
    }

    public UMPIntegrationException(String message, Throwable cause) {
        super(message, cause);
    }

    public UMPIntegrationException(final String message, final IntegrationErrorCodes errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public UMPIntegrationException(final String message, final IntegrationErrorCodes errorCode, Throwable throwable) {
        super(message, throwable);
        this.errorCode = errorCode;
    }


}
