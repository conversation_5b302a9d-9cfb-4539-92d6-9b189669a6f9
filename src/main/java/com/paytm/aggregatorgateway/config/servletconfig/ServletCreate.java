package com.paytm.aggregatorgateway.config.servletconfig;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.support.AnnotationConfigWebApplicationContext;
import org.springframework.web.servlet.DispatcherServlet;

@Configuration
public class ServletCreate {
    Logger servletCreateLogger = LoggerFactory.getLogger(ServletCreate.class);

    @Value("${url.mapping}")
    private String urlMapping;

    @Bean
    public ServletRegistrationBean rootServlet() {
        servletCreateLogger.info("url mapping : {}", urlMapping);
        DispatcherServlet dispatcherServlet = new DispatcherServlet();
        AnnotationConfigWebApplicationContext applicationContext = new AnnotationConfigWebApplicationContext();
        applicationContext.register(RootConfig.class);
        dispatcherServlet.setApplicationContext(applicationContext);
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(dispatcherServlet, urlMapping);
        servletRegistrationBean.setName("root");
        servletRegistrationBean.setLoadOnStartup(1);
        return servletRegistrationBean;
    }
}
