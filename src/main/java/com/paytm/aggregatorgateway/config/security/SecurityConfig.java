package com.paytm.aggregatorgateway.config.security;

import com.paytm.aggregatorgateway.config.security.filters.CORSFilter;
import com.paytm.aggregatorgateway.config.security.filters.CsrfRequestMatcher;
import com.paytm.aggregatorgateway.service.security.IUserFacade;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.security.web.csrf.CsrfLogoutHandler;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository;
import org.springframework.security.web.header.writers.StaticHeadersWriter;
import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

@EnableMethodSecurity(securedEnabled = true, prePostEnabled = true)
@EnableWebSecurity
@Configuration
public class SecurityConfig {

    private static final Logger LOGGER = LogManager.getLogger(SecurityConfig.class);

    @Value("${spring.profiles.active:local}")
    private String platformName;

    @Value("${csrf.enable:true}")
    private boolean enableCsrf;

    @Value("${origin.filter:false}")
    private boolean enableOriginFilter;

    @Value("${allow.post.request}")
    private String allowPostRequest;

    @Value("${allow.iframe.origins:}")
    private String allowIframeOrigins;

    @Value("${allow.cors.origins:}")
    private String allowCorsOrigins;

    @Value("${allow.csrf.origins:}")
    private String allowCsrfOrigins;

    @Value("${internal.user.permission}")
    private String internalUserPermission;

    @Value("${allow.pseudo.merchant.post.request}")
    private String allowPseudoMerchantPostRequest;

    @Value("${pseudo.merchant.mid:MQQVCS05838368647127}")
    private String pseudoMerchantMid;

    @Autowired
    private IUserFacade userFacade;

    @Autowired
    private Environment environment;

    @Value("${enable.session.creation:false}")
    private boolean enableSessionCreation;

    // Logout Handlers for spring security and CSRF
    @Bean
    public LogoutHandler csrfLogoutHandler() {
        return new CsrfLogoutHandler(csrfTokenRepository());
    }

    @Bean
    public LogoutHandler securityContextLogoutHandler() {
        return new SecurityContextLogoutHandler();
    }

    @Bean
    public SecurityFilterChain configure(HttpSecurity http) throws Exception {
        http.formLogin(AbstractHttpConfigurer::disable);
        http.logout(AbstractHttpConfigurer::disable);
        http.httpBasic(AbstractHttpConfigurer::disable);
        http.headers(headers -> headers.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable));
        http.sessionManagement(sessionManagement -> sessionManagement.sessionCreationPolicy(SessionCreationPolicy.NEVER));

        boolean enableOriginFilter = !(StringUtils.equals("dev", platformName) || StringUtils.equals("local", platformName));
        if (enableOriginFilter) {
            http.headers(headers ->headers
                    .addHeaderWriter(new StaticHeadersWriter("Content-Security-Policy",
                            "default-src 'self' https://*.paytm.com https://*.paytm.in;" + "script-src 'self' 'unsafe-inline' https://connect.facebook.net/;" + "img-src 'self' data: https://*.paytm.com https://www.facebook.com/;"
                                    + "style-src 'self' 'unsafe-inline' https://*.paytm.com;" + "report-uri https://csp-report.mypaytm.com/reportcspviolations.php;"
                                    + "frame-ancestors 'self' " + allowIframeOrigins)));

        }
        http.authorizeHttpRequests(auth -> auth.requestMatchers(new AntPathRequestMatcher("/healthcheck"), new AntPathRequestMatcher("/status/live"),
                new AntPathRequestMatcher("/status/ready"), new AntPathRequestMatcher("/actuator/refresh")).permitAll());
        //ToDo: Need to check if above code is required for all the previous endpoints
        http.authorizeHttpRequests(auth -> auth.anyRequest().authenticated());
        http.addFilterBefore(new InternalAPIFilter(userFacade, environment), SecurityContextHolderAwareRequestFilter.class);
        http.addFilterBefore(new CORSFilter(allowCorsOrigins, allowIframeOrigins, enableOriginFilter), ChannelProcessingFilter.class);
		http.addFilterAfter(new BlockedMerchantFilter(allowPostRequest, internalUserPermission, allowPseudoMerchantPostRequest,pseudoMerchantMid), InternalAPIFilter.class);
        http.csrf(AbstractHttpConfigurer::disable);

        return http.build();
    }

    /*@Override
    public void configure(WebSecurity web) throws Exception {
        web.ignoring().antMatchers("/static/**", "/favicon.ico", "/public/**","/webjars/**");

    }*/

    @Bean
    public CsrfTokenRepository csrfTokenRepository() {
        HttpSessionCsrfTokenRepository repository = new HttpSessionCsrfTokenRepository();
        repository.setHeaderName("X-XSRF-TOKEN");
        return repository;
    }

    @Bean
    public CsrfRequestMatcher csrfRequestMatcher() {
        return new CsrfRequestMatcher(allowCsrfOrigins);
    }

}
