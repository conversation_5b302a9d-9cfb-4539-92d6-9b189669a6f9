package com.paytm.aggregatorgateway.config.security.filters;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 
 * <AUTHOR>
 *
 */
public class CORSFilter extends OncePerRequestFilter {

	private static final String ORIGIN = "Origin";
	private static final Pattern URL_PATTERN = Pattern.compile("(https?://[^\\/]*)(\\/.*)?");
	
	private Set<String> allowCorsOrigins;
	private Set<String> allowIframeOrigins;
	private boolean enableOriginFilter;
	private static final Logger LOGGER = LogManager.getLogger(CORSFilter.class);

	public CORSFilter(String allowCorsOrigins, String allowIframeOrigins, boolean enableOriginFilter) {
		String[] allowCorsOriginsArray = StringUtils.splitByWholeSeparator(allowCorsOrigins, null);
		this.allowCorsOrigins = new HashSet<>(Arrays.asList(allowCorsOriginsArray));

		String[] allowIframeOriginsArray = StringUtils.splitByWholeSeparator(allowIframeOrigins, null);
		this.allowIframeOrigins = new HashSet<>(Arrays.asList(allowIframeOriginsArray));

		this.enableOriginFilter = enableOriginFilter;
	}

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		
		/**
		 * IFRAME FILTER
		 */
		if (enableOriginFilter) {
			String referer = request.getHeader("referer");
			String origin = request.getHeader("origin");
			if (!allowIframeOrigins.contains(getUrlDomain(referer)) && !allowIframeOrigins.contains(getUrlDomain(origin))) {
				response.addHeader("X-Frame-Options", "sameorigin");
			}
		}

		/**
		 * CORS FILTER
		 */
		String requestOrigin = request.getHeader(ORIGIN);
		if (!enableOriginFilter || allowCorsOrigins.contains(requestOrigin)) {
			response.addHeader("Access-Control-Allow-Origin", requestOrigin);
		}
		response.addHeader("Access-Control-Allow-Credentials", "true");
		response.addHeader("Access-Control-Max-Age", "10");
		response.addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, OPTIONS, DELETE");


		String reqHead = request.getHeader("Access-Control-Request-Headers");
		if (StringUtils.isNotEmpty(reqHead)) {
			response.addHeader("Access-Control-Allow-Headers", reqHead);
		}

		if (request.getMethod().equals("OPTIONS")) {
			try {
				response.getWriter().print("OK");
				response.getWriter().flush();
			} catch (IOException e) {
				LOGGER.error("Exception e : {}", e.getMessage());
			}
		} else {
			filterChain.doFilter(request, response);
		}

	}

	private static String getUrlDomain(String url) {
		if (url != null) {
			Matcher matcher = URL_PATTERN.matcher(url);
			if (matcher.find()) {
				return matcher.group(1);
			}
		}
		return null;
	}
}
