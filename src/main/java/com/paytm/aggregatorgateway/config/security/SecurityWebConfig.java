/*
package com.paytm.aggregatorgateway.config.security;

import com.paytm.aggregatorgateway.config.security.filters.CORSFilter;
import com.paytm.aggregatorgateway.config.security.filters.CsrfHeaderFilter;
import com.paytm.aggregatorgateway.config.security.filters.CsrfRequestMatcher;
import com.paytm.aggregatorgateway.service.security.IUserFacade;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.csrf.CsrfLogoutHandler;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository;
import org.springframework.security.web.header.writers.StaticHeadersWriter;
import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter;

@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
@EnableWebSecurity
@Order(2)
public class SecurityWebConfig extends WebSecurityConfigurerAdapter {

	private static final Logger LOGGER = LogManager.getLogger(SecurityWebConfig.class);

	@Value("${spring.profiles.active:local}")
	private String platformName;

	@Value("${csrf.enable:true}")
	private boolean enableCsrf;
	
	@Value("${origin.filter:false}")
	private boolean enableOriginFilter;

	@Value("${allow.post.request}")
	private String allowPostRequest;

	@Value("${allow.iframe.origins:}")
	private String allowIframeOrigins;

	@Value("${allow.cors.origins:}")
	private String allowCorsOrigins;

	@Value("${allow.csrf.origins:}")
	private String allowCsrfOrigins;

    @Value("${internal.user.permission}")
    private String internalUserPermission;
	
    @Value("${allow.pseudo.merchant.post.request}")
    private String allowPseudoMerchantPostRequest;
	
   @Value("${pseudo.merchant.mid:MQQVCS05838368647127}")
   private String pseudoMerchantMid;

	@Autowired
	private IUserFacade userFacade;

	@Autowired
	private Environment environment;

	@Value("${enable.session.creation:false}")
	private boolean enableSessionCreation;
	

	// Logout Handlers for spring security and CSRF
	@Bean
	public LogoutHandler csrfLogoutHandler() {
		return new CsrfLogoutHandler(csrfTokenRepository());
	}

	@Bean
	public LogoutHandler securityContextLogoutHandler() {
		return new SecurityContextLogoutHandler();
	}

	@Override
	protected void configure(HttpSecurity http) throws Exception {
		http.formLogin().disable().logout().disable().httpBasic().disable();
		http.headers().frameOptions().disable();
		boolean enableOriginFilter = !(StringUtils.equals("dev", platformName) || StringUtils.equals("local", platformName));
		if (enableOriginFilter) {
			http.headers()
					.addHeaderWriter(new StaticHeadersWriter("Content-Security-Policy",
							"default-src 'self' https://*.paytm.com https://*.paytm.in;" + "script-src 'self' 'unsafe-inline' https://connect.facebook.net/;" + "img-src 'self' data: https://*.paytm.com https://www.facebook.com/;"
									+ "style-src 'self' 'unsafe-inline' https://*.paytm.com;" + "report-uri https://csp-report.mypaytm.com/reportcspviolations.php;"
									+ "frame-ancestors 'self' " + allowIframeOrigins));

		}
		http.authorizeRequests().antMatchers("/logout","/logout2", "/auth", "/login", "/", "/redirect", "/home-app", "/page/*", "/2", "/ok", "/developer-login", "/developer-auth","/devpayment-status","/testpay","/api/v1/feedback/contact","/api/v1/business/contact","/app","/healthcheck","/api/v1/permission/validate", "/api/v1/download/file", "/api/v1/merchant/integration/validation", "/api/v1/merchant/integration/onboard", "/status/live", "/status/ready").permitAll();
		http.authorizeRequests().anyRequest().authenticated();
		http.addFilterBefore(new InternalAPIFilter(userFacade, environment), SecurityContextHolderAwareRequestFilter.class);
		http.addFilterBefore(new CORSFilter(allowCorsOrigins, allowIframeOrigins, enableOriginFilter), ChannelProcessingFilter.class);
		//TODO: @Ankit :confirm if the below filter is needed in onboarding service 
        http.addFilterAfter(new BlockedMerchantFilter(allowPostRequest, internalUserPermission, allowPseudoMerchantPostRequest,pseudoMerchantMid), InternalAPIFilter.class);
		http.csrf().csrfTokenRepository(csrfTokenRepository()).requireCsrfProtectionMatcher(csrfRequestMatcher());
		http.addFilterAfter(new CsrfHeaderFilter(), CsrfFilter.class);
	}

	@Override
	public void configure(WebSecurity web) throws Exception {
		web.ignoring().antMatchers("/static/**", "/favicon.ico", "/public/**","/webjars/**");

	}

	@Bean
	public CsrfTokenRepository csrfTokenRepository() {
		HttpSessionCsrfTokenRepository repository = new HttpSessionCsrfTokenRepository();
		repository.setHeaderName("X-XSRF-TOKEN");
		return repository;
	}

	@Bean
	public CsrfRequestMatcher csrfRequestMatcher() {
		return new CsrfRequestMatcher(allowCsrfOrigins);
	}

}
*/
