package com.paytm.aggregatorgateway.config.security;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.WarmUpConstants;
import com.paytm.aggregatorgateway.controller.healthcheck.HealthCheckController;
import com.paytm.aggregatorgateway.dao.JwtDao;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.UnauthorizedException;
import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.pgdashboard.commons.dto.Role;
import com.paytm.pgdashboard.commons.dto.User;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.security.IUserFacade;
import com.paytm.aggregatorgateway.utils.RequestUtil;
import com.paytm.dashboard.security.UserAuthentication;
import com.paytm.dashboard.security.UserDetailConfig;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;

import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.ANDROID_CLIENT;
import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.IOS_CLIENT;

public class InternalAPIFilter extends OncePerRequestFilter {


	private static final String iosInvalidTokenVersionThreshold = "9.2.0";

	private List<String> whilistingURls;
	private IUserFacade userFacade;
	private ObjectMapper mapper = new ObjectMapper();
	private static final Logger LOGGER = LogManager.getLogger(InternalAPIFilter.class);
	private static final String DEFAULT_LOCALE="en-IN";
	private static final String FORCE_UPDATE_MERCHANTS = "/bffv2/api/v1/app/forceupdate";
	private static final String IOS_SOURCE = "P4B_IOS";
	private static final int SESSION_EXPIRED_HTTP_CODE = 440;

	@Autowired
	private JwtDao jwtDao;

	public InternalAPIFilter(IUserFacade userFacade, Environment environment) {
		this.userFacade = userFacade;
		this.whilistingURls = environment.getProperty("api.skip.merchant.association.urls", List.class,
				Arrays.asList("/api/v2/subusers/pending", "/api/v2/subusers/consent", "/api/v1/onboard/activate/banks"));
	}

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
			String midInRequest = request.getHeader("x-user-mid");
			String warmUpRequest = request.getHeader("x-warm-up");
			String jwtToken = request.getHeader("x-jwt-token");
			String client = request.getHeader("client");
			LOGGER.info("mid in header : {}", midInRequest);
			String apiEndPoint = request.getRequestURI();
			String whoIs = request.getHeader("x-auth-ump");

			LOGGER.debug("Operating via API KEY {}", whoIs);
			String userToken = request.getHeader("x-user-token");
			String cookie = request.getHeader("Cookie");
			boolean isSuperUser = false;

		//String clientId = request.getHeader("client-id");
		/*if ("IOT_client_production".equalsIgnoreCase(clientId)) {
			LOGGER.info("=== Printing all request headers for client-id: IOT_client_production ===");

			Enumeration<String> headerNames = request.getHeaderNames();
			while (headerNames.hasMoreElements()) {
				String headerName = headerNames.nextElement();
				Enumeration<String> headerValues = request.getHeaders(headerName); // in case of multi-value headers
				while (headerValues.hasMoreElements()) {
					String value = headerValues.nextElement();
					LOGGER.info(headerName + ": " + value);
				}
			}

			LOGGER.info("=== End of Headers ===");
		}*/

		if(HealthCheckController.warmUpCompleted && (StringUtils.isNotBlank(jwtToken)) ){
			LOGGER.info("jwt token accepted "+request.getHeader("x-jwt-token"));
			try {
				if(apiEndPoint.contains("api/v1/upi/mandate/callback")){
					userFacade.validateJWTForCheckout(request.getHeader("x-jwt-token").toString());
				}
				else if(apiEndPoint.contains("/bankProof/qualification/callBack") || apiEndPoint.contains("/bankProof/deductions/callBack")){
					Map<String,String> clientInfoMap;
					clientInfoMap = jwtDao.fetchClientSecret(request.getHeader("client-id").toString());
					String permissionsString = clientInfoMap.get("permissions");
					List<String> permissions = null;
					if(StringUtils.isNotBlank(permissionsString)){
						permissions = Arrays.asList(permissionsString.split(","));
					}
					addPermission(permissions);
				}
				else {
					userFacade.validateJWT(request.getHeader("x-jwt-token").toString(), request.getHeader("client-id").toString());
				}
			} catch (Exception e) {
				throw new ValidationException(UMPErrorCodeEnums.INVALID_JWT_TOKEN);
			}
		}
		else if (HealthCheckController.warmUpCompleted && (StringUtils.isNotBlank(whoIs) || StringUtils.isNotBlank(cookie))) {
				LOGGER.debug("User token to get context {}", userToken);
				User user = null;
				String versionStr = "";
				String source = "";

				try {

					Map<String, Object> queryParams = request.getParameterMap().entrySet().stream()
							.collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue()[0]));
					if(queryParams.get("version") != null)
						versionStr = queryParams.get("version").toString();
					if(queryParams.get("source") != null)
						source = queryParams.get("source").toString();
					if (StringUtils.isNotBlank(whoIs)) {
						userFacade.checkXAuthUMPValidity(whoIs, request);
					}
					user = Objects.isNull(userToken) ? (User) BeanUtils.cloneBean(userFacade.getUserByXAuthUMPOrCookie(request))
							: (User) BeanUtils.cloneBean(userFacade.getUser(request, userToken));
					String appClient = request.getHeader("client");
					LOGGER.info("app client is {}", appClient);
					String locale = request.getHeader(HttpHeaders.ACCEPT_LANGUAGE);
					user.setCurrentLocale(StringUtils.isBlank(locale) ? DEFAULT_LOCALE : locale);

				}
                catch (UnauthorizedException e){
                    LOGGER.error("Unauthorized Exception using given token");
                    LOGGER.error("uri:{}, IP : {}, mid: {}, error Msg :{}",request.getRequestURI(), RequestUtil.getXRealIP(request), midInRequest, e.getMessage());
                    response.setContentType("application/json");
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    return;
                } catch(ValidationException ex) {
					LOGGER.error("Validation Exception using with given token, ", ex);
					LOGGER.error("uri:{}, IP : {}, mid: {}, error Msg :{}, error codes: {} ",request.getRequestURI(), RequestUtil.getXRealIP(request), midInRequest, ex.getMessage(),ex.getErrors());
					response.setContentType("application/json");


					if(FORCE_UPDATE_MERCHANTS.equalsIgnoreCase(apiEndPoint)){
						LOGGER.info("force update merchants endpoint");
						if(IOS_SOURCE.equalsIgnoreCase(source) && compareVersions(versionStr, iosInvalidTokenVersionThreshold) > 0){
							LOGGER.info("setting session expired status code");
							response.setStatus(SESSION_EXPIRED_HTTP_CODE);
							response.getWriter().write(mapper.writeValueAsString(ex.getErrors()));
							return;
						}

					}

					response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
					response.getWriter().write(mapper.writeValueAsString(ex.getErrors()));
					return;
				} catch (AccessDeniedException ex) {
					LOGGER.error("AccessDeniedException using with given token, ", ex);
					LOGGER.error("uri:{}, IP : {}, mid: {}, error Msg :{}, error codes: {} ",request.getRequestURI(), RequestUtil.getXRealIP(request), midInRequest, ex.getMessage(),ex.getMessage());
					response.setContentType("application/json");
					response.setStatus(HttpServletResponse.SC_FORBIDDEN);
					response.getWriter().write(mapper.writeValueAsString(ex.getMessage()));
					return;
				} catch (Exception e) {
					LOGGER.error("Error fetching using with given token,",e);
					LOGGER.error("uri:{}, IP : {}, mid: {}, error Msg :{}",request.getRequestURI(),RequestUtil.getXRealIP(request), midInRequest, e.getMessage());
					response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
					return;
				}
//				request.setAttribute(ClientNameConstants.CLIENT_NAME, client.getClientId());
				try {
					updateCurrentMerchant(user, midInRequest,client);
				}
                catch(UnauthorizedException e)
                {
					LOGGER.error("Unauthorized exception while updating context {}", e.getMessage());
					response.setContentType("application/json");
					response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
					return;

                }
                catch (ValidationException ex) {
					if(FORCE_UPDATE_MERCHANTS.equalsIgnoreCase(apiEndPoint) &&
							IOS_SOURCE.equalsIgnoreCase(source)
							&& StringUtils.isBlank(midInRequest) ){
						LOGGER.info("skipping validation exception for empty mid in forceUpdate");
					}
					else {
						LOGGER.error("Error updating merchant context with given mid {}", ex);
						response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
						response.getWriter().write(mapper.writeValueAsString(ex.getErrors()));
						return;
					}
				}
                catch(Exception e){
                    LOGGER.error("Error updating merchant context with given mid {}", e.getMessage());
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
				isSuperUser = userFacade.isSuperUser(user);
				UserDetails details = new UserDetailConfig(user, isSuperUser);
				Authentication auth = new UserAuthentication(details);
				SecurityContextImpl secureContext = new SecurityContextImpl();
				secureContext.setAuthentication(auth);
				SecurityContextHolder.setContext(secureContext);


			}
			else if(!HealthCheckController.warmUpCompleted && StringUtils.isNotBlank(warmUpRequest) && warmUpRequest.equalsIgnoreCase("true")) {
				mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				User dummyUser = mapper.readValue(WarmUpConstants.DUMMY_CONTEXT, User.class);
				UserDetails details = new UserDetailConfig(dummyUser, isSuperUser);
				Authentication auth = new UserAuthentication(details);
				SecurityContextImpl secureContext = new SecurityContextImpl();
				secureContext.setAuthentication(auth);
				SecurityContextHolder.setContext(secureContext);
			}

			filterChain.doFilter(request, response);
	}

	private void addPermission(List<String> permissions) {
		User user = new User();
		if(Objects.nonNull(permissions)) {
			Role jwtRole = new Role();
			jwtRole.setPermissions(permissions);
			List<Role> roleList = Arrays.asList(jwtRole);
			Merchant merchant = new Merchant();
			merchant.setRoles(roleList);
			merchant.setId(0l);
			List<Merchant> merchantList = Arrays.asList(merchant);
			user.setMerchants(merchantList);
			user.setCurrentMerchant(0L);
		}
		UserDetails details = new UserDetailConfig(user,false);
		Authentication auth = new UserAuthentication(details);
		SecurityContextImpl secureContext = new SecurityContextImpl();
		secureContext.setAuthentication(auth);
		SecurityContextHolder.setContext(secureContext);
	}

	private void updateCurrentMerchant(User user, String midInRequest, String client) throws Exception{
		if(midInRequest != null && user != null && user.getMerchants() != null && !user.getMerchants().isEmpty()) {
			Merchant merchantExists = null;
			for (Merchant merchant : user.getMerchants()) {
				if (merchant.getMid().equalsIgnoreCase(midInRequest)) {
					merchantExists = merchant;
					break;
				}
			}
			if (merchantExists == null) {
                if(isPPSLMerchant(user.getPpslMerchants(), midInRequest))
                {   logger.info("ppsl migrated merchant");
                    if(ANDROID_CLIENT.equalsIgnoreCase(client)) {
                        throw new ValidationException("ERROR", "INVALID_TOKEN");
                    }
                    else if(IOS_CLIENT.equalsIgnoreCase(client)){
                        throw new UnauthorizedException();
                    }
                }
				throw new ValidationException("invalid_operation", "Merchant operation not allowed");
			}
			user.setCurrentMerchant(merchantExists.getId());
			user.setGuid(merchantExists.getGuid());
		}
	}
    private boolean isPPSLMerchant(List<Merchant> ppslMerchants, String xUserMid){
        if(CollectionUtils.isNotEmpty(ppslMerchants)) {
            for(Merchant merchant : ppslMerchants) {
                if(xUserMid.equalsIgnoreCase(merchant.getMid())){
                    return true;
                }
            }
        }
        return false;
    }
	private int compareVersions(String version1, String version2) {
		String[] version1Parts = version1.split("\\.");
		String[] version2Parts = version2.split("\\.");
		int length = Math.max(version1Parts.length, version2Parts.length);
		for(int i = 0; i < length; i++) {
			int version1Part = i < version1Parts.length ?
					Integer.parseInt(version1Parts[i]) : 0;
			int version2Part = i < version2Parts.length ?
					Integer.parseInt(version2Parts[i]) : 0;
			if(version1Part < version2Part)
				return -1;
			if(version1Part > version2Part)
				return 1;
		}
		return 0;
	}
}
