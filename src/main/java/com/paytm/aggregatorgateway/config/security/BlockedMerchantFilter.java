package com.paytm.aggregatorgateway.config.security;

import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.pgdashboard.commons.dto.Merchant;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class BlockedMerchantFilter extends OncePerRequestFilter {
	private final Logger blockedMerchantFilterLogger = LogManager.getLogger(BlockedMerchantFilter.class);

	private String allowPostRequest;
	private String allowPseudoMerchantPostRequest;
	private String internalUserPermission;
	private String pseudoMerchantMid;

	public BlockedMerchantFilter(String allowPostRequest, String internalUserPermission, String allowPseudoMerchantPostRequest, String pseudoMerchantMid) {
		this.allowPostRequest = allowPostRequest;
		this.internalUserPermission = internalUserPermission;
		this.allowPseudoMerchantPostRequest = allowPseudoMerchantPostRequest;
		this.pseudoMerchantMid = pseudoMerchantMid;
	}

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		try {
			Merchant merchant = SecurityUtils.getCurrentMerchant();
			boolean pseudoMerchant = SecurityUtils.hasPermission(internalUserPermission);

			if (null != merchant && !StringUtils.equals(request.getMethod(), RequestMethod.GET.toString())
					&& ((!merchant.getIsActive() && !allowPostForInactiveMerchant(request))
							|| (!merchant.getIsActive() && request.getRequestURL().toString().contains("/api/v1/onboard/upgrade") && merchant.getInactiveState().equalsIgnoreCase("terminated") && allowPostForInactiveMerchant(request))
								|| (pseudoMerchant && !allowPostForPseudoMerchant(request) && !pseudoMerchantMid.equals(merchant.getMid())))) {

				blockedMerchantFilterLogger.info(
						"Inactive or pseudo merchant trying to update something-->> mid: {}, uri: {}, pseudoMerchant: {}",
						merchant.getMid(), request.getRequestURI(), pseudoMerchant);
				Map<String, String> responseMap = new HashMap<>();
				responseMap.put("ERROR", "Requested operation not allowed for inactive merchant");
				responseMap.put("statusCode", "UMP-403");
				responseMap.put("statusMessage", "Requested operation not allowed for inactive merchant");
				response.setStatus(HttpServletResponse.SC_FORBIDDEN);
				response.setContentType(MediaType.APPLICATION_JSON_VALUE);
				response.getWriter().write(MappingUtils.convertObjectToJson(responseMap));

			} else {
				filterChain.doFilter(request, response);
			}
		} catch (Exception e) {
			blockedMerchantFilterLogger.error("Exception-->>", e);
		}
	}

	private boolean allowPostForInactiveMerchant(HttpServletRequest request) {
		return checkPostRequest(request, allowPostRequest);
	}

	private boolean allowPostForPseudoMerchant(HttpServletRequest request) {
		return checkPostRequest(request, allowPseudoMerchantPostRequest);
	}
	
	private boolean checkPostRequest(HttpServletRequest request, String postAPIs) {
		String[] requestURI = postAPIs.split(",");
		for (String uri : requestURI) {
			if (null != request.getRequestURL() && request.getRequestURL().toString().contains(uri.trim()))
				return true;
		}
		return false; 
	}

}
