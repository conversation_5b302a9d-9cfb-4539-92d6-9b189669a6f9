package com.paytm.aggregatorgateway.config.security.filters;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.security.web.util.matcher.RequestMatcher;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 
 * <AUTHOR>
 *
 */
public class CsrfRequestMatcher implements RequestMatcher {
	private Pattern allowedMethods = Pattern.compile("^(GET|HEAD|TRACE|OPTIONS)$");
	private static String[] urlToSkip= {"/api/v1/feedback/contact","/api/v1/business/contact"};
	private Set<String> allowCsrfOrigins;

	public CsrfRequestMatcher(String allowCsrfOrigins) {
		String[] allowCsrfOriginsArray = StringUtils.splitByWholeSeparator(allowCsrfOrigins, null);
		this.allowCsrfOrigins = new HashSet<>(Arrays.asList(allowCsrfOriginsArray));
	}

	@Override
	public boolean matches(HttpServletRequest request) {
		if (allowedMethods.matcher(request.getMethod()).matches() || isUrlAllowed(request)){
			return false;
		}
		//NO CSRF check for API calls
		return request.getHeader("x-auth-ump") == null;
	}

	private boolean isUrlAllowed(HttpServletRequest request) {
		
		String requestOrigin = request.getHeader(HttpHeaders.ORIGIN);
		if (allowCsrfOrigins.contains(requestOrigin)) {
			return true;
		}
		
		for (String url: urlToSkip) {
			if (null != request.getRequestURL() && request.getRequestURL().toString().contains(url.trim()))
				return true;
		}
		return false;
	}
}
