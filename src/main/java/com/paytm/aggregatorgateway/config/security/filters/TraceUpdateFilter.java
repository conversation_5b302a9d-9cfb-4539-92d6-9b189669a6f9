package com.paytm.aggregatorgateway.config.security.filters;

import com.paytm.aggregatorgateway.config.strategy.MdcPropagatingOnScheduleFunction;
import io.reactivex.plugins.RxJavaPlugins;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;


/**
 * 
 * <AUTHOR>
 *
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TraceUpdateFilter implements Filter {
	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		//Do Nothing
	}

	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain)
			throws IOEx<PERSON>, ServletException {
		try {
			String currentTraceIdentifier = ((HttpServletRequest) servletRequest).getHeader("x-trace-identifier");
			String url = ((HttpServletRequest) servletRequest).getRequestURI();
			String upstreamDomain = ((HttpServletRequest) servletRequest).getServerName();
			RxJavaPlugins.setScheduleHandler(new MdcPropagatingOnScheduleFunction());
			if (StringUtils.isNotBlank(currentTraceIdentifier)) {
				MDC.put("traceId", currentTraceIdentifier);
			}
			MDC.put("upstreamAPI", url);
			MDC.put("upstreamDomain", upstreamDomain);
			filterChain.doFilter(servletRequest, servletResponse);
		} finally {
			MDC.clear();
		}
		
	}

	@Override
	public void destroy() {
		//Do Nothing
	}

}