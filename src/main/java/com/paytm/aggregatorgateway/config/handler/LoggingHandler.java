package com.paytm.aggregatorgateway.config.handler;

import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.pgdashboard.commons.dto.User;
import com.paytm.aggregatorgateway.utils.RequestUtil;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.filter.AbstractRequestLoggingFilter;

import jakarta.servlet.http.HttpServletRequest;


/**
 * 
 * <AUTHOR>
 *
 */
public class LoggingHandler extends AbstractRequestLoggingFilter {

	private static final String UID_LOG = "uid=";
	private static final String EID_LOG = "eid=";
	private static final String SC = ";";

	@Override
	protected void beforeRequest(HttpServletRequest request, String message) {
		//Do Nothing
	}

	@Override
	protected void afterRequest(HttpServletRequest request, String message) {
		User user = SecurityUtils.getLoggedInUser();
		String uid = null;
		Long eid = null;
		if (user != null) {
			uid = user.getId();
			eid = user.getCurrentMerchant();
		}
		try {
			message = StringUtils.replaceOnce(message, PayTmPGConstants.UID_EID_LOGGER, UID_LOG + uid + SC + EID_LOG + eid + SC);
			message = message.substring(0, message.length() - 1) + SC + "xRealIp=" + RequestUtil.getXRealIP(request) + "]";
		} catch (Exception ex) {
			logger.error("UID-EID LOGGER ERROR", ex);
		}
		logger.info(message);
	}

}
