package com.paytm.aggregatorgateway.config.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;

import static com.paytm.aggregatorgateway.constants.ApplicationConstants.HEADERS;
import static com.paytm.aggregatorgateway.constants.ApplicationConstants.P4B_NUDGES_MID;

@Slf4j
@Component
public class RedisCustomCompressedKeyGenerator implements KeyGenerator {

    @Override
    public Object generate(Object target, Method method, Object... params) {
        StringBuilder key = new StringBuilder();

        if(params[0] instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) params[0];
            if(map.containsKey("method") && StringUtils.isNotBlank(map.get("method").toString())) {
                key.append(map.get("method")).append("|");
            }
            if(map.containsKey("url") && StringUtils.isNotBlank(map.get("url").toString())){
                key.append(map.get("url")).append("|");
            }
            if(map.containsKey(HEADERS) && Objects.nonNull(map.get(HEADERS))) {
                Map<String, String> headers = (Map<String, String>) map.get(HEADERS);
                if(!headers.isEmpty()) {
                    key.append("Header->");
                    headers.forEach((k, v) -> key.append(k).append(":").append(v).append(","));
                }
                key.deleteCharAt(key.length()-1);
            }
            if(ObjectUtils.isNotEmpty(map.get(P4B_NUDGES_MID))){
                key.append("P4B_NUDGES|").append(map.get(P4B_NUDGES_MID));
            }
        }
        key.append("_COMPRESSED");
        //log.info("key generated: {}", key.toString());
        return key.toString();
    }

}
