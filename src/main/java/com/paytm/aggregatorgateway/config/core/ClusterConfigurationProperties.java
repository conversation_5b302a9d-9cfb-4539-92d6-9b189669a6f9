package com.paytm.aggregatorgateway.config.core;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "spring.redis.cluster")
public class ClusterConfigurationProperties {



    private boolean enable;
    private boolean enableForContext;
    private List<String> nodes;
    private List<String>nodesForContext;

    private int maxRedirects;
    private int maxRedirectsForContext;

    public boolean getEnable() {
        return enable;
    }
    public boolean getEnableForContext() {
        return enableForContext;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }
    public void setEnableForContext(boolean enableForContext) {
        this.enableForContext = enableForContext;
    }
    public List<String> getNodes() {
        return nodes;
    }
    public List<String> getNodesForContext() {
        return nodesForContext;
    }

    public void setNodesForContext(List<String> nodesForContext) {
        this.nodesForContext = nodesForContext;
    }
    public void setNodes(List<String> nodes) {
        this.nodes = nodes;
    }

    public int getMaxRedirects() {
        return maxRedirects;
    }
    public int getMaxRedirectsForContext() {
        return maxRedirectsForContext;
    }

    public void setMaxRedirects(int maxRedirects) {
        this.maxRedirects = maxRedirects;
    }
    public void setMaxRedirectsForContext(int maxRedirectsForContext) {
        this.maxRedirectsForContext = maxRedirectsForContext;
    }

}