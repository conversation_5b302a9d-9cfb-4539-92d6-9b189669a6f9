package com.paytm.aggregatorgateway.config.core;

import io.lettuce.core.ClientOptions;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class RedisConfig implements CachingConfigurer {

	private static final Logger LOGGER = LoggerFactory.getLogger(RedisConfig.class);
	
		@Value("${spring.redis.sentinel.master}")
		private String sentinalMaster;
		@Value("${spring.redis.sentinel.nodes}")
		private String sentinalNodes;
	    @Value("${user.cache.ttl}")
	    private Long userCacheExpiryTime;
	    @Value("${is.sentinel.enable}")
	    private String isSentinalEnabled;
	    @Value("${spring.redis.host}")
		private String redisHost;
		@Value("${spring.redis.port}")
		private Integer redisPort;

		@Value("${spring.jedispool.connect.wait.time}")
		private Long jedisConnectWaitTime;

		@Value("${spring.jedispool.minIdleConnections}")
		private Integer jedisMinIdleConn;

		@Value("${spring.jedispool.maxIdleConnections}")
		private Integer jedisMaxIdleConn;

		@Value("${spring.jedispool.maxTotalConnections}")
		private Integer jedisTotalConn;

		@Value("${spring.redis.socket.readtimeout}")
		private Integer redisReadTimeout;

		@Value("${client.cache.ttl}")
		private Long clientCacheExpiryTime;

		@Value("${limit.upgrade.cache.ttl}")
		private Long limitUpgradeCacheExpiryTime;

		@Value("${limit.upgrade.queue.cache.ttl}")
		private Long limitUpgradeQueueCacheExpiryTime;

		@Value("${payment.hold.cache.ttl}")
		private Long paymentHoldCacheExpiryTime;

		@Value("${payment.hold.close.cache.ttl}")
		private Long paymentHoldCloseCacheExpiryTime;

		@Value("${business.proof.cache.ttl}")
		private Long businessProofCacheExpiryTime;

	@Value("${business.proof.active.cache.ttl}")
	private Long businessProofActiveCacheExpiryTime;

	@Value("${p4b.nudges.cache.ttl:86400}")
	private Long p4bNudgesCacheExpiryTime;

	@Value("${devicePreference.cache.ttl:900}")
	private Long devicePreferencesExpiryTime;

	@Value("${consent.cache.ttl:86400}")
	private Long consentCacheExpiryTime;

	@Value("${tws.flag.cache.ttl:240}")
	private Long twsFlagCacheExpiryTime;


		@Autowired
		private ClusterConfigurationProperties clusterProperties;

	    @Bean
	    public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
	        return new PropertySourcesPlaceholderConfigurer();
	    }

		@Bean
		@Primary
		RedisClusterConfiguration redisConfiguration() {
			RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration(clusterProperties.getNodes());
			redisClusterConfiguration.setMaxRedirects(clusterProperties.getMaxRedirects());
			return redisClusterConfiguration;
		}
		@Bean
		RedisClusterConfiguration redisConfigurationForContext() {
			RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration(clusterProperties.getNodesForContext());
			redisClusterConfiguration.setMaxRedirects(clusterProperties.getMaxRedirectsForContext());
			return redisClusterConfiguration;
		}

		@Bean
		@Primary
		public LettuceConnectionFactory lettuceConnectionFactory() {

			if(clusterProperties.getEnableForContext()){
				return new LettuceConnectionFactory(redisConfiguration(), getClientConfig());
			} else if("yes".equals(isSentinalEnabled)) {
				RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration().master(sentinalMaster);
				for (String node : sentinalNodes.split(",")) {
					String[] split = node.split(":");
					sentinelConfig.sentinel(split[0], Integer.parseInt(split[1]));
				}
				return new LettuceConnectionFactory(sentinelConfig, getClientConfig());
			} else {
				RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration(redisHost, redisPort);
				return new LettuceConnectionFactory(standaloneConfig, getClientConfig());
			}
		}

		@Bean
		public LettuceConnectionFactory lettuceConnectionFactoryForContext() {

			if(clusterProperties.getEnableForContext()){
				return new LettuceConnectionFactory(redisConfigurationForContext(), getClientConfig());
			} else if("yes".equals(isSentinalEnabled)) {
				RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration().master(sentinalMaster);
				for (String node : sentinalNodes.split(",")) {
					String[] split = node.split(":");
					sentinelConfig.sentinel(split[0], Integer.parseInt(split[1]));
				}
				return new LettuceConnectionFactory(sentinelConfig, getClientConfig());
			} else {
				RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration(redisHost, redisPort);
				return new LettuceConnectionFactory(standaloneConfig, getClientConfig());
			}
		}

		public LettucePoolingClientConfiguration getClientConfig() {

			return LettucePoolingClientConfiguration.builder()
					.commandTimeout(Duration.ofMillis(redisReadTimeout))
					.clientOptions(ClientOptions.builder().autoReconnect(true).build())
					.poolConfig(poolingConfig())
					.build();
		}

		public GenericObjectPoolConfig<?> poolingConfig() {

			GenericObjectPoolConfig<?> poolingConfig = new GenericObjectPoolConfig<>();
			// minimum number of free connections, default 0
			poolingConfig.setMinIdle(jedisMinIdleConn);
			// maximum number of free connections, default 8
			poolingConfig.setMaxIdle(jedisMaxIdleConn);
			// Maximum number of connections Default 16
			poolingConfig.setMaxTotal(jedisTotalConn);
			// Get the maximum number of milliseconds to wait for the connection (BlockWhenExhausted if set to block), throw an exception if it times out, less than zero: block the indeterminate time,
			// default -1
			poolingConfig.setMaxWaitMillis(jedisConnectWaitTime);

			return poolingConfig;
		}

		@Bean
		public CacheManager redisCacheManagerForObjectCaching() {
			RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
					.entryTtl(Duration.ofSeconds(userCacheExpiryTime));

			Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
			cacheConfigurations.put("clientSecret", RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(clientCacheExpiryTime)));

			return RedisCacheManager.builder(lettuceConnectionFactory())
					.cacheDefaults(defaultCacheConfig)
					.withInitialCacheConfigurations(cacheConfigurations)
					.build();
		}

		@Bean
		public CacheManager redisCacheManager() {
			RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
					.disableKeyPrefix()
					.serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
					.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
					.entryTtl(Duration.ofSeconds(userCacheExpiryTime));

			Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
			RedisCacheConfiguration customCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig().disableKeyPrefix().serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
					.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()));

			cacheConfigurations.put("LIMIT_UPGRADE", customCacheConfiguration.entryTtl(Duration.ofSeconds(limitUpgradeCacheExpiryTime)));
			cacheConfigurations.put("LIMIT_UPGRADE_QUEUE", customCacheConfiguration.entryTtl(Duration.ofSeconds(limitUpgradeQueueCacheExpiryTime)));
			cacheConfigurations.put("PAYMENT_HOLD", customCacheConfiguration.entryTtl(Duration.ofSeconds(paymentHoldCacheExpiryTime)));
			cacheConfigurations.put("PAYMENT_HOLD_CLOSE", customCacheConfiguration.entryTtl(Duration.ofSeconds(paymentHoldCloseCacheExpiryTime)));
			cacheConfigurations.put("BUSINESS_PROOF", customCacheConfiguration.entryTtl(Duration.ofSeconds(businessProofCacheExpiryTime)));
			cacheConfigurations.put("BUSINESS_PROOF_ACTIVE", customCacheConfiguration.entryTtl(Duration.ofSeconds(businessProofActiveCacheExpiryTime)));
			cacheConfigurations.put("P4B_NUDGES", customCacheConfiguration.entryTtl(Duration.ofSeconds(p4bNudgesCacheExpiryTime)));
			cacheConfigurations.put("devicePreferences", customCacheConfiguration.entryTtl(Duration.ofSeconds(devicePreferencesExpiryTime)));
			cacheConfigurations.put("CONSENT", customCacheConfiguration.entryTtl(Duration.ofSeconds(consentCacheExpiryTime)));
			cacheConfigurations.put("TWS_SUBSCRIPTION_ACTIVE", customCacheConfiguration.entryTtl(Duration.ofHours(twsFlagCacheExpiryTime)));


			return RedisCacheManager.builder(lettuceConnectionFactory())
					.cacheDefaults(defaultCacheConfig)
					.withInitialCacheConfigurations(cacheConfigurations)
					.build();
		}

		@Bean
		public CacheManager redisCacheManagerForContext() {
			RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
					.entryTtl(Duration.ofSeconds(userCacheExpiryTime))
					.serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
					.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
					.computePrefixWith(cacheName -> cacheName+":");

			return RedisCacheManager.builder(lettuceConnectionFactoryForContext())
					.cacheDefaults(defaultCacheConfig)
					.build();
		}

        @Bean
        public CacheManager redisCacheManagerContext() {
			RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
					.entryTtl(Duration.ofSeconds(userCacheExpiryTime))
					.serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
					.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()));

			return RedisCacheManager.builder(lettuceConnectionFactory())
					.cacheDefaults(defaultCacheConfig)
					.build();
        }

	    @Override
	    public CacheErrorHandler errorHandler() {
	        return new RedisCacheErrorHandler();
	    }

	    public static class RedisCacheErrorHandler implements CacheErrorHandler {

	    	@Override
	        public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
	        	LOGGER.info("Unable to get from cache : {} : {}", cache.getName(), exception.getMessage());
	        }
	    	@Override
	        public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
	        	LOGGER.info("Unable to put into cache : {} : {}", cache.getName(), exception.getMessage());
	        }
	    	@Override
	        public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
	        	LOGGER.info("Unable to evict from cache : {} : {}", cache.getName(), exception.getMessage());
	        }
	    	@Override
	        public void handleCacheClearError(RuntimeException exception, Cache cache) {
	        	LOGGER.info("Unable to clean cache : {} : {}", cache.getName(), exception.getMessage());
	        }
	    }
}
