package com.paytm.aggregatorgateway.config.core;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.paytm.aggregatorgateway.config.handler.LoggingHandler;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class AppConfigCore {
	
	@Autowired
	private Environment environment;

	@Bean
	public MappingJackson2HttpMessageConverter jacksonConvertor() {
		MappingJackson2HttpMessageConverter convertor = new MappingJackson2HttpMessageConverter();
		convertor.getObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		convertor.getObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);

		return convertor;
	}

	@Bean(name = "monitorLogExecutor")
	public Executor getMonitorLogExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(environment.getProperty("async.monitor.pool.size.core", Integer.class, 20));
		executor.setMaxPoolSize(environment.getProperty("async.monitor.pool.size.max", Integer.class, 40));
		executor.setQueueCapacity(environment.getProperty("async.monitor.queue.size", Integer.class, 40));
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		executor.setThreadNamePrefix("ump-async-monitorlog-");
		executor.initialize();
		return executor;
	}

	@Bean
	public LoggingHandler requestLoggingFilter() {
		LoggingHandler filter = new LoggingHandler();
		filter.setIncludeQueryString(true);
		filter.setIncludeClientInfo(true);
		filter.setIncludePayload(true);
		filter.setMaxPayloadLength(1024);
		filter.setAfterMessagePrefix("<<<<REQUEST RECEIVED>>>> [" + PayTmPGConstants.UID_EID_LOGGER);
		return filter;
	}
}
