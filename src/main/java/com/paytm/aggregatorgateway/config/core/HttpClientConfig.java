package com.paytm.aggregatorgateway.config.core;

import com.paytm.aggregatorgateway.constants.DomainConstants;
import org.apache.hc.client5.http.ConnectionKeepAliveStrategy;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.http.Header;
import org.apache.hc.core5.http.HeaderElement;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.http.message.BasicHeaderElementIterator;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.protocol.HTTP;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.HttpsURLConnection;
import java.util.HashMap;
import java.util.Iterator;

@Configuration
public class HttpClientConfig {

	@Value("${http.default.keep-alive.timeout:30000}")
	private long keepAliveTimeout;

	@Value("${http.validate.inactivity:200}")
	private int inactivityTimeInMillis;

	//UMP
	@Value("${http.timeout.ump:4000}")
	private int timeoutMsUMP;

	@Value("${http.connect.timeout.ump:4000}")
	private int connectTimeoutMsUMP;

	@Value("${http.max.conn.per-route.ump:200}")
	private int maxConnPerRouteUMP;

	@Value("${http.max.conn.total.ump:200}")
	private int maxTotalConnUMP;

	//PROMO_CLM
	@Value("${http.timeout.promoclm:2500}")
	private int timeoutMsPromoCLM;

	@Value("${http.connect.timeout.promoclm:2500}")
	private int connectTimeoutMsPromoCLM;

	@Value("${http.max.conn.per-route.promoclm:200}")
	private int maxConnPerRoutePromoCLM;

	@Value("${http.max.conn.total.promoclm:200}")
	private int maxTotalConnPromoCLM;

	//BOSS
	@Value("${http.timeout.boss:2000}")
	private int timeoutMsBOSS;

	@Value("${http.connect.timeout.boss:2000}")
	private int connectTimeoutMsBOSS;

	@Value("${http.max.conn.per-route.boss:200}")
	private int maxConnPerRouteBOSS;

	@Value("${http.max.conn.total.boss:200}")
	private int maxTotalConnBOSS;

	//Clevertap
	@Value("${http.timeout.clevertap:3000}")
	private int timeoutMsCleverTap;

	@Value("${http.connect.timeout.clevertap:3000}")
	private int connectTimeoutMsCleverTap;

	@Value("${http.max.conn.per-route.clevertap:200}")
	private int maxConnPerRouteClevertap;

	@Value("${http.max.conn.total.clevertap:200}")
	private int maxTotalConnClevertap;

	//Subscription
	@Value("${http.timeout.subscription:3000}")
	private int timeoutMsSubscription;

	@Value("${http.connect.timeout.subscription:3000}")
	private int connectTimeoutMsSubscription;

	@Value("${http.max.conn.per-route.subscription:200}")
	private int maxConnPerRouteSubscription;

	@Value("${http.max.conn.total.subscription:200}")
	private int maxTotalConnSubscription;

	//UPS
	@Value("${http.timeout.ups:500}")
	private int timeoutMsUPS;

	@Value("${http.connect.timeout.ups:500}")
	private int connectTimeoutMsUPS;

	@Value("${http.max.conn.per-route.ups:200}")
	private int maxConnPerRouteUPS;

	@Value("${http.max.conn.total.ups:200}")
	private int maxTotalConnUPS;

	//Rewards
	@Value("${http.timeout.rewards:2000}")
	private int timeoutMsRewards;

	@Value("${http.connect.timeout.rewards:2000}")
	private int connectTimeoutMsRewards;

	@Value("${http.max.conn.per-route.rewards:200}")
	private int maxConnPerRouteRewards;

	@Value("${http.max.conn.total.rewards:200}")
	private int maxTotalConnRewards;

	//KYB
	@Value("${http.timeout.kyb:3000}")
	private int timeoutMsKYB;

	@Value("${http.connect.timeout.kyb:3000}")
	private int connectTimeoutMsKYB;

	@Value("${http.max.conn.per-route.kyb:200}")
	private int maxConnPerRouteKYB;

	@Value("${http.max.conn.total.kyb:200}")
	private int maxTotalConnKYB;

	//CST
	@Value("${http.timeout.cst:4000}")
	private int timeoutMsCST;

	@Value("${http.connect.timeout.cst:4000}")
	private int connectTimeoutMsCST;

	@Value("${http.max.conn.per-route.cst:200}")
	private int maxConnPerRouteCST;

	@Value("${http.max.conn.total.cst:200}")
	private int maxTotalConnCST;

	//CST Service Call
	@Value("${http.timeout.cst:2000}")
	private int timeoutMsCSTServiceCall;

	@Value("${http.connect.timeout.cst:2000}")
	private int connectTimeoutMsCSTServiceCall;

	@Value("${http.max.conn.per-route.cst:200}")
	private int maxConnPerRouteCSTServiceCall;

	@Value("${http.max.conn.total.cst:200}")
	private int maxTotalConnCSTServiceCall;

	//Maquette
	@Value("${http.timeout.maquette:2000}")
	private int timeoutMsMaquette;

	@Value("${http.connect.timeout.maquette:2000}")
	private int connectTimeoutMsMaquette;

	@Value("${http.max.conn.per-route.maquette:200}")
	private int maxConnPerRouteMaquette;

	@Value("${http.max.conn.total.maquette:200}")
	private int maxTotalConnMaquette;

	//FSM
	@Value("${http.timeout.fsm:2000}")
	private int timeoutMsFsm;

	@Value("${http.connect.timeout.fsm:2000}")
	private int connectTimeoutMsFsm;

	@Value("${http.max.conn.per-route.fsm:200}")
	private int maxConnPerRouteFsm;

	@Value("${http.max.conn.total.fsm:200}")
	private int maxTotalConnFsm;

	//OTHERS
	@Value("${http.max.conn.total.others:200}")
	private int maxTotalConnOthers;

	@Value("${http.timeout.others:10000}")
	private int timeoutMsOthers;

	@Value("${http.connect.timeout.others:5000}")
	private int connectTimeoutMsOthers;

	@Value("${http.max.conn.per-route.others:200}")
	private int maxConnPerRouteOthers;

	//Digital Proxy
	@Value("${http.timeout.digitalproxy:5000}")
	private int timeoutMsDigitalProxy;

	@Value("${http.connect.timeout.digitalproxy:5000}")
	private int connectTimeoutMsDigitalProxy;

	@Value("${http.max.conn.per-route.digitalproxy:200}")
	private int maxConnPerRouteDigitalProxy;

	@Value("${http.max.conn.total.digitalproxy:200}")
	private int maxTotalConnDigitalProxy;

	//Notification
	@Value("${http.timeout.notification:2000}")
	private int timeoutMsNotification;

	@Value("${http.connect.timeout.notification:2000}")
	private int connectTimeoutMsNotification;

	@Value("${http.max.conn.per-route.notification:200}")
	private int maxConnPerRouteNotification;

	@Value("${http.max.conn.total.notification:200}")
	private int maxTotalConnNotification;

	@Value("${http.timeout.cst.mgw:5000}")
	private int timeoutMsCSTMGW;

	@Value("${http.connect.timeout.cst.mgw:5000}")
	private int connectTimeoutMsCSTMGW;

	@Value("${http.max.conn.per-route.cst.mgw:200}")
	private int maxConnPerRouteCSTMGW;

	@Value("${http.max.conn.total.cst.mgw:200}")
	private int maxTotalConnCSTMGW;

	//KYB ADDRESS
	@Value("${http.timeout.kyb:2000}")
	private int timeoutMsKYBAddress;

	@Value("${http.connect.timeout.kyb:2000}")
	private int connectTimeoutMsKYBAddress;

	@Value("${http.max.conn.per-route.kyb:200}")
	private int maxConnPerRouteKYBAddress;

	@Value("${http.max.conn.total.kyb:200}")
	private int maxTotalConnKYBAddress;

	//EOS
	@Value("${http.timeout.eos:2000}")
	private int timeoutMsEos;

	@Value("${http.connect.timeout.eos:2000}")
	private int connectTimeoutMsEos;

	@Value("${http.max.conn.per-route.eos:200}")
	private int maxConnPerRouteEos;

	@Value("${http.max.conn.total.eos:200}")
	private int maxTotalConnEos;

	//CENTRAL TOOLKIT
	@Value("${http.timeout.CentralToolkit:2000}")
	private int timeoutMsCentralToolkit;

	@Value("${http.connect.timeout.CentralToolkit:2000}")
	private int connectTimeoutMsCentralToolkit;

	@Value("${http.max.conn.per-route.CentralToolkit:200}")
	private int maxConnPerRouteCentralToolkit;

	@Value("${http.max.conn.total.CentralToolkit:200}")
	private int maxTotalConnCentralToolkit;

	//STORE FRONT
	@Value("${http.timeout.storefront:5000}")
	private int timeoutMsStoreFront;

	@Value("${http.connect.timeout.storefront:5000}")
	private int connectTimeoutMsStoreFront;

	@Value("${http.max.conn.per-route.storefront:200}")
	private int maxConnPerRouteStoreFront;

	@Value("${http.max.conn.total.storefront:200}")
	private int maxTotalConnStoreFront;

	//SURVEY
	@Value("${http.timeout.survey:5000}")
	private int timeoutMsSurvey;

	@Value("${http.connect.timeout.survey:5000}")
	private int connectTimeoutMsSurvey;

	@Value("${http.max.conn.per-route.survey:200}")
	private int maxConnPerRouteSurvey;

	@Value("${http.max.conn.total.survey:200}")
	private int maxTotalConnSurvey;

	//OE
	@Value("${http.timeout.oe:5000}")
	private int timeoutMsOE;

	@Value("${http.connect.oe.survey:5000}")
	private int connectTimeoutMsOE;

	@Value("${http.max.conn.per-route.oe:200}")
	private int maxConnPerRouteOE;

	@Value("${http.max.conn.total.oe:200}")
	private int maxTotalConnOE;

	//DMS
	@Value("${http.timeout.oe:5000}")
	private int timeoutMsDMS;

	@Value("${http.connect.oe.survey:5000}")
	private int connectTimeoutMsDMS;

	@Value("${http.max.conn.per-route.oe:200}")
	private int maxConnPerRouteDMS;

	@Value("${http.max.conn.total.oe:200}")
	private int maxTotalConnDMS;

	//OMS
	@Value("${http.timeout.oe:5000}")
	private int timeoutMsOMS;

	@Value("${http.connect.oe.survey:5000}")
	private int connectTimeoutMsOMS;

	@Value("${http.max.conn.per-route.oe:200}")
	private int maxConnPerRouteOMS;

	@Value("${http.max.conn.total.oe:200}")
	private int maxTotalConnOMS;

	//CHECKOUT
	@Value("${http.timeout.checkout:2000}")
	private int timeoutMsCheckout;

	@Value("${http.connect.timeout.checkout:2000}")
	private int connectTimeoutCheckout;

	@Value("${http.max.conn.per-route.checkout:200}")
	private int maxConnPerRouteCheckout;

	@Value("${http.max.conn.total.checkout:200}")
	private int maxTotalConnCheckout;

	//VOUCHER
	@Value("${http.timeout.oe:5000}")
	private int timeoutMsVoucher;

	@Value("${http.connect.oe.survey:5000}")
	private int connectTimeoutMsVoucher;

	@Value("${http.max.conn.per-route.oe:200}")
	private int maxConnPerRouteVoucher;

	@Value("${http.max.conn.total.oe:200}")
	private int maxTotalConnVoucher;

	//OCR
	@Value("${http.timeout.ocr:5000}")
	private int timeoutMsOCR;

	@Value("${http.connect.timeout.ocr:5000}")
	private int connectTimeoutMsOCR;

	@Value("${http.max.conn.per-route.ocr:200}")
	private int maxConnPerRouteOCR;

	@Value("${http.max.conn.total.ocr:200}")
	private int maxTotalConnOCR;

	public static HashMap<String, PoolingHttpClientConnectionManager> connectionMangerMap = new HashMap<>();

	@Bean("restTemplateUMP")
	public RestTemplate restTemplateUMP(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.UMP))).build();
	}

	@Bean("restTemplatePromoCLM")
	public RestTemplate restTemplatePromoCLM(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.PROMO_CLM))).build();
	}

	@Bean("restTemplateBOSS")
	public RestTemplate restTemplateBOSS(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.BOSS))).build();
	}

	@Bean("restTemplateCleverTap")
	public RestTemplate restTemplateCleverTap(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.CLEVERTAP))).build();
	}
	@Bean("restTemplateSubscription")
	public RestTemplate restTemplateSubscription(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.SUBSCRIPTION))).build();
	}

	@Bean("restTemplateUPS")
	public RestTemplate restTemplateUPS(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.UPS))).build();
	}

	@Bean("restTemplateRewards")
	public RestTemplate restTemplateRewards(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.REWARDS))).build();
	}

	@Bean("restTemplateKYB")
	public RestTemplate restTemplateKYB(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.KYB))).build();
	}

	@Bean("restTemplateCST")
	public RestTemplate restTemplateCST(RestTemplateBuilder restTemplateBuilder){
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.CST))).build();
	}
	@Bean("restTemplateCSTServiceCall")
	public RestTemplate restTemplateCSTServiceCall(RestTemplateBuilder restTemplateBuilder){
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.CST_SERVICE_CALL))).build();
	}

	@Bean("restTemplateMaquette")
	public RestTemplate restTemplateMaquette(RestTemplateBuilder restTemplateBuilder){
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.MAQUETTE))).build();
	}
	@Bean("restTemplateFsm")
	public RestTemplate restTemplateFsm(RestTemplateBuilder restTemplateBuilder){
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.FSM))).build();
	}

	@Bean("restTemplateOthers")
	public RestTemplate restTemplateOthers(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.OTHERS))).build();
	}

	@Bean("restTemplateDigitalProxy")
	public RestTemplate restTemplateDigitalProxy(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.DIGITALPROXY))).build();
	}
	@Bean("restTemplateCstMGW")
	public RestTemplate restTemplateCstMGW(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.CST_MGW))).build();
	}

	@Bean("restTemplateNotification")
	public RestTemplate restTemplateNotification(RestTemplateBuilder restTemplateBuilder){
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.NOTIFICATION))).build();
	}

	@Bean("restTemplateKYBAddress")
	public RestTemplate restTemplateKYBAddress(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.KYB_ADDRESS))).build();
	}
	@Bean("restTemplateEos")
	public RestTemplate restTemplateEos(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.EOS))).build();
	}
	@Bean("restTemplateCentralToolKit")
	public RestTemplate restTemplateCentralToolKit(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.CENTRAL_TOOL_KIT))).build();
	}

	@Bean("restTemplateStoreFront")
	public RestTemplate restTemplateStoreFront(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.STORE_FRONT))).build();
	}

	@Bean("restTemplateSurvey")
	public RestTemplate restTemplateSurvey(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.SURVEY))).build();
	}

	@Bean("restTemplateOE")
	public RestTemplate restTemplateOE(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.OE))).build();
	}
	@Bean("restTemplateOMS")
	public RestTemplate restTemplateOMS(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.OMS))).build();
	}
	@Bean("restTemplateCheckout")
	public RestTemplate restTemplateCheckout(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.CHECKOUT))).build();
	}

	@Bean("restTemplateDMS")
	public RestTemplate restTemplateDMS(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.DMS))).build();

	}
	@Bean("restTemplateVoucher")
	public RestTemplate restTemplateVoucher(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.VOUCHER))).build();

	}

	@Bean("restTemplateOCR")
	public RestTemplate restTemplateOCR(RestTemplateBuilder restTemplateBuilder) {
		HttpsURLConnection.setDefaultHostnameVerifier(NoopHostnameVerifier.INSTANCE);
		return restTemplateBuilder.requestFactory(() -> new HttpComponentsClientHttpRequestFactory(getHttpClient(DomainConstants.OCR))).build();
	}
	private CloseableHttpClient getHttpClient(String domain) {
		PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
		int timeout;	//connection request timeout
		int socketTimeout;	//timeout between 2 packets
		int connectTimeout;	//timeout till which connection is maintained


		switch(domain) {
			case DomainConstants.UMP:
				timeout = timeoutMsUMP;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsUMP;
				connectionManager.setMaxTotal(maxTotalConnUMP);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteUMP);
				break;
			case DomainConstants.BOSS:
				timeout = timeoutMsBOSS;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsBOSS;
				connectionManager.setMaxTotal(maxTotalConnBOSS);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteBOSS);
				break;
			case DomainConstants.SUBSCRIPTION:
				timeout = timeoutMsSubscription;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsSubscription;
				connectionManager.setMaxTotal(maxTotalConnSubscription);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteSubscription);
				break;
			case DomainConstants.CLEVERTAP:
				timeout = timeoutMsCleverTap;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsCleverTap;
				connectionManager.setMaxTotal(maxTotalConnClevertap);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteClevertap);
				break;
			case DomainConstants.PROMO_CLM:
				timeout = timeoutMsPromoCLM;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsPromoCLM;
				connectionManager.setMaxTotal(maxTotalConnPromoCLM);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRoutePromoCLM);
				break;
			case DomainConstants.UPS:
				timeout = timeoutMsUPS;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsUPS;
				connectionManager.setMaxTotal(maxTotalConnUPS);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteUPS);
				break;
			case DomainConstants.REWARDS:
				timeout = timeoutMsRewards;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsRewards;
				connectionManager.setMaxTotal(maxTotalConnRewards);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteRewards);
				break;
			case DomainConstants.KYB:
				timeout = timeoutMsKYB;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsKYB;
				connectionManager.setMaxTotal(maxTotalConnKYB);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteKYB);
				break;
			case DomainConstants.CST:
				timeout = timeoutMsCST;
				socketTimeout = timeout;
				connectTimeout = connectTimeoutMsCST;
				connectionManager.setMaxTotal(maxTotalConnCST);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteCST);
				break;

			case DomainConstants.CST_SERVICE_CALL:
				timeout = timeoutMsCSTServiceCall;
				socketTimeout = timeout;
				connectTimeout = connectTimeoutMsCSTServiceCall;
				connectionManager.setMaxTotal(maxTotalConnCSTServiceCall);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteCSTServiceCall);
				break;
			case DomainConstants.OTHERS:
				timeout = timeoutMsOthers;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsOthers;
				connectionManager.setMaxTotal(maxTotalConnOthers);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteOthers);
				break;
			case DomainConstants.MAQUETTE:
				timeout = timeoutMsMaquette;
				socketTimeout = timeout;
				connectTimeout = connectTimeoutMsMaquette;
				connectionManager.setMaxTotal(maxTotalConnMaquette);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteMaquette);
				break;
			case DomainConstants.FSM:
				timeout = timeoutMsFsm;
				socketTimeout = timeout;
				connectTimeout = connectTimeoutMsFsm;
				connectionManager.setMaxTotal(maxTotalConnFsm);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteFsm);
				break;
			case DomainConstants.DIGITALPROXY:
				timeout = timeoutMsDigitalProxy;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsDigitalProxy;
				connectionManager.setMaxTotal(maxTotalConnDigitalProxy);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteDigitalProxy);
				break;
			case DomainConstants.NOTIFICATION:
				timeout = timeoutMsNotification;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsNotification;
				connectionManager.setMaxTotal(maxTotalConnNotification);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteNotification);
				break;
			case DomainConstants.CST_MGW:
				timeout = timeoutMsCSTMGW;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsCSTMGW;
				connectionManager.setMaxTotal(maxTotalConnCSTMGW);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteCSTMGW);
				break;
			case DomainConstants.KYB_ADDRESS:
				timeout = timeoutMsKYBAddress;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsKYBAddress;
				connectionManager.setMaxTotal(maxTotalConnKYBAddress);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteKYBAddress);
				break;
			case DomainConstants.EOS:
				timeout = timeoutMsEos;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsEos;
				connectionManager.setMaxTotal(maxTotalConnEos);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteEos);
				break;
			case DomainConstants.CENTRAL_TOOL_KIT:
				timeout = timeoutMsCentralToolkit;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsCentralToolkit;
				connectionManager.setMaxTotal(maxTotalConnCentralToolkit);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteCentralToolkit);
				break;
			case DomainConstants.STORE_FRONT:
				timeout = timeoutMsStoreFront;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsStoreFront;
				connectionManager.setMaxTotal(maxTotalConnStoreFront);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteStoreFront);
				break;
			case DomainConstants.SURVEY:
				timeout = timeoutMsSurvey;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsSurvey;
				connectionManager.setMaxTotal(maxTotalConnSurvey);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteSurvey);
				break;
			case DomainConstants.OE:
				timeout = timeoutMsOE;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsOE;
				connectionManager.setMaxTotal(maxTotalConnOE);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteOE);
				break;
			case DomainConstants.OMS:
				timeout = timeoutMsOMS;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsOMS;
				connectionManager.setMaxTotal(maxTotalConnOMS);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteOMS);
				break;
			case DomainConstants.CHECKOUT:
				timeout = timeoutMsCheckout;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutCheckout;
				connectionManager.setMaxTotal(maxTotalConnCheckout);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteCheckout);
				break;
			case DomainConstants.DMS:
				timeout = timeoutMsDMS;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsDMS;
				connectionManager.setMaxTotal(maxTotalConnDMS);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteDMS);
				break;
			case DomainConstants.VOUCHER:
				timeout = timeoutMsVoucher;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsVoucher;
				connectionManager.setMaxTotal(maxTotalConnVoucher);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteVoucher);
				break;
			case DomainConstants.OCR:
				timeout = timeoutMsOCR;
				socketTimeout=timeout;
				connectTimeout=connectTimeoutMsOCR;
				connectionManager.setMaxTotal(maxTotalConnOCR);
				connectionManager.setDefaultMaxPerRoute(maxConnPerRouteOCR);
				break;
			default:
				connectionManager.close();
				throw new RuntimeException("No separate rest caller defined for the domain: " + domain);
		}

		connectionManager.setValidateAfterInactivity(TimeValue.ofMilliseconds(inactivityTimeInMillis));
		RequestConfig config = RequestConfig.custom()
				.setConnectTimeout(Timeout.ofMilliseconds(connectTimeout))
				.setConnectionRequestTimeout(Timeout.ofMilliseconds(timeout)).build();

		SocketConfig socketConfig = SocketConfig.custom()
				.setSoTimeout(Timeout.ofMilliseconds(socketTimeout))
				.build();
		connectionManager.setDefaultSocketConfig(socketConfig);

		connectionMangerMap.put(domain, connectionManager);

		CloseableHttpClient httpclient = HttpClientBuilder.create().setDefaultRequestConfig(config).setConnectionManager(connectionManager)
				.setKeepAliveStrategy(connectionKeepAliveStrategy()).build();
		return httpclient;
	}

	@Bean
	public ConnectionKeepAliveStrategy connectionKeepAliveStrategy() {
		return (httpResponse, httpContext) -> {
			Iterator<Header> headerIterator = httpResponse.headerIterator(HTTP.CONN_KEEP_ALIVE);
			BasicHeaderElementIterator elementIterator = new BasicHeaderElementIterator(headerIterator);

			while (elementIterator.hasNext()) {
				HeaderElement element = elementIterator.next();
				String param = element.getName();
				String value = element.getValue();
				if (value != null && param.equalsIgnoreCase("timeout")) {
					try {
						return TimeValue.ofMilliseconds(Long.valueOf(value) * 1000);
					} catch (NumberFormatException e) {
						return TimeValue.ofMilliseconds(keepAliveTimeout);
					}
				}
			}

			return TimeValue.ofMilliseconds(keepAliveTimeout);
		};
	}

}


