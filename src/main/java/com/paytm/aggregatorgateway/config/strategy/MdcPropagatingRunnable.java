package com.paytm.aggregatorgateway.config.strategy;

import org.slf4j.MDC;

import java.util.Map;

public final class MdcPropagatingRunnable implements Runnable {
   private final Runnable runnable0;
   private final Map<String, String> context;

   /**
    * Decorates an {@link Runnable} so that it executes with the current {@link MDC} as its context.
    *
    * @param runnable0 the {@link Runnable} to decorate.
    */
   public MdcPropagatingRunnable(final Runnable runnable0) {
       this.runnable0 = runnable0;
       this.context = MDC.getCopyOfContextMap();
   }

   @Override
   public void run() {
       final Map<String, String> originalMdc = MDC.getCopyOfContextMap();

       if (context != null) {
           MDC.setContextMap(context);
       }
       try {
           this.runnable0.run();
       } finally {
           if (originalMdc != null) {
               MDC.setContextMap(originalMdc);
           }
       }
   }
}
