package com.paytm.aggregatorgateway.service.helper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.paytm.aggregatorgateway.service.UPSService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

@Component
public class RedisHelper {

    @Autowired
    private UPSService upsService;

    @Autowired
    private ObjectMapper objectMapper;


    private static final Logger LOGGER = LogManager.getLogger(RedisHelper.class);

    static Gson gson = new GsonBuilder().create();

    @Cacheable(value = "LIMIT_UPGRADE", key = "#token", unless="#result==null",cacheManager = "redisCacheManager")
    public String getLastNotificationSentOn(String token){
        return null;
    }

    @Cacheable(value = "LIMIT_UPGRADE", key = "#token", unless="#result==null",cacheManager = "redisCacheManager")
    public String setLastNotificationSentOn(String token){
        return LocalDateTime.now().toString();
    }

    @CacheEvict(value="LIMIT_UPGRADE",key = "#token",cacheManager = "redisCacheManager")
    public void evictLastSentOnKey(String token) {
        LOGGER.info("Clearing last sent on cache from redis for key: {}.", token);
    }

    @Cacheable(value = "LIMIT_UPGRADE_QUEUE", key = "#token", unless="#result==null",cacheManager = "redisCacheManager")
    public String getQueuedNotification(String token){
        return null;
    }

    @CacheEvict(value="LIMIT_UPGRADE_QUEUE",key = "#token",cacheManager = "redisCacheManager")
    public void evictQueue(String token) {
        LOGGER.info("Clearing queue cache from redis for key: {}.", token);
    }

    @Cacheable(value = "LIMIT_UPGRADE_QUEUE", key = "#token", unless="#result==null",cacheManager = "redisCacheManager")
    public String setQueue(String token, String mlcObj){
        return mlcObj;
    }

    @Cacheable(value = "PAYMENT_HOLD", key = "#customKey", unless="#result==null",cacheManager = "redisCacheManager")
    public String getPaymentHoldOnKey(String customKey, Map<String,String> value){
        LOGGER.info("Adding payment hold  cache in redis for key: {}.", customKey);
        return gson.toJson(value);
    }

    @Cacheable(value = "PAYMENT_HOLD_CLOSE", key = "#customKey", unless="#result==null",cacheManager = "redisCacheManager")
    public String getPaymentHoldCloseOnKey(String customKey,Map<String,String> value){
        LOGGER.info("updating payment hold close cache in redis for key: {}.", customKey);
        return gson.toJson(value);
    }

    @CacheEvict(value="PAYMENT_HOLD",key = "#customKey",cacheManager = "redisCacheManager")
    public void evictPaymentHold( String customKey) {
        LOGGER.info("Clearing payment hold cache from redis for key: {}.",  customKey);
    }

    @CacheEvict(value="BUSINESS_PROOF",key = "#customKey",cacheManager = "redisCacheManager")
    public void evictBusinessProof( String customKey) {
        LOGGER.info("Clearing Business Proof cache from redis for key: {}.",  customKey);
    }

    @Cacheable(value = "BUSINESS_PROOF", key = "#customKey", unless="#result==null",cacheManager = "redisCacheManager")
    public String setBusinessProofFlag(String customKey,String value){
        LOGGER.info("updating business proof cache in redis for key: {}, Value : {}.", customKey,value);
        return value;
    }
    @Cacheable(value = "BUSINESS_PROOF_ACTIVE", key = "#customKey", unless="#result==null",cacheManager = "redisCacheManager")
    public String setBusinessProofFlagActive(String customKey,String value){
        LOGGER.info("updating active business proof cache in redis for key: {}, Value : {}.", customKey,value);
        return value;
    }
    @Cacheable(value = "TWS_SUBSCRIPTION_ACTIVE", key = "#customKey", unless="#result==null",cacheManager = "redisCacheManager")
    public String setTWSflag(String customKey,String value){
        LOGGER.info("updating TWS subscription flag cache in redis for key: {}, Value : {}.", customKey,value);
        return value;
    }

    @CacheEvict(value="P4B_NUDGES",key = "#customKey", cacheManager = "redisCacheManager")
    public void evictNudges(String customKey, String mid) {
        LOGGER.info("Clearing Nudges cache from redis for key: {}.", mid);
    }

    @Cacheable(value="devicePreferences",key="#redisKey", unless="#result==null",cacheManager = "redisCacheManager")
    public String fetchDevicePreference(String mid,String redisKey) throws JsonProcessingException {
        //LOGGER.info("inside fetchDevicePreference for key: {}",redisKey);
        Map<String, Boolean> devicePreferences = upsService.getDevicePreferences(mid);
        String devicePreferencesString = objectMapper.writeValueAsString(devicePreferences);
        return devicePreferencesString;
    }

    public String generateRedisKey(Object... params) {
        if (params == null) {
            throw new IllegalArgumentException("Parameters cannot be null");
        }
        try {
            return org.springframework.util.StringUtils.arrayToDelimitedString(params, "|");
        } catch (Exception e) {
            throw new RuntimeException("Error while generating Redis key: " + e.getMessage(), e);
        }
    }


}
