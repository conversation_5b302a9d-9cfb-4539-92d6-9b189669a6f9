package com.paytm.aggregatorgateway.service.helper;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.UPSIntegrationConstants;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.UUID;

@Component
public class UPSServiceHelper {

    private static Logger LOGGER = LogManager.getLogger(UPSServiceHelper.class);

    @Autowired
    Environment environment;


    public String generateHash(String data) throws UMPIntegrationException {
        String hashedData = null;
        if (StringUtils.isNotBlank(data)) {
            MessageDigest digest;
            try {
                digest = MessageDigest.getInstance("SHA-256");
                byte[] encodedHash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
                hashedData = bytesToHex(encodedHash);
            } catch (Throwable th) {
                LOGGER.info("Error occurred while hashing data", th);
                throw new UMPIntegrationException("Error in generating request Hash");
            }
        }
        return hashedData;
    }

    public  String bytesToHex(byte[] hash) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public String generateJwtToken(String clientId, String jwtTokenSecretKey, HttpMethod httpMethod, String requestBody) throws UMPIntegrationException {
        try {
			JWTCreator.Builder jwtBuilder = JWT.create();
			jwtBuilder.withClaim(UPSIntegrationConstants.TIME_STAMP, String.valueOf(System.currentTimeMillis()));
			jwtBuilder.withClaim(UPSIntegrationConstants.TOKEN_CLIENT_ID, clientId);

			if (httpMethod.equals(HttpMethod.PUT) || httpMethod.equals(HttpMethod.POST))
			    jwtBuilder.withClaim(UPSIntegrationConstants.UPS_REQUEST_HASH, generateHash(requestBody));

			Algorithm algorithm = Algorithm.HMAC256(jwtTokenSecretKey);
			return jwtBuilder.sign(algorithm);
		} catch (RuntimeException e) {
			throw new UMPIntegrationException("error while generating jwt token ~ " + e.getMessage(), e);
		}
    }


    public HttpHeaders getHeaders(HttpMethod httpMethod, String requestBody) throws UMPIntegrationException {
        HttpHeaders headers = new HttpHeaders();
        String token = generateJwtToken
                (AWSSecretManager.awsSecretsMap.get(AWSSecrets.UPS_CLIENT_ID.getValue()),
                        AWSSecretManager.awsSecretsMap.get(AWSSecrets.UPS_SECRET_KEY.getValue()),httpMethod,requestBody);

        headers.add(UPSIntegrationConstants.JWT_TOKEN, token);
        headers.add(UPSIntegrationConstants.CONTENT_TYPE, UPSIntegrationConstants.APPLICATION_JSON);
        headers.add(UPSIntegrationConstants.REQUEST_ID, UUID.randomUUID().toString());//to track the request
        return headers;
    }

}
