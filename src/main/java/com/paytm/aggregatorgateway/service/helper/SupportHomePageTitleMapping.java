package com.paytm.aggregatorgateway.service.helper;

import com.paytm.aggregatorgateway.constants.SupportHomePageConstants;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
public class SupportHomePageTitleMapping extends SupportHomePageConstants {

    public Map<String,String> titleMapping = null;
    @PostConstruct
    public void init()
    {
        titleMapping = new HashMap<>();
        titleMapping.put(P4BSOUNDBOX,SOUND_BOX_RELATED_ISSUE);
        titleMapping.put(P4BPAYOUTANDSETTLEMENT,SETTLEMENT_RELATED_ISSUE);
        titleMapping.put(P4BPROFILE,PROFILE_ACCOUNT_ISSUE);
        titleMapping.put(P4BEDC,CARD_MACHINE_ISSUE);
        titleMapping.put(P4BBUSSINESSLOAN,BUSINESS_LOAN_ISSUE);
        titleMapping.put(P4BACCEPTPAYMENT,PAYMENT_ISSUE);
        titleMapping.put(P4BCASHBACK,CASH_BACK_ISSUE);
        titleMapping.put(SB_GENERAL_QUERY,SOUNDBOX_RENTAL_ISSUE);
        titleMapping.put(SB_RENTAL,SOUNDBOX_RENTAL_ISSUE);
        titleMapping.put(SB_HARDWARE,SOUNDBOX_HARDWARE_ISSUE);
        titleMapping.put(SB_DEACTIVATION,SOUNDBOX_DEACTIVATION_REQUEST);
        titleMapping.put(PAYOUT_SETTLEMENT,SETTLEMENT_RELATED_ISSUE);
        titleMapping.put(MERCHANT_PROFILE,PROFILE_ACCOUNT_ISSUE);
        titleMapping.put(ACCOUNT_TERMINATION,PROFILE_ACCOUNT_ISSUE);
        titleMapping.put(EDC_HARDWARE,CARD_MACHINE_ISSUE);
        titleMapping.put(BUSSINESS_LOAN,BUSINESS_LOAN_ISSUE);
    }
}
