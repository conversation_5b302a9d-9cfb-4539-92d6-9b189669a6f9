package com.paytm.aggregatorgateway.service;

import com.paytm.aggregatorgateway.dto.SmsSubscriptionDTO;
import com.paytm.aggregatorgateway.dto.UpdateSubscriptionStatusDTO;

public interface SmsSubscriptionService {
    public String fetchIdFromCleverTap(String string, String id, String bannerId) throws Exception;
    public String updateSubscriptionStatus(UpdateSubscriptionStatusDTO updateStatusDTO, Boolean retry, String client) throws Exception;

    public String createSubscription(SmsSubscriptionDTO smsSubscriptionDTO) throws Exception;
}
