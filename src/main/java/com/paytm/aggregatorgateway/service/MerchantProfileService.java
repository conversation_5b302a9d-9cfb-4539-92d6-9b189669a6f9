package com.paytm.aggregatorgateway.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.aggregatorgateway.dto.MerchantDetailsVO;
import com.paytm.aggregatorgateway.dto.MerchantInfoDto;
import com.paytm.aggregatorgateway.dto.OutletAndBusinessPreference;
import com.paytm.aggregatorgateway.vo.ResponseUmp;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public interface MerchantProfileService {

    Map<String, Object> getCommunicationConfiguration(String mid, String type, String client) throws Exception;

    Map<String, Object> setCommunicationConfiguration(Map<String, Object> requestBody, String mid, HttpServletRequest httpRequest) throws Exception;

    Map<String,Object> getMerchantPref(String mid, List<String> preferencesList) throws Exception;

    MerchantDetailsVO fetchMerchantDetails() throws Exception;

    void handleSMSEmailAlertsOnBoss(Map<String, Object> updatedStatus, String bossUrl) throws Exception;

    ResponseUmp forceUpdateMerchants(String version, String source, String merchantType, HttpServletResponse httpServletResponse) throws Exception;

    MerchantInfoDto fetchMerchantDetailsByMid(String mid) throws Exception;

    OutletAndBusinessPreference getMerchantOutletAndBusinessPreference(String mid);

    ResponseUmp checkAggEligibility(String mid) throws InterruptedException, JsonProcessingException;

    Map<String, Object> getMerchantPreference(String mid,List<String> preferences,Boolean filterByStatus) throws Exception;

}
