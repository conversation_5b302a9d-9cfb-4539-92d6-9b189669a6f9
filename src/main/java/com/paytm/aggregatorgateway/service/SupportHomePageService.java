package com.paytm.aggregatorgateway.service;

import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.vo.ResponseUmp;

import java.io.IOException;
import java.util.Map;
/**
 * <AUTHOR>
 *
 */
public interface SupportHomePageService {

    String fetchIssueCategory (Map<String,String> requestBody) throws Exception;
    String reopenTicket(String ticketNumber, String description, String mid, Boolean closeTicket) throws Exception;
    Map<String,Object> getAllTickets(boolean categoryWiseTicket) throws Exception;
    Map<String, Object> getRecentTicket() throws Exception;
    String getSurvey() throws Exception;
    String getTicketTimeLine(TicketInfoDTO ticketInfoDTO) throws Exception;

    Object feedbackUpload(Map<String, Object> request) throws Exception;

    ResponseUmp getCallDetails() throws IOException, InterruptedException, UMPIntegrationException;
    ResponseUmp requestCallBack(Boolean edcRented, Boolean sbRented) throws Exception;
}
