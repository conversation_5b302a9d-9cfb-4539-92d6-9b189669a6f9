package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.AppPosService;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

@Service
public class AppPosServiceImpl implements AppPosService {
	private static final Logger LOGGER = LogManager.getLogger(AppPosServiceImpl.class);

	@Autowired
	private Environment environment;

	@Autowired
	private RestProcessorDelegate restProcessorDelegate;

    private static Algorithm bossJwtAlgorithm;
	
	
	private HttpHeaders generateBossHeaders() {
		if (bossJwtAlgorithm == null) {
			String clientKey = AWSSecretManager.awsSecretsMap.get(AWSSecrets.BOSS_CLIENT_KEY.getValue());
			byte[] decodedKey = java.util.Base64.getDecoder().decode(clientKey);
			bossJwtAlgorithm = Algorithm.HMAC512(decodedKey);
		}
		String clientId = AWSSecretManager.awsSecretsMap.get(AWSSecrets.BOSS_CLIENT_ID.getValue());
		String token = JWT.create().withIssuedAt(new Date()).withClaim(PayTmPGConstants.CLIENT_ID, clientId).sign(bossJwtAlgorithm);

		HttpHeaders headers = new HttpHeaders();
		headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
		headers.add(PayTmPGConstants.X_CLIENT_TOKEN, token);
		headers.add(PayTmPGConstants.X_CLIENT_ID, clientId);
		return headers;
	}
	
	@Override
	public String checkStatus(Map<String, String> queryParamMap) throws Exception {

		LOGGER.info("Entered into terminal status with Request: {}", queryParamMap);
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		String deviceId = queryParamMap.get("deviceId");
		String vendorName = queryParamMap.get("vendorName");

		String url = environment.getRequiredProperty(DomainConstants.BOSS_BASE_URL) + "/api/v1/terminal/metadata/" + mid
				+ "/" + deviceId + "/" + vendorName;

		ResponseEntity<String> httpResponse = restProcessorDelegate.executeBOSSRequestHystrix(url,
				HttpMethod.GET.name(), null, generateBossHeaders(), null, String.class);

		return httpResponse.getBody();
	}
}
