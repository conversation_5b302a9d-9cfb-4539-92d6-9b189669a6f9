package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dao.P4bNudgesDao;
import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;
import com.paytm.aggregatorgateway.enums.DeviceErrorType;
import com.paytm.aggregatorgateway.service.DevicesAPIService;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.vo.DeviceHealthInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.*;

@RefreshScope
@Slf4j
@Service
public class DevicesAPIServiceImpl  implements DevicesAPIService {

    @Autowired
    private UPSService upsService;

    @Autowired
    private P4bNudgesDao p4bNudgesDao;

    @Autowired
    private Environment environment;

    @Autowired
    RedisHelper redisHelper;

    @Autowired
    ObjectMapper objectMapper;

    @Value("${sb.deepdischarge.time.limit}")
    private int sbDeepDischargeTimeLimit;

    @Value("${sb.switchoff.time.limit}")
    private int sbSwitchOffTimeLimit;

    @Value("${sb.deepdischarge.enabled:false}")
    private boolean sbDeepdischargeEnabled;

    @Value("${sb.switchOff.enabled:false}")
    private boolean sbSwitchOffEnabled;

    @Value("${sb.switchOff.troubleshoot.enabled:false}")
    private boolean sbSwitchOffTroubleshootEnabled;

    @Override
    public Map<String, Object> getDeviceHealthDetails(String mid) throws Exception {
        log.info("Inside getDeviceHealthDetails with mid: {}", mid);

        Map<String, Object> response = new HashMap<>();
        List<DeviceHealthInfo> deviceList = new ArrayList<>();
        try {
            String redisKey = redisHelper.generateRedisKey("SoundboxOrEDC_CACHE", mid);
            String devicePreferencesString = redisHelper.fetchDevicePreference(mid,redisKey);
            Map<String,Boolean> devicePreferences = objectMapper.readValue(devicePreferencesString,new TypeReference<Map<String, Boolean>>() {});
            boolean edcRented = false, sbRented = false;
            if (devicePreferences.containsKey("ocl.boss.merchant.edc")) {
                edcRented = devicePreferences.get("ocl.boss.merchant.edc");
            }
            if (devicePreferences.containsKey("ocl.iot.merchant.soundbox")) {
                sbRented = devicePreferences.get("ocl.iot.merchant.soundbox");
            }

            response.put("edcRented", edcRented);
            response.put("sbRented", sbRented);

            if(sbRented) {
                List<WidgetInfoDTO> card = p4bNudgesDao.fetchDeviceCard(mid, "CARD", getAllCardType());
                processForSB(deviceList, card);
            }

        }catch (Exception ex){
            log.error("Exception Occurred ", ex);
            response.put("error", true);
        }

        response.put("deviceList", deviceList);
        response.put("showSOS", !deviceList.isEmpty());
        return response;
    }

    private void processForSB(List<DeviceHealthInfo> deviceList, List<WidgetInfoDTO> card) throws Exception{

        for(WidgetInfoDTO widgetInfo : card) {
            if (deviceList.size() == 6) {
                break;
            }

            long differenceInHours = getDifferenceInHours(widgetInfo.getMetadata());
            String path = String.format("/app?redirectUrl=p4b/my-devices/soundbox/details/%s?src=p4b&channel=p4b", widgetInfo.getIdentifierValue());
            String deeplink = "paytmba://business-app/ump-web?url="+ environment.getRequiredProperty(PayTmPGConstants.DEEPLINK_BASE_URL) + path;
            String chatBotDeeplink =environment.getRequiredProperty("ai.chatbot.deeplink") + widgetInfo.getIdentifierValue();

            DeviceHealthInfo deviceHealthInfo = null;
            String featureType = widgetInfo.getFeatureType();

            if(SB_OUT_OF_BATTERY_5.equals(featureType) || SB_OUT_OF_BATTERY_10.equals(featureType)) {

                if(sbDeepdischargeEnabled && differenceInHours > sbDeepDischargeTimeLimit) {
                    // deep discharge case
                    deviceHealthInfo = new DeviceHealthInfo(DeviceErrorType.SB_DEEP_DISCHARGE);
                    deviceHealthInfo.setDeviceCTADeeplink(deeplink);
                }
            }
            else if(SB_MANUAL_SWITCH_OFF.equals(featureType)) {
                if(sbSwitchOffEnabled && differenceInHours < sbSwitchOffTimeLimit){
                    deviceHealthInfo = new DeviceHealthInfo(DeviceErrorType.SB_MANUAL_OFF);
                    deviceHealthInfo.setDeviceCTADeeplink(deeplink);
                }else if(sbSwitchOffTroubleshootEnabled && differenceInHours >= sbSwitchOffTimeLimit){
                    deviceHealthInfo = new DeviceHealthInfo(DeviceErrorType.SB_MANUAL_OFF_OPEN_TROUBLESHOOT);
                    deviceHealthInfo.setDeviceCTADeeplink(chatBotDeeplink);
                }
            }

            if(deviceHealthInfo != null){
                deviceList.add(deviceHealthInfo);
            }
        }

    }

    private List<String> getAllCardType() {

        List<String> cardTypeList = new ArrayList<>();
        cardTypeList.add(SB_OUT_OF_BATTERY_5);
        cardTypeList.add(SB_OUT_OF_BATTERY_10);
        cardTypeList.add(SB_MANUAL_SWITCH_OFF);
        cardTypeList.add(SB_BATTERY_10_AND_CHARGING);
        cardTypeList.add(SB_BATTERY_5_AND_CHARGING);
        cardTypeList.add(SB_CHARGER_CONNECTED_AND_CHARGING);
        cardTypeList.add(SB_CHARGER_CONNECTED_AND_NOT_CHARGING);
        cardTypeList.add(SB_CHARGER_DISCONNECTED);
        cardTypeList.add(SB_CHARGER_DISCONNECTED_MULTIPLE);

        return cardTypeList;
    }

    private long getDifferenceInHours(String metaData) throws Exception{

        Map<String, Object> metaDataMap = MappingUtils.convertJsonToMap(metaData);
        String  currentTimestamp = MapUtils.getString(metaDataMap, "currentTimestamp");
        LocalDateTime lastUpdatedTime = LocalDateTime.parse(currentTimestamp, DateTimeFormatter.ISO_DATE_TIME);

        OffsetDateTime lastUpdatedOffsetTime = lastUpdatedTime.atOffset(ZoneOffset.UTC);
        OffsetDateTime currentTime = OffsetDateTime.now(ZoneOffset.UTC);
        return Duration.between(lastUpdatedOffsetTime, currentTime).toHours();
    }
}
