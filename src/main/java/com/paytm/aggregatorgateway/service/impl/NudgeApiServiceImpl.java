package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dao.P4bNudgesDao;
import com.paytm.aggregatorgateway.dto.MLCLimitRedisVO;
import com.paytm.aggregatorgateway.dto.MLCLimitVo;
import com.paytm.aggregatorgateway.dto.MerchantInfoDto;
import com.paytm.aggregatorgateway.dto.P4bNudges;
import com.paytm.aggregatorgateway.enums.IntegrationErrorCodes;
import com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers;
import com.paytm.aggregatorgateway.enums.Nudges;
import com.paytm.aggregatorgateway.enums.OEStages;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.service.NotificationService;
import com.paytm.aggregatorgateway.service.NudgeApiService;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers.BALANCE;
import static com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers.CC;
import static com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers.DC;
import static com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers.PER_MID;
import static com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers.POSTPAID;
import static com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers.UPI;

@Service
@Slf4j
public class NudgeApiServiceImpl implements NudgeApiService {
    private static final Logger LOGGER = LogManager.getLogger(NudgeApiServiceImpl.class);

    private static final String limitUpgradeCacheName = "LIMIT_UPGRADE";
    private static final String limitUpgradeQueueCacheName = "LIMIT_UPGRADE_QUEUE";

    private static final int monthlyLimitTriggerValue = 85;

    static Gson gson = new GsonBuilder().create();

    @Autowired
    private P4bNudgesDao p4bNudgesDao;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private UPSService upsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    MerchantProfileService merchantProfileService;

    @Value("${" + DomainConstants.GOLDENGATE_BASE_URL + "}")
    private String oeBaseUrl;


    @Override
    public ResponseUmp updateNudge(Map<String, Object> requestBody) throws Exception {
        String mid = null;
        String identifierKey = null;
        String metaData = null;
        String identifierValue = null;
        String status;
        String featureType = null;
        Long custid = null;
        Integer ttl = null;
        String type = null;


        if (!requestBody.containsKey("status"))
            throw new ValidationException(UMPErrorCodeEnums.INVALID_PARAMS);
        else
            status = requestBody.get("status").toString();
        if (!requestBody.containsKey("featureType"))
            throw new ValidationException(UMPErrorCodeEnums.INVALID_PARAMS);
        else
            featureType = requestBody.get("featureType").toString();

        if (!requestBody.containsKey("type"))
            throw new ValidationException(UMPErrorCodeEnums.INVALID_PARAMS);
        else
            type = requestBody.get("type").toString();


        mid = MapUtils.getString(requestBody, "mid");
        identifierKey = MapUtils.getString(requestBody, "identifierKey");
        custid = MapUtils.getLong(requestBody, "custid");
        metaData = MapUtils.getString(requestBody, "metaData");
        identifierValue = MapUtils.getString(requestBody, "identifierValue");
        ttl = MapUtils.getInteger(requestBody, "ttl");
       // type = MapUtils.getString(requestBody, "type");
        Set<String> whiteListedNudgesNames = Arrays.stream(Nudges.values())
                .map(Enum::name)
                .collect(Collectors.toSet());
        if (!whiteListedNudgesNames.contains(featureType)) {
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, "Feature Type is not WhileListed");
        }
        if (StringUtils.isNotBlank(status) && !(PayTmPGConstants.ActiveStatus.equals(status) || PayTmPGConstants.InActiveStatus.equals(status))) {
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, "Invalid status passed");
        }
        if (PayTmPGConstants.ActiveStatus.equals(status) && (ttl == null || ttl == 0)) {
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, "ttl value is missing or is 0 for nudge");
        }


        try {
            if (status.equals(PayTmPGConstants.ActiveStatus)) {
                activateNudge(mid, type, featureType, custid, ttl, status, identifierKey, identifierValue, metaData);
            } else {
                //TODO later
//                deactivateNudge();
            }
            return new ResponseUmp("200", "SUCCESS", "Widget pushed successfully", null);
        }  catch (Exception e) {
            log.error("Error while updating widget e ~~ {}", e.getMessage());
            throw new ResponseUmpException("FAILURE", "400", "Error while updating widget", null);
        }
    }

    private void activateNudge(String mid, String type, String featureType, Long custid, Integer ttl, String status, String identifierKey, String identifierValue, String metaData) throws Exception {
        Nudges nudge = Nudges.valueOf(featureType);
        try {
            switch (nudge) {
                case LIMIT_UPGRADE:
                    activateLimitUpgradeNudge(mid, type, featureType, custid, ttl,
                            status, identifierKey, identifierValue, metaData);
            }
        } catch (Exception ex) {
            log.error("Error while activating nudge  {}", ex.getMessage());
            throw ex;
        }
    }

    private String getLimitUpgradeKey(String cacheName, String mid) {
        return mid + "||" + cacheName;
    }

    private void activateLimitUpgradeNudge(String mid, String type, String featureType, Long custid, Integer ttl, String status, String identifierKey, String identifierValue, String metaData) throws Exception {
        try {
            log.info("activating nudge for LIMIT_UPGRADE");
            MLCLimitVo metadataObj = gson.fromJson(metaData, MLCLimitVo.class);
            boolean edcRented = false;
            if(metadataObj.getRequestPayload().getTriggerValueMonthly()>85){
                String redisKey = redisHelper.generateRedisKey("SoundboxOrEDC_CACHE", mid);
                String storeFrontResponseString = redisHelper.fetchDevicePreference(mid,redisKey);
                Map<String,Boolean> storeFrontResponse = objectMapper.readValue(storeFrontResponseString,new TypeReference<Map<String, Boolean>>() {});
                if (storeFrontResponse.containsKey("ocl.boss.merchant.edc")) {
                    edcRented = storeFrontResponse.get("ocl.boss.merchant.edc");
                }
                log.info("limit breach: {}, edc active: {} ",metadataObj.getRequestPayload().getTriggerValueMonthly(),edcRented);
                if(edcRented) {
                    addOrUpdateNudgesInDB(mid, type, featureType, custid, ttl,
                            status, identifierKey, identifierValue, metaData);
                }
            }
            log.info("notification eligible to send/queue for  mid: {}", mid);
            String redisGetLastNotificationSentOn = redisHelper.getLastNotificationSentOn(getLimitUpgradeKey(limitUpgradeCacheName, mid));
            LocalDateTime lastNotificationSentAt = redisGetLastNotificationSentOn != null ? LocalDateTime.parse(redisGetLastNotificationSentOn) : null;
            log.info("lastNotificationSentAt: {}", lastNotificationSentAt);
            Boolean sendNotification = false;
            if (lastNotificationSentAt == null) {
                sendNotification = true;
            } else {
                if (lastNotificationSentAt.isBefore(LocalDate.now().atStartOfDay())) {
                    sendNotification = true;
                }
            }

            if (sendNotification) {
                log.info("sending notification for mid: {}", mid);
                redisHelper.evictLastSentOnKey(getLimitUpgradeKey(limitUpgradeCacheName, mid));
                MLCLimitVo notificationToSend = getPrioritizedNotification(mid, metaData);
                log.info("notificationToSend Identifier: {}", notificationToSend.getIdentifier());

                MerchantInfoDto merchantDetails = merchantProfileService.fetchMerchantDetailsByMid(mid);

                if (merchantDetails == null || StringUtils.isBlank(merchantDetails.getMerchantName()) || StringUtils.isBlank(merchantDetails.getPrimaryMobileNumber())) {
                    log.error("PrimaryMobileNumber or merchantName not there for mid: {}", mid);
                    throw new UMPIntegrationException("PrimaryMobileNumber or merchantName not there", IntegrationErrorCodes.MERCHANT_INFO_DATA_MISSING);
                }

                String featureType_notification = getCardType(notificationToSend, featureType);

                notificationService.notifyPush(mid, notificationToSend.getIdentifier(), featureType_notification);
                notificationService.notifyWhatsappPush(mid, featureType_notification, merchantDetails, notificationToSend.getIdentifier());
                redisHelper.setLastNotificationSentOn(getLimitUpgradeKey(limitUpgradeCacheName, mid));
                log.info("notification sent for mid: {}", mid);
            } else {
                log.info("queuing notification for mid: {}", mid);
                MLCLimitVo notificationToQueue = getPrioritizedNotification(mid, metaData);
                log.info("notificationToQueue Identifier: {}", notificationToQueue.getIdentifier());
                redisHelper.setQueue(getLimitUpgradeKey(limitUpgradeQueueCacheName, mid), gson.toJson(new MLCLimitRedisVO(notificationToQueue, LocalDateTime.now())));
                log.info("notification queued for mid: {}", mid);
            }


        } catch (Exception e) {
            if (e instanceof UMPIntegrationException && ((UMPIntegrationException) e).getErrorCode().equals("BFF-424")) {
                log.error("Whatsapp notification failed");
            } else {
                throw e;
            }
        }
    }

    private String getCardType(MLCLimitVo mlcLimitVo, String featureType) {
        if(mlcLimitVo.getRequestPayload().getTriggerValueMonthly()>85){
            if(!mlcLimitVo.getIdentifier().equals("PER_MID")){
                featureType = featureType+"_INSTRUMENT";
            }
            featureType = featureType + "_85";
        }
        return featureType;
    }


    private MLCLimitVo getPrioritizedNotification(String mid, String metaData) {
        String queuedNotification = redisHelper.getQueuedNotification(getLimitUpgradeKey(limitUpgradeQueueCacheName, mid));
        log.info("queuedNotification: {}", queuedNotification);

        MLCLimitRedisVO queuedNotifObj = null;
        if (queuedNotification != null) {
            queuedNotifObj = gson.fromJson(queuedNotification, MLCLimitRedisVO.class);
            log.info("identifier of queued notification: {}", queuedNotifObj.getNotification().getIdentifier());
        }
        MLCLimitVo currentNotification = gson.fromJson(metaData, MLCLimitVo.class);
        log.info("identifier of current notification: {}", currentNotification.getIdentifier());

        if (queuedNotifObj != null) {
            redisHelper.evictQueue(getLimitUpgradeKey(limitUpgradeQueueCacheName, mid));
            if (queuedNotifObj.getReceivedAt().plusHours(24).isBefore(LocalDateTime.now())) {
                return currentNotification;
            } else {
                Double queuedTriggerValue = queuedNotifObj.getNotification()
                        .getRequestPayload().getTriggerValueMonthly();
                Double currentTriggerValue = currentNotification.getRequestPayload().getTriggerValueMonthly();

                if ((queuedTriggerValue >= monthlyLimitTriggerValue && currentTriggerValue >= monthlyLimitTriggerValue)
                        || (queuedTriggerValue < monthlyLimitTriggerValue && currentTriggerValue < monthlyLimitTriggerValue)) {
                    return getHighPriorityNotification(queuedNotifObj.getNotification(), currentNotification);
                } else if (queuedTriggerValue >= monthlyLimitTriggerValue && currentTriggerValue < monthlyLimitTriggerValue) {
                    return queuedNotifObj.getNotification();
                } else if (queuedTriggerValue < monthlyLimitTriggerValue && currentTriggerValue >= monthlyLimitTriggerValue) {
                    return currentNotification;
                } else {
                    return currentNotification;
                }
            }
        } else {
            return currentNotification;
        }
    }

    private MLCLimitVo getHighPriorityNotification(MLCLimitVo queuedNotification, MLCLimitVo currentNotification) {
        MLCLimitVo result = null;
        LimitUpgradeIdentifiers queuedIdentifier = LimitUpgradeIdentifiers.valueOf(queuedNotification.getIdentifier());
        String currentIdentifier = currentNotification.getIdentifier();
        switch (queuedIdentifier) {
            case PER_MID:
                result = queuedNotification;
                break;
            case UPI:
                result = currentIdentifier.equals(PER_MID.name()) ? currentNotification : queuedNotification;
                break;
            case CC:
                result = Arrays.asList(PER_MID.name(), UPI.name()).contains(currentIdentifier) ? currentNotification : queuedNotification;
                break;
            case DC:
                result = Arrays.asList(PER_MID.name(), UPI.name(), CC.name()).contains(currentIdentifier) ? currentNotification : queuedNotification;
                break;
            case BALANCE:
                result = Arrays.asList(PER_MID.name(), UPI.name(), CC.name(), DC.name()).contains(currentIdentifier) ? currentNotification : queuedNotification;
                break;
            case POSTPAID:
                result = Arrays.asList(PER_MID.name(), UPI.name(), CC.name(), DC.name(), BALANCE.name()).contains(currentIdentifier) ? currentNotification : queuedNotification;
                break;
            default:
                result = Arrays.asList(PER_MID.name(), UPI.name(), CC.name(), DC.name(), BALANCE.name(), POSTPAID.name()).contains(currentIdentifier) ? currentNotification : queuedNotification;
                break;
        }
        return result;
    }

    private void addOrUpdateNudgesInDB(String mid, String type, String featureType, Long custid, Integer ttl, String status, String identifierKey, String identifierValue, String metaData) {
        log.info("adding or updating nudges info in DB");
        P4bNudges p4bNudge = p4bNudgesDao.getP4BNudge(mid, type, featureType);
        LocalDateTime expiryTime = Instant.ofEpochMilli(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(ttl))
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        if (p4bNudge == null) {
            addNudge(mid, type, featureType, custid, ttl,
                    status, identifierKey, identifierValue, metaData, expiryTime);
            log.info("adding nudge info in DB");
        } else {
            updateNudgeExpiryTime(expiryTime, p4bNudge.getId());
            log.info("updating nudge info in DB");
        }
    }

    private void updateNudgeExpiryTime(LocalDateTime expiryTime, long id) {
        log.info("updating nudge expiryTime {}", expiryTime);
        try {
            p4bNudgesDao.updateP4BNudgeExpiry(id, expiryTime);
        } catch (Exception e) {
            log.error("Error while updating nudge e {}", e.getMessage());
            throw e;
        }
    }

    private void addNudge(String mid, String type, String featureType, Long custid, Integer ttl, String status, String identifierKey, String identifierValue, String metaData, LocalDateTime expiryTime) {
        log.info("inside add Widget");
        try {
            p4bNudgesDao.addP4BNudge(mid, type, featureType, custid, ttl,
                    status, identifierKey, identifierValue, metaData, expiryTime);
        } catch (Exception e) {
            log.error("Error while adding widget to DB");
            throw e;
        }
    }

    // This check is being removed afte the confirmation of product team.
    /*private boolean getEligibilityForNotification(String mid) throws Exception {
        boolean response = false;
        String url = String.format("%s/MerchantService/v1/sdMerchant/lead", oeBaseUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.add("sso_token", SecurityUtils.getLoggedInUser().getPaytmSSOToken());
        headers.add("Content-Type", "application/json");

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("solution", "diy_mco");
        queryParams.put("channel", "DIY_P4B_APP");
        queryParams.put("fetchLeadStatusOnly", "true");
        queryParams.put("mids", mid);

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeOERequestHystrix(url,
                HttpMethod.GET.name(), queryParams, headers, null, String.class);


        if (httpResponse.getStatusCode() == HttpStatus.OK) {
            String responseData = httpResponse.getBody();
            if (StringUtils.isNotBlank(responseData)) {
                LOGGER.info("Successfully fetched response of /MerchantService/v1/sdMerchant/lead from OE,response is :{}", responseData);
                Map<String, Object> responseMap = MappingUtils.convertJsonToType(httpResponse.getBody(), Map.class);
                if (responseMap.containsKey("stage")) {
                    String stageValue = (String) responseMap.get("stage");
                    if (stageValue.equals(OEStages.LEAD_CREATED.name()) ||
                            stageValue.equals(OEStages.LEAD_NOT_PRESENT.name())) {
                        response = true;
                    }
                } else {
                    LOGGER.error("Error Occurred in /MerchantService/v1/sdMerchant/lead from OE :{}", httpResponse);
                    throw new RuntimeException("Something went wrong.");
                }
            }

        } else {
            LOGGER.error("Error Occurred:{}", httpResponse);
            throw new RuntimeException("Something went wrong.");
        }
        return response;
    }*/


}
