package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.dto.TicketDTO;
import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.dto.WidgetInfoDTO;
import com.paytm.aggregatorgateway.enums.NudgeFeatures;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.FsmService;
import com.paytm.aggregatorgateway.service.NotificationService;
import com.paytm.aggregatorgateway.service.WidgetApiService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.ActiveStatus;
import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.InActiveStatus;
import static com.paytm.aggregatorgateway.enums.NudgeFeatures.PAYMENT_HOLD;


@RefreshScope
@Service
@Slf4j
public class WidgetApiServiceImpl implements WidgetApiService {

    @Autowired
    HomepageWidgetDao homepageWidgetDao;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private FsmService fsmService;


    @Value("#{'${edc10To20.allowed.last.digits}'.split(',')}")
    private List<String> allowedLastDigitsForCM10TO20;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisHelper redisHelper;

    @Value("${payment.hold.enable}")
    private Boolean isPaymentHoldEnabled;

    @Value("${refresh.scope.testing}")
    private String refreshScopeFlag;


    @Value("${notify.outOfBattery10}")
    private boolean notifyForOutOfBattery10;

    @Value("${notify.lowBattery20}")
    private boolean notifyForLowBattery20;

    @Value("${notify.manualSwitchOff}")
    private boolean notifyForManualSwitchOff;

    @Value("${success.settlement.card.ttl}")
    private int successSettlementCardFullTTL;

    @Value("${success.settlement.card.partial.ttl}")
    private int successSettlementCardPartialTTL;
    static Gson gson = new GsonBuilder().create();
    public static final String docsRequired = "docsrequired";
    public static final String underReview = "underReview";
    public static final String docsRequiredAddition = "docsRequiredAddition";
    public static final String closedSuccess = "closedSuccess";
    public static final String closedFailure = "closedFailure";
    public static final String closedNoResponse = "closedNoResponse";
    private static final String offlineRiskMonitoring = "offline risk monitoring";
    private static final String transactionInvestigation = "transaction investigation";
    private static final String paymentToBeHeld = "payment to be held";
    private static final String paymentHeld = "payment held";
    private static final String detailsPendingFromMerchant = "details pending from merchant";
    private static final String payoutHoldDocumentsNotReceived = "payout hold- documents not received";
    private static final String merchantResponseReceived = "merchant response received";
    private static final String pendingForResolution = "pending for resolution";
    private static final String rejected = "additional document required";
    private static final String resolved = "resolved";
    private static final String closed = "closed";
    private static final String duplicate = "duplicate";
    private static final String payoutReleased = "payout released";
    private static final String payoutNotReleased = "payout not released";
    private static final String PAYMENT_HOLD_STR = "PAYMENT_HOLD";
    private final int EDC_ORDER_DELIVERED_TTL = 4320;

    @Value("#{'${unused.feature.card}'.split(',')}")
    private List<String> unusedFeatureCard;

    @Override
    public ResponseUmp updateWidget(Map<String, Object> requestBody) throws Exception {

        String mid = null, identifierKey = null, metaData = null, identifierValue = null;
        String status, featureType, type;
        Long custid = null;
        Integer ttl = null;


        if (!requestBody.containsKey("status"))
            throw new ValidationException(UMPErrorCodeEnums.INVALID_PARAMS);
        else
            status = requestBody.get("status").toString();

        if (!requestBody.containsKey("cardType") && !requestBody.containsKey("featureType"))
            throw new ValidationException(UMPErrorCodeEnums.INVALID_PARAMS);
        if (requestBody.containsKey("cardType"))
            featureType = requestBody.get("cardType").toString();
        else
            featureType = MapUtils.getString(requestBody, "featureType");

        mid = MapUtils.getString(requestBody, "mid");
        identifierKey = MapUtils.getString(requestBody, "identifierKey");
        custid = MapUtils.getLong(requestBody, "custid");
        if(featureType.equals(PAYMENT_HOLD.name()))
            metaData = gson.toJson(requestBody.get("metaData"));
        else
            metaData = MapUtils.getString(requestBody, "metaData");

        identifierValue = MapUtils.getString(requestBody, "identifierValue");
        ttl = MapUtils.getInteger(requestBody, "ttl");
        type = MapUtils.getString(requestBody, "type", "CARD");
        Set<String> whiteListedFeaturesNames = Arrays.stream(NudgeFeatures.values())
                .map(Enum::name)
                .collect(Collectors.toSet());

        if (!whiteListedFeaturesNames.contains(featureType)) {
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, String.format("%s is not whiteListed", type));
        }
        if (StringUtils.isNotBlank(status) && !(ActiveStatus.equals(status) || InActiveStatus.equals(status))) {
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, "Invalid status passed");
        }
        if (ActiveStatus.equals(status) && (ttl == null || ttl == 0)) {
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, "ttl value is missing or is 0 for activating widget");
        }

        try {
            if (status.equals(ActiveStatus)) {
                activateWidget(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            } else {
                String message = "";
                deactivateWidget(mid, identifierValue, custid, featureType, type, message,metaData);
                if(StringUtils.isNotBlank(message)) {
                    return new ResponseUmp("200", "SUCCESS", message, null);
                }
            }
            return new ResponseUmp("200", "SUCCESS", "Widget pushed successfully", null);
        } catch (ResponseUmpException e) {
            if (e.getResponse() != null && ("201").equals(e.getResponse().getStatusCode())) {
                return new ResponseUmp("201", "SUCCESS", "Card is not eligible to push", null);
            } else
                throw e;
        } catch (Exception e) {
            log.error("Error while updating widget e ~~ {}", e.getMessage());
            throw new ResponseUmpException("FAILURE", "400", "Error while updating widget", null);
        }

    }

    private void deactivateWidget(String mid, String identifierValue, Long custId, String featureType, String type, String message, String metaData) throws Exception {
        log.info("inside deactivateWidget impl for featureType {}, identifierValue {}, mid {}, custId {}",
                featureType, identifierValue, mid, custId);

        //remove this check once we've started pushing CM_SIM_NETWORK_25 event into our db
        if(PayTmPGConstants.CM_SIM_NETWORK_25.equalsIgnoreCase(featureType)) {
            return;
        }
        if(PayTmPGConstants.CM_BATTERY_10TO20.equalsIgnoreCase(featureType) && !(allowedLastDigitsForCM10TO20.contains(mid.substring(mid.length() - 1)))){
            return;
        }
        List<WidgetInfoDTO> activeWidgetList = homepageWidgetDao.fetchWidgetDetails(mid, identifierValue, custId, featureType, ActiveStatus, type);
        log.info("No. of active cards fetched " + activeWidgetList.size());

        if (activeWidgetList.size() == 0) {
            log.info("No Active " + type + " exists so INACTIVE " + type + " can't be pushed");
            message = " No Active " + type + " exists so INACTIVE " + type + "can't be pushed";
            return;
        }
        Date expiryTime = new Date(System.currentTimeMillis());
        if(StringUtils.equals(featureType, NudgeFeatures.SETTLEMENT_STATUS_UPDATE_CARD.name())) {
            String cardTimeToShow = showSuccessCard(metaData);
            if(cardTimeToShow.equals("FULL")){
                activateWidget(mid, "SETTLEMENT_BILL_ID", custId, NudgeFeatures.SETTLEMENT_SUCCESS_UPDATE_CARD.name(), metaData, identifierValue, successSettlementCardFullTTL, type);
            }
            if(cardTimeToShow.equals("PARTIAL")){
                activateWidget(mid, "SETTLEMENT_BILL_ID", custId, NudgeFeatures.SETTLEMENT_SUCCESS_UPDATE_CARD.name(), metaData, identifierValue, successSettlementCardPartialTTL, type);
            }
        }

        updateWidgetStatusAndExpiryTime(mid, identifierValue, custId, featureType, InActiveStatus, ActiveStatus, expiryTime, type, null);

        if(StringUtils.equals(featureType, NudgeFeatures.BUSINESS_PROOF_COLLECTION.name())) {
            log.info("evicting business proof for mid {}", mid);
            updateWidgetStatusAndExpiryTime(mid, identifierValue, custId, featureType, InActiveStatus, ActiveStatus, expiryTime, "FLAG", null);
            redisHelper.evictBusinessProof("BUSSINESS_PROOF" + mid);
            redisHelper.setBusinessProofFlag("BUSSINESS_PROOF" + mid, InActiveStatus);
        }
    }

    private String showSuccessCard(String metaData) throws Exception {
        try{
        JsonNode jsonNode = objectMapper.readTree(metaData);

        String billDateTime = jsonNode.get("billDateTime").asText();

        // Parse the settlementTime string to a ZonedDateTime object
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        LocalDateTime settlementTime = LocalDateTime.parse(billDateTime, formatter);

        // Get the current month and the previous month
        int currentMonth = ZonedDateTime.now().getMonthValue();
        int previousMonth = currentMonth == 1 ? 12 : currentMonth - 1;

        // Compare the month of the settlementTime with the current and previous month
        int settlementMonth = settlementTime.getMonthValue();
        if (settlementMonth == currentMonth || settlementMonth == previousMonth) {
           log.info("The settlement month is the current month or previous month.");
           return "PARTIAL";
        }  else {
            log.info("The settlement month is older than the previous month.");
            return "FULL";
        }}
        catch (Exception e){
            log.error("Exception occurs at showSuccessCard "+e.fillInStackTrace());
            throw e;
        }
    }

    private void updateWidgetStatusAndExpiryTime(String mid, String identifierValue, Long custId, String featureType, String toStatus, String fromStatus, Date expiryTime, String type, String metaData) throws Exception {
        log.info("updating widget status to {}, from status {} and setting expiryTime", toStatus, fromStatus);
        try {
            homepageWidgetDao.updateStatusAndFlag(mid, identifierValue, custId, featureType, toStatus, expiryTime, fromStatus, type, metaData);
        } catch (Exception e) {
            log.error("Error while updating widgets e {}", e.getMessage());
            throw e;
        }
    }

    public void updatePaymentHoldCard(String mid, String identifierValue, String featureType, Date expiryTime, String type, String metadata) throws Exception {
        //log.info("updating widget status to {}, from status {} and setting expiryTime", toStatus, fromStatus);
        try {
            homepageWidgetDao.updatePaymentHoldCard(mid, identifierValue, featureType,expiryTime, type, metadata);
        } catch (Exception e) {
            log.error("Error while updating widgets e {}", e.getMessage());
            throw e;
        }
    }

    private void addWidget(String mid, String identifierKey, Long custid, String featureType, String metaData, String identifierValue, int ttl, String type) throws Exception {
        log.info("inside add Widget");
        try {
            Date cardExpiryTime = new Date(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(ttl));
            homepageWidgetDao.addWidget(mid, identifierKey, custid, ActiveStatus, featureType, metaData, cardExpiryTime, identifierValue, ttl, type);
        } catch (Exception e) {
            log.error("Error while adding widget to DB");
            throw e;
        }

    }

    private void addOrUpdateWidgetInfoInDB(String mid, String identifierKey, Long custid, String featureType, String metaData, String identifierValue, int ttl, String type) throws Exception {
        //log.info("adding or updating widget info in DB");
        if(unusedFeatureCard.contains(featureType)){
            return;
        }
        List<WidgetInfoDTO> activeWidgetList = homepageWidgetDao.fetchWidgetDetails(mid, identifierValue, custid, featureType, ActiveStatus, type);
        log.info("No. of active cards fetched " + activeWidgetList.size());

        boolean isBillCard= false;
        if(StringUtils.equals(featureType, NudgeFeatures.SETTLEMENT_STATUS_UPDATE_CARD.name()))
            isBillCard = true;

        if (isBillCard){
            log.info("already failed mid : {}, identifierValue : {}, metaData : {}",mid,identifierValue,metaData);

            List<WidgetInfoDTO> InactiveWidgetList = homepageWidgetDao.fetchWidgetDetails(mid, identifierValue, custid, featureType, InActiveStatus, type);
            if (InactiveWidgetList.size() > 0)
                return;
        }

        if (activeWidgetList.size() == 0) {
            addWidget(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        } else {
            Date cardExpiryTimeToSet = new Date(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(ttl));
            updateWidgetStatusAndExpiryTime(mid, identifierValue, custid, featureType, ActiveStatus, ActiveStatus, cardExpiryTimeToSet, type, metaData);
        }
    }

    private void activateWidget(String mid, String identifierKey, Long custid, String featureType, String metaData, String identifierValue, Integer ttl, String type) throws Exception {
        NudgeFeatures feature = NudgeFeatures.valueOf(featureType.toUpperCase());
        try {
            switch (feature) {
                case UPDATE_TICKET_ADDRESS:
                    activateUpdateTicketAddress(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_TRANSACTION_BLOCKED:
                    activateCMTxnBlocked(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case EDC_ORDER_DELIVERED:
                    activateEDCOderDelivered(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case AGENT_VISIT_SCHEDULED:
                    activateAgentVisitScheduled(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case DOCUMENT_VERIFICATION_FAILED:
                    activateDocVerificationFailed(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_OUT_OF_PRINTING_PAPER:
                    activateOutOfPrintingPaperEDC(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_OUT_OF_NETWORK:
                    activateOutOfNetworkEDC(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_OUT_OF_BATTERY:
                    activateOutOfBatteryEDC(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_OUT_OF_BATTERY_5:
                    activateOutOfBattery5Sb(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_OUT_OF_BATTERY_10:
                    activateOutOfBattery10Sb(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_SIM_NETWORK_25:
                    activateSimNetwork25EDC(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_WIFI_NETWORK_25:
                    activateWiFiNetwork25EDC(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_BATTERY_10TO15:
                    activateBattery10TO15EDC(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case CM_BATTERY_10TO20:
                    activateBattery10TO20EDC(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case PAYMENT_HOLD:
                    activatePaymentHold(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case EDC_ORDER_PLACED:
                	activateEdcOrderPlaced(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                	break;
                case BUSINESS_PROOF_COLLECTION:
                    addBusinessProofUpdate(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_BATTERY_10_AND_CHARGING:
                    activateBattery10AndChargingForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_BATTERY_5_AND_CHARGING:
                    activateBattery5AndChargingForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_CHARGER_CONNECTED_AND_CHARGING:
                    activateChargerConnectedAndChargingForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_CHARGER_CONNECTED_AND_NOT_CHARGING:
                    activateChargerConnectedAndNotChargingForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_CHARGER_DISCONNECTED:
                    activateChargerDisconnectedForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_CHARGER_DISCONNECTED_MULTIPLE:
                    activateChargerDisconnectedMultipleForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_MANUAL_SWITCH_OFF:
                    activateManualSwitchOffForSb(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case SB_MANUAL_SWITCH_ON:
                    activateManualSwitchOnnForSb(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
                case YBL_CASA:
                    activateYblCASA(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
                    break;
//                case SB_LOW_BATTERY_20:
//                    activateLowBattery20ForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
//                    break;
//                case SB_BATTERY_20_AND_CHARGING:
//                    activateBattery20AndChargingForSB(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
//                   break;
                default:
                    addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            }
        } catch (Exception e) {
            log.error("Error while activating widget e {}", e.getMessage());
            throw e;
        }

    }

    private void addBusinessProofUpdate(String mid, String identifierKey, Long custid, String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for BUSINESS_PROOF_COLLECTION ");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, "FLAG");
        redisHelper.evictBusinessProof("BUSSINESS_PROOF" + mid);
        redisHelper.setBusinessProofFlagActive("BUSSINESS_PROOF" + mid, ActiveStatus);
    }

    private void activateEdcOrderPlaced(String mid, String identifierKey, Long custid, String metaData,
			String identifierValue, Integer ttl, String featureType, String type) throws Exception {

    	Map<String,Object> metaMap = MappingUtils.convertJsonToMap(metaData);
    	String resumeJourneyValue = MapUtils.getString(metaMap, "parentLeadId", null);
        deactivateWidget(mid, resumeJourneyValue, custid, PayTmPGConstants.EDC_RESUME_JOURNEY, type, null, null);

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
	}

	private void activatePaymentHold(String mid, String identifierKey, Long custid, String metadata, String identifierValue, Integer ttl, String featureType, String type) {

        if(!isPaymentHoldEnabled){
            log.info("Payment hold feature is disabled");
            return;
        }
        log.info("activating PaymentHold for featureType {}, identifierValue {}, mid {}, custId {}",
                featureType, identifierValue, mid, custid);
        try {
            String status = null;
            String partialHoldText = "";
//            TicketDTO ticketInfoDTO = objectMapper.readValue(metadata, TicketDTO.class);
            TicketDTO ticketInfoDTO = gson.fromJson(metadata, TicketDTO.class);

            // Check if issue_category conditions are met
            if (checkIssueCategoryConditions(ticketInfoDTO)) {
                log.info("Issue category conditions met for ticketId {}", identifierValue);
                boolean isPartialHold = checkPartialHoldCondition(ticketInfoDTO);

                if (isPartialHold)
                    partialHoldText = "prehold";
                else
                    partialHoldText = "fullhold";
                if (checkStatusConditions(ticketInfoDTO)) {
                    log.info("open status for ticketId {}", identifierValue);
                    if (checkDocsRequiredStatus(ticketInfoDTO))
                        status = docsRequired;
                    else if (checkUnderReviewStatus(ticketInfoDTO))
                        status = underReview;
                    else if (rejected.equals(ticketInfoDTO.getStatus().toLowerCase()))
                        status = docsRequiredAddition;

                    log.info("status for ticketId {}", status);
                    List<WidgetInfoDTO> widgetList = homepageWidgetDao.fetchWidgetDetailsForPaymentHold(mid, identifierValue, featureType);
                    log.info("Total No. of existing cards fetched " + widgetList.size());
                    Set<String> uniqueIdentifierValues = widgetList.stream()
                            .filter(widget -> widget.getIdentifierValue() != null)
                            .filter(widget -> {
                                TicketDTO ticket = gson.fromJson(widget.getMetadata(), TicketDTO.class);
                                String parsedStatus = ticket.getStatus().toLowerCase();
                                return !parsedStatus.equals(resolved) && !parsedStatus.equals(closed) && !parsedStatus.equals(duplicate);
                            })
                            .map(WidgetInfoDTO::getIdentifierValue)
                            .collect(Collectors.toSet());

                    log.info("No. of uniqueIdentifierValues fetched " + uniqueIdentifierValues.size());

                    Boolean isDuplicateFound = false;
                    if (uniqueIdentifierValues.size() == 1 && !uniqueIdentifierValues.contains(identifierValue)) {
                        isDuplicateFound = true;
                    } else if (uniqueIdentifierValues.size() > 1) {
                        isDuplicateFound = true;
                    }

                    log.info("Duplicate found for identifierValue " + isDuplicateFound);

                    List<WidgetInfoDTO> activeWidgetList = widgetList.stream()
                            .filter(widgetInfoDTO -> identifierValue.equals(widgetInfoDTO.getIdentifierValue()))
                            .collect(Collectors.toList());
                    featureType = featureType + "_" + partialHoldText + "_" + status;
                    log.info("No. of activeWidgetList fetched {} , feature type updated {}", activeWidgetList.size(), featureType);
                    if (activeWidgetList.size() == 0) {
                        ttl = 525600;//1 year
                        if (status.equals(docsRequired) || status.equals(docsRequiredAddition)) {
                            addWidget(mid, identifierKey, custid, featureType, metadata, identifierValue, ttl, PayTmPGConstants.BottomSheet);
                            addWidget(mid, identifierKey, custid, featureType, metadata, identifierValue, ttl, PayTmPGConstants.Card);
                            addWidget(mid, identifierKey, custid, featureType, metadata, identifierValue, ttl, PayTmPGConstants.PNS_top);
                        } else if (status.equals(underReview)) {
                            addWidget(mid, identifierKey, custid, featureType, metadata, identifierValue, ttl, PayTmPGConstants.Card);
                            addWidget(mid, identifierKey, custid, featureType, metadata, identifierValue, ttl, PayTmPGConstants.PNS_top);
                        }
                    } else {
                        updatePaymentHoldCard(mid, identifierValue, featureType, null, PayTmPGConstants.Card, metadata);
                        updatePaymentHoldCard(mid, identifierValue, featureType, null, PayTmPGConstants.PNS_top, metadata);
                        if (status.equals(underReview)) {
                            Date cardExpiryTimeToSet = new Date(System.currentTimeMillis());
                            updatePaymentHoldCard(mid, identifierValue, featureType, cardExpiryTimeToSet, PayTmPGConstants.BottomSheet, metadata);
                        } else {
                            if (activeWidgetList.stream().filter(widgetInfoDTO -> widgetInfoDTO.getType().equals(PayTmPGConstants.BottomSheet)).count() == 0) {
                                ttl = 525600;
                                addWidget(mid, identifierKey, custid, featureType, metadata, identifierValue, ttl, PayTmPGConstants.BottomSheet);
                            } else
                                updatePaymentHoldCard(mid, identifierValue, featureType, null, PayTmPGConstants.BottomSheet, metadata);
                        }
                    }
                    //make key on full hold*************** with featuretype,ticket id ttl-1 year
                    if (isDuplicateFound) {
                        long isFullHoldPresent = widgetList.stream()
                                .map(WidgetInfoDTO::getFeatureType)
                                .filter(getFeatureType -> getFeatureType.contains("fullhold")) // Optional: Filter out null values
                                .count();

                        if (!isPartialHold) {
                            isFullHoldPresent++;
                        }

                        log.info("isFullHoldPresent " + isFullHoldPresent);
                        //if full hold present
                        if (isFullHoldPresent >= 1) {
                            redisHelper.evictPaymentHold(getPaymentHoldRedisKey(mid));
                            Map<String, String> redisMap = new HashMap<>();
                            redisMap.put("messageKey", "payment_hold_duplicate");
                            log.info("redisMap " + redisMap);
                            redisHelper.getPaymentHoldOnKey(getPaymentHoldRedisKey(mid), redisMap);
                        } else {
                            redisHelper.evictPaymentHold(getPaymentHoldRedisKey(mid));
                        }
                    } else if (!isPartialHold) {
                        Map<String, String> redisMap = new HashMap<>();
                        redisMap.put("messageKey", featureType);
                        redisMap.put("ticketId", identifierValue);
                        log.info("redisMap " + redisMap);
                        redisHelper.evictPaymentHold(getPaymentHoldRedisKey(mid));
                        redisHelper.getPaymentHoldOnKey(getPaymentHoldRedisKey(mid), redisMap);
                    }
                } else {
                    // Closed Ticket
                    if (checkClosedTicketStatus(ticketInfoDTO)) {
                        // Update as Active and update card TTL-2 days
                        log.info("Closed ticket for ticketId {}", identifierValue);
                        String statusFetch = ticketInfoDTO.getStatus().toLowerCase();

                        String riskComment = ticketInfoDTO.getSub_status();
                        boolean isClose = resolved.equals(statusFetch) || closed.equals(statusFetch) || duplicate.equals(statusFetch);

                        if (isClose && riskComment != null && payoutReleased.equals(riskComment.toLowerCase()))
                            status = closedSuccess;
                        if (isClose && riskComment != null && payoutNotReleased.equals(riskComment.toLowerCase()))
                            status = closedFailure;
                        if (isClose && riskComment != null && riskComment.equals(""))
                            status = closedNoResponse;

                        log.info("status for ticketId {}", status);
                        List<WidgetInfoDTO> activeWidgetList = homepageWidgetDao.fetchWidgetDetailsForPaymentHold(mid, identifierValue, featureType);

                        log.info("No. of active cards fetched " + activeWidgetList.size());
                        featureType = featureType + "_" + partialHoldText + "_" + status;

                        Map<String, TicketDTO> identifierValueToMetadataMap = new HashMap<>();

                        for(WidgetInfoDTO widgetInfoDTO : activeWidgetList){
                            TicketDTO ticketDTO = gson.fromJson(widgetInfoDTO.getMetadata(), TicketDTO.class);
                            if(!ticketDTO.getStatus().toLowerCase().equals(resolved) && !ticketDTO.getStatus().toLowerCase().equals(closed) && !ticketDTO.getStatus().toLowerCase().equals(duplicate)){
                                identifierValueToMetadataMap.put(widgetInfoDTO.getIdentifierValue(), ticketDTO);
                            }
                        }
                        log.info("identifierValueToMetadataMap " + identifierValueToMetadataMap);
                        Set<String> identifierValueSet = identifierValueToMetadataMap.keySet();
                        log.info("identifierValueSet size" + identifierValueSet.size());


                        if(identifierValueSet.contains(identifierValue)){
                            log.info("Updating card for ticketId {}", identifierValue);
                            ttl = 2880; //2 days
                            Date cardExpiryTimeToSet = new Date(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(ttl));
                            updatePaymentHoldCard(mid, identifierValue, featureType, cardExpiryTimeToSet, PayTmPGConstants.Card, metadata);
                            updatePaymentHoldCard(mid, identifierValue, featureType, cardExpiryTimeToSet, PayTmPGConstants.PNS_top, metadata);
                            //disabling bottomsheet
                            updatePaymentHoldCard(mid, identifierValue, featureType, new Date(System.currentTimeMillis()), PayTmPGConstants.BottomSheet, metadata);
                            identifierValueSet.remove(identifierValue);


                            if(identifierValueSet.size() == 0){
                                log.info("identifierValueSet size is 0 ");
                                redisHelper.evictPaymentHold(getPaymentHoldRedisKey(mid));
                                Map<String, String> redisMap = new HashMap<>();
                                redisMap.put("messageKey", featureType);
                                redisMap.put("ticketId", identifierValue);
                                log.info("redisMap " + redisMap);
                                redisHelper.getPaymentHoldCloseOnKey(getPaymentHoldRedisKey(mid), redisMap);
                            }
                            else if (identifierValueSet.size() == 1) {
                                log.info("identifierValueSet size is 1 ");
                                String otherIdentifierValue = identifierValueSet.iterator().next();
                                TicketDTO otherTicketInfoDTO = identifierValueToMetadataMap.get(otherIdentifierValue);
                                boolean isOtherPartialHold = checkPartialHoldCondition(otherTicketInfoDTO);
                                if (isOtherPartialHold)
                                    partialHoldText = "prehold";
                                else
                                    partialHoldText = "fullhold";

                                if (checkDocsRequiredStatus(otherTicketInfoDTO))
                                    status = docsRequired;
                                else if (checkUnderReviewStatus(otherTicketInfoDTO))
                                    status = underReview;
                                else if (rejected.equals(otherTicketInfoDTO.getStatus().toLowerCase()))
                                    status = docsRequiredAddition;

                                log.info("status for other ticket {}", status);
                                featureType = PAYMENT_HOLD_STR + "_" + partialHoldText + "_" + status;

                                redisHelper.evictPaymentHold(getPaymentHoldRedisKey(mid));
                                Map<String, String> redisMap = new HashMap<>();
                                redisMap.put("messageKey", featureType);
                                redisMap.put("ticketId", identifierValue);
                                log.info("redisMap " + redisMap);
                                redisHelper.getPaymentHoldCloseOnKey(getPaymentHoldRedisKey(mid), redisMap);

                            }
                        }
                        else{
                            log.info("Can't close non existing ticket for ticketId {}", identifierValue);
                        }
                    }
                    else{
                        log.info("close criteria didn't match for ticketId {}", identifierValue);
                    }
                }
            } else {
                // Do not push if no issue category matches
                log.info("Issue category conditions not met for ticketId {}", identifierValue);
            }
        } catch (Exception e) {
            log.error("Error while activatePaymentHold {}", e.getMessage());
        }
    }

    private String getPaymentHoldRedisKey(String mid){
        return PAYMENT_HOLD_STR+"|" + mid;
    }

    private boolean checkIssueCategoryConditions(TicketDTO ticketInfoDTO) {
        // Implement based on issue category conditions
        return offlineRiskMonitoring.equals(ticketInfoDTO.getIssue_category_l1().toLowerCase()) &&
                transactionInvestigation.equals(ticketInfoDTO.getIssue_category_l2().toLowerCase()) &&
                (paymentToBeHeld.equals(ticketInfoDTO.getIssue_category_l3().toLowerCase()) ||
                        paymentHeld.equals(ticketInfoDTO.getIssue_category_l3().toLowerCase()));
    }

    private boolean checkStatusConditions(TicketDTO ticketInfoDTO) {
        // Implement based on status conditions
        return detailsPendingFromMerchant.equals(ticketInfoDTO.getStatus().toLowerCase()) ||
                payoutHoldDocumentsNotReceived.equals(ticketInfoDTO.getStatus().toLowerCase()) ||
                merchantResponseReceived.equals(ticketInfoDTO.getStatus().toLowerCase()) ||
                pendingForResolution.equals(ticketInfoDTO.getStatus().toLowerCase()) ||
                rejected.equals(ticketInfoDTO.getStatus().toLowerCase());
    }

    private boolean checkPartialHoldCondition(TicketDTO ticketInfoDTO) {
        if (paymentToBeHeld.equals(ticketInfoDTO.getIssue_category_l3().toLowerCase()))
            return true;
        else
            return false;
    }

    private boolean checkDocsRequiredStatus(TicketDTO ticketInfoDTO) {
        return detailsPendingFromMerchant.equals(ticketInfoDTO.getStatus().toLowerCase()) ||
                payoutHoldDocumentsNotReceived.equals(ticketInfoDTO.getStatus().toLowerCase());
    }

    private boolean checkUnderReviewStatus(TicketDTO ticketInfoDTO) {
        return merchantResponseReceived.equals(ticketInfoDTO.getStatus().toLowerCase()) ||
                pendingForResolution.equals(ticketInfoDTO.getStatus().toLowerCase());
    }
    private boolean checkClosedTicketStatus(TicketDTO ticketInfoDTO) {
        String status = ticketInfoDTO.getStatus().toLowerCase();
        String riskComment = ticketInfoDTO.getSub_status();
        boolean isClosed = resolved.equals(status) || closed.equals(status) || duplicate.equals(status);

        return (isClosed && riskComment != null && payoutReleased.equals(riskComment.toLowerCase())) ||
                (isClosed && riskComment != null && payoutNotReleased.equals(riskComment.toLowerCase())) ||
                (isClosed && riskComment != null && riskComment.equals(""));
    }

    private static void updateClosedTicket(TicketInfoDTO ticketInfoDTO) {
        // Implement logic to update closed ticket
        // Example: ticketInfoDTO.setStatus("UpdatedClosedStatus");
        System.out.println("Updated closed ticket: " + ticketInfoDTO);
    }

    private void activateUpdateTicketAddress(String mid, String identifierKey, Long custid,
                                             String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for UPDATE_TICKET_ADDRESS");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        notificationService.notifyPush(mid, identifierValue, featureType);
    }

    private void activateCMTxnBlocked(String mid, String identifierKey, Long custid,
                                      String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {

        log.info("activating widget for CM_TRANSACTION_BLOCKED ");
        Map<String, Object> openBeats = fsmService.getRelevantOpenBeat(mid, identifierValue);
        if (openBeats != null)
            throw new ResponseUmpException("Failure", "201", "Not Eligible", null);
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        notificationService.notifyPush(mid, identifierValue, featureType);
    }

    private void activateEDCOderDelivered(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for  EDC_ORDER_DELIVERED ");
        ttl = EDC_ORDER_DELIVERED_TTL;
        activateCurrDeactivateOthers(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
    }

    private void activateAgentVisitScheduled(String mid, String identifierKey, Long custid,
                                             String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for AGENT_VISIT_SCHEDULED ");
        activateCurrDeactivateOthers(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
    }

    private void activateDocVerificationFailed(String mid, String identifierKey, Long custid,
                                               String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for DOCUMENT_VERIFICATION_FAILED ");
        activateCurrDeactivateOthers(mid, identifierKey, custid, metaData, identifierValue, ttl, featureType, type);
    }

    private void activateOutOfPrintingPaperEDC(String mid, String identifierKey, Long custid, String metaData, String identifierValue,
                                               Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for CM_OUT_OF_PRINTING_PAPER");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        notificationService.notifyPush(mid, identifierValue, featureType);
    }

    private void activateOutOfNetworkEDC(String mid, String identifierKey, Long custid, String metaData, String identifierValue,
                                         Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for CM_OUT_OF_NETWORK");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        notificationService.notifyPush(mid, identifierValue, featureType);
    }

    private void activateOutOfBatteryEDC(String mid, String identifierKey, Long custid, String metaData, String identifierValue,
                                         Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for CM_OUT_OF_BATTERY ");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        notificationService.notifyPush(mid, identifierValue, featureType);
    }

    private boolean isEventForNewVersion(String metadata) throws Exception {

        if(StringUtils.isNotBlank(metadata)){
            Map<String, Object> metaDataMap = MappingUtils.convertJsonToMap(metadata);
            String newVersion = MapUtils.getString(metaDataMap, PayTmPGConstants.NEW_VERSION);
            return "true".equals(newVersion);
        }
        return false;
    }

    private void activateOutOfBattery5Sb(String mid, String identifierKey, Long custid,
                                         String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_OUT_OF_BATTERY_5");

        if(isEventForNewVersion(metaData)) {
            addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            markInactiveEvents(mid, identifierValue, custid, PayTmPGConstants.SB_BATTERY_5_AND_CHARGING, type);
        }

        notificationService.notifyPush(mid, identifierValue, featureType);

    }

    private void activateOutOfBattery10Sb(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_OUT_OF_BATTERY_10");
        if(isEventForNewVersion(metaData)) {
            addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            markInactiveEvents(mid, identifierValue, custid, PayTmPGConstants.SB_BATTERY_10_AND_CHARGING, type);
        }
        if(notifyForOutOfBattery10) {
            notificationService.notifyPush(mid, identifierValue, featureType);
        }

    }

    private void activateSimNetwork25EDC(String mid, String identifierKey, Long custid,
                                         String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for CM_SIM_NETWORK_25");
        //Remove the checks in deactivateWidget method for the cardType CM_SIM_NETWORK_25 once we have started pushing this event into our db.
    }

    private void activateWiFiNetwork25EDC(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for CM_WIFI_NETWORK_25");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
    }

    private void activateBattery10TO15EDC(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for CM_BATTERY_10TO15");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
    }

    private void activateBattery10TO20EDC(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for CM_BATTERY_10TO20");
        if(allowedLastDigitsForCM10TO20.contains(mid.substring(mid.length() - 1))) {
            addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            notificationService.notifyPush(mid, identifierValue, featureType);
        }
    }

    private void activateCurrDeactivateOthers(String mid, String identifierKey, Long custid,
                                              String metaData, String identifierValue, Integer ttl, String currFeature, String type) throws Exception {
        Date expiryTime = new Date(System.currentTimeMillis());
        homepageWidgetDao.setCardInactive(mid, identifierValue, custid, InActiveStatus, expiryTime, ActiveStatus);
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, currFeature, metaData, identifierValue, ttl, type);

    }

    private void activateBattery10AndChargingForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_BATTERY_10_AND_CHARGING");

        if(isBatteryMoreThan(metaData, 10)) {
            homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, getAllCardType());
            addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            return;
        }
        
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        List<String> featureTypeList = new ArrayList<>();
        featureTypeList.add(PayTmPGConstants.SB_OUT_OF_BATTERY_10);
        featureTypeList.add(PayTmPGConstants.SB_OUT_OF_BATTERY_5);
        featureTypeList.add(PayTmPGConstants.SB_BATTERY_5_AND_CHARGING);
        homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, featureTypeList);
    }

	private void activateBattery5AndChargingForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_BATTERY_5_AND_CHARGING");

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        markInactiveEvents(mid, identifierValue, custid, PayTmPGConstants.SB_OUT_OF_BATTERY_5, type);
    }

    private void activateChargerConnectedAndChargingForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_CHARGER_CONNECTED_AND_CHARGING");

        //doubt to confirm this
        if(isBatteryMoreThan(metaData, 10)){
            homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, getAllCardType());
            addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            return;
        }
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        List<String> featureTypeList = new ArrayList<>();
        featureTypeList.add(PayTmPGConstants.SB_CHARGER_DISCONNECTED);
        featureTypeList.add(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_NOT_CHARGING);
        featureTypeList.add(PayTmPGConstants.SB_CHARGER_DISCONNECTED_MULTIPLE);
        homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, featureTypeList);
    }

    private void activateChargerConnectedAndNotChargingForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_CHARGER_CONNECTED_AND_NOT_CHARGING");

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        markInactiveEvents(mid, identifierValue, custid, PayTmPGConstants.SB_CHARGER_CONNECTED_AND_CHARGING, type);
        notificationService.notifyPush(mid, identifierValue, featureType);
    }

    private void activateChargerDisconnectedForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_CHARGER_DISCONNECTED");

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        List<String> featureTypeList = new ArrayList<>();
        featureTypeList.add(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_CHARGING);
        featureTypeList.add(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_NOT_CHARGING);
        homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, featureTypeList);
    }

    private void activateChargerDisconnectedMultipleForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_CHARGER_DISCONNECTED_MULTIPLE");

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        List<String> featureTypeList = new ArrayList<>();
        featureTypeList.add(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_CHARGING);
        featureTypeList.add(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_NOT_CHARGING);
        homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, featureTypeList);
        notificationService.notifyPush(mid, identifierValue, featureType);
    }

    private void activateManualSwitchOffForSb(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_MANUAL_SWITCH_OFF");

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        if(notifyForManualSwitchOff) {
            notificationService.notifyPush(mid, identifierValue, featureType);
        }
        markInactiveEvents(mid, identifierValue, custid, PayTmPGConstants.SB_MANUAL_SWITCH_ON, type);
    }

    private void activateManualSwitchOnnForSb(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_MANUAL_SWITCH_ON");

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        markInactiveEvents(mid, identifierValue, custid, PayTmPGConstants.SB_MANUAL_SWITCH_OFF, type);
    }

    private void activateYblCASA(String mid, String identifierKey, Long custid, String metaData, String identifierValue, Integer ttl, String featureType,
                                 String type) throws Exception {
        log.info("activating widget for YBL_CASA");
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
    }

    /*private void activateLowBattery20ForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_LOW_BATTERY_20");

        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        markInactiveEvents(mid, identifierValue, custid, PayTmPGConstants.SB_BATTERY_20_AND_CHARGING, type);
        if(notifyForLowBattery20) {
            notificationService.notifyPush(mid, identifierValue, featureType);
        }
    }

    private void activateBattery20AndChargingForSB(String mid, String identifierKey, Long custid,
                                          String metaData, String identifierValue, Integer ttl, String featureType, String type) throws Exception {
        log.info("activating widget for SB_BATTERY_20_AND_CHARGING");

        if(isBatteryMoreThan(metaData, 20)) {
            homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, getAllCardType());
            addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
            return;
        }
        addOrUpdateWidgetInfoInDB(mid, identifierKey, custid, featureType, metaData, identifierValue, ttl, type);
        List<String> featureTypeList = new ArrayList<>();
        featureTypeList.add(PayTmPGConstants.SB_BATTERY_10_AND_CHARGING);
        featureTypeList.add(PayTmPGConstants.SB_OUT_OF_BATTERY_10);
        featureTypeList.add(PayTmPGConstants.SB_LOW_BATTERY_20);
        homepageWidgetDao.updateMultipleCardType(mid,identifierValue, InActiveStatus, new Date(), ActiveStatus, type, featureTypeList);
    }*/

    private void markInactiveEvents(String mid, String identifierValue, Long custId, String featureType, String type) {

        try{
            deactivateWidget(mid, identifierValue, custId, featureType, type, "", null);
        }catch (Exception ex) {
            log.info("Exception occurred for {} : ", featureType, ex);
        }
    }

    private boolean isBatteryMoreThan(String metadata, int batteryPercentage) throws Exception {
        log.info("Entering into isBatteryMoreThan20 with metadata: {}", metadata);

        if(StringUtils.isNotBlank(metadata)){
            Map<String, Object> metaDataMap = MappingUtils.convertJsonToMap(metadata);
            String chargeLevel = MapUtils.getString(metaDataMap, PayTmPGConstants.CHARGE_LEVEL);
            int value = Integer.parseInt(chargeLevel);
            return value > batteryPercentage;
        }
        return false;
    }

    private List<String> getAllCardType() {

        List<String> cardTypeList = new ArrayList<>();
        cardTypeList.add(PayTmPGConstants.SB_OUT_OF_BATTERY_5);
        cardTypeList.add(PayTmPGConstants.SB_OUT_OF_BATTERY_10);
  //      cardTypeList.add(PayTmPGConstants.SB_LOW_BATTERY_20);
        cardTypeList.add(PayTmPGConstants.SB_BATTERY_5_AND_CHARGING);
        cardTypeList.add(PayTmPGConstants.SB_BATTERY_10_AND_CHARGING);
//        cardTypeList.add(PayTmPGConstants.SB_BATTERY_20_AND_CHARGING);
        cardTypeList.add(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_CHARGING);
        cardTypeList.add(PayTmPGConstants.SB_CHARGER_CONNECTED_AND_NOT_CHARGING);
        cardTypeList.add(PayTmPGConstants.SB_CHARGER_DISCONNECTED);
        cardTypeList.add(PayTmPGConstants.SB_CHARGER_DISCONNECTED_MULTIPLE);
        cardTypeList.add(PayTmPGConstants.SB_MANUAL_SWITCH_OFF);
        cardTypeList.add(PayTmPGConstants.SB_MANUAL_SWITCH_ON);
        return cardTypeList;
    }

}
