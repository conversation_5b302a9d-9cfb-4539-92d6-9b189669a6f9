package com.paytm.aggregatorgateway.service.impl;

import com.amazonaws.util.CollectionUtils;
import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.service.SettleApiService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.flogger.Flogger;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SettleApiServiceImpl implements SettleApiService {
    static List<String> preferencesList ;

    static {
        preferencesList = new ArrayList<String>();
        preferencesList.add("BW_ENABLED");
        preferencesList.add("BW_MANUAL_TYPE");
        preferencesList.add("SETTLE_FREEZE");
        preferencesList.add("TRANSFER_HOLD");
    }

    @Autowired
    MerchantProfileService merchantProfileService;

    private static final Logger LOGGER = LogManager.getLogger(SettleApiServiceImpl.class);

    @Override
    public ResponseUmp getSettleApi(Map<String, Object> requestBody) throws Exception {
        String mid = MapUtils.getString(requestBody,"mid");
        boolean settleFreeze = false;
        boolean bwMannualTypeFlag = false;
        boolean transferHold = false;
        boolean bwEnabled = false;
        boolean isEligible = false;
        try {
            List<Map<String, Object>> prefValueList = (List<Map<String, Object>>) merchantProfileService.getMerchantPref(mid, preferencesList).get("merchantPref");
            if (!CollectionUtils.isNullOrEmpty(prefValueList)) {
                for (Map<String, Object> prefMap : prefValueList) {
                    String preferenceType = "";
                    String preferenceValue = "";
                    if (prefMap.get("prefName") != null
                            && StringUtils.isNotBlank(prefMap.get("prefName").toString())) {
                        preferenceType = prefMap.get("prefName").toString();
                    }
                    if (prefMap.get("prefValue") != null
                            && StringUtils.isNotBlank(prefMap.get("prefValue").toString())) {
                        preferenceValue = prefMap.get("prefValue").toString();
                    }
                    if (preferenceType.equals("SETTLE_FREEZE")) {
                        if (preferenceValue.equals("Y")) {
                            settleFreeze = true;
                            break;
                        }
                    }
                    if (preferenceType.equals("TRANSFER_HOLD")) {
                        if (preferenceValue.equals("Y")) {
                            transferHold = true;
                            break;
                        }
                    }
                    if (preferenceType.equals("BW_ENABLED")) {
                        if (preferenceValue.equals("Y")) {
                            bwEnabled = true;
                        }
                    }
                    if (preferenceType.equals("BW_MANUAL_TYPE")) {
                        if (StringUtils.isBlank(preferenceValue))
                            bwMannualTypeFlag = true;
                        if (preferenceValue.equals("BUSINESSWALLET_WITHDRAW"))
                            bwMannualTypeFlag = true;
                        if (preferenceValue.equals("BW_TRANSFER_ONLY"))
                            bwMannualTypeFlag = true;
                    }
                }
            }
            if (!settleFreeze && !transferHold && bwEnabled && bwMannualTypeFlag) {
                LOGGER.info("merchant is eligible mid:{} ",mid);
                isEligible = true;
            }
            HashMap<String, Object> responseMap = new HashMap<>();
            responseMap.put("isEligible", isEligible);
            return new ResponseUmp("SUCCESS", "200", "Results fetch successfully", responseMap);
        }
        catch (Exception e){
            LOGGER.error("Exception in settle api "+e);
            throw e;
        }
    }
}
