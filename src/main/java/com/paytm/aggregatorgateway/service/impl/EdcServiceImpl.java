package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.service.EdcService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import java.util.Map;

@Slf4j
@Service
public class EdcServiceImpl implements EdcService {

    @Value("${edc.client}")
    private String edcClientId;

    @Autowired
    private Environment environment;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;
    @Override
    public Map<String,Object> fetchLatLong(String deviceId, String ssotoken) throws Exception {
        try{
                HttpHeaders headerParams = new HttpHeaders();
                headerParams.setContentType(MediaType.APPLICATION_JSON);
                headerParams.add("oauthToken",ssotoken);
                String edcUrl = environment.getProperty(DomainConstants.EOS_BASE_URL) + "/getLastTransactionAddressDetails/deviceId/"+deviceId;
                ResponseEntity<String> edcHttpResponse = restProcessorDelegate.executeEosAddressRequestHystrix(edcUrl, HttpMethod.GET.name(), null, headerParams,null, String.class);
               if (edcHttpResponse.getStatusCode().equals(HttpStatus.OK)) {
                    Map<String, Object> responseMap = MappingUtils.convertJsonToMap(edcHttpResponse.getBody());
                    return responseMap;
                }
                else
                    throw  new RuntimeException("Exception from EDC while fetch Lat Long");
        }catch(Exception e){
            log.error(e.getMessage());
            throw e;
        }
    }

        /*private String getEdcJWT(String mid, String clientId) {
            String clientKeyFromVault = AWSSecretManager.awsSecretsMap.get(AWSSecrets.EDC_JWT_SECRET.getValue());
            String edcSecretKey = StringUtils.isNotBlank(clientKeyFromVault) ? clientKeyFromVault : environment.getRequiredProperty(PayTmPGConstants.EDC_JWT_SECRET);
            String secret = TextCodec.BASE64.encode(edcSecretKey);
            Claims claims = Jwts.claims().setSubject("getLastTransactionAddressDetails");
            claims.put("mid", mid);
            claims.put("clientId", clientId);
            long now = (new Date()).getTime();
            Date validity = new Date(now + 30000);
            return Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS256, secret).setExpiration(validity).compact();
        }*/
}
