package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.CentralToolKitService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
@Slf4j
@Service
public class CentralToolKitServiceImpl implements CentralToolKitService {

    @Autowired
    private Environment environment;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;
    @Override
    public Map<String, Object> getAddressFromLatLong(String latitude, String longitude) throws Exception {
        try {
            String url = environment.getProperty(DomainConstants.CENTRAL_TOOL_KIT_BASE_URL) + "/geolocation/api/v1/address";
            HttpHeaders headerParams = generateCentralToolKitHeaders();
            Map<String, String> queryParam = new HashMap<String, String>();
            queryParam.put("lat", latitude);
            queryParam.put("lon", longitude);
            ResponseEntity<String> httpResponse = restProcessorDelegate.executeCentralToolKitRequestHystrix(url, HttpMethod.GET.name(), queryParam, headerParams, null, String.class);
            if (httpResponse.getStatusCode().equals(HttpStatus.OK)) {

                if (StringUtils.isBlank(httpResponse.getBody()))
                    throw new ValidationException("UMP-400", "Blank response returned by Central tool kit");

                Map<String, Object> responseMap = MappingUtils.convertJsonToMap(httpResponse.getBody());
                return responseMap;
            } else
                throw new RuntimeException("Error while fetching address from Central tool kit");
        }catch(Exception e){
            log.error("Exception from mapping centre ",e);
            throw e;
        }

    }

    private HttpHeaders generateCentralToolKitHeaders() throws Exception{
        String centralToolKitClientId = environment.getProperty(PayTmPGConstants.CENTRAL_TOOL_KIT_CLIENT_ID);
        String secretKeyFromVault = AWSSecretManager.awsSecretsMap.get(AWSSecrets.CENTRAL_TOOL_KIT_SECRET_KEY.getValue());
        String secretKey = StringUtils.isNotBlank(secretKeyFromVault) ? secretKeyFromVault : environment.getProperty(PayTmPGConstants.CENTRAL_TOOL_KIT_SECRET);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("client-id",  centralToolKitClientId);
        httpHeaders.add("client-secret", generateCentralToolKitJwtToken(secretKey));
        httpHeaders.add("x-tags", "[\"OCL\", \"MERCHANT\", \"UMP\"]");
        httpHeaders.add("x-vendor-priority", "GOOGLE,MMI");

        return httpHeaders;
    }
    private String generateCentralToolKitJwtToken(String secretKey) throws Exception {
        Algorithm algorithm = Algorithm.HMAC512(secretKey);
        return JWT.create()
                .withIssuer("CTS")
                .withClaim("timestamp", new DateTime().toString())
                .sign(algorithm);
    }
}
