package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.enums.IntegrationErrorCodes;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.FsmService;
import com.paytm.aggregatorgateway.utils.JwtUtil;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FsmServiceImpl implements FsmService {
    private static final String EDC_ADDRESS_UPDATE_TAG = "EDC_address_update";
    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private Environment environment;

    @Autowired
    private ObjectMapper objectMapper;


    @Override
    public void createBeat(Map<String, Object> requestBody, String mid, String deviceId, Map<String, String> createTicketResponse) throws Exception {
        String url = environment.getRequiredProperty(DomainConstants.FSM_BASE_URL) + "/service-flow/service-beat";

        Map<String, Object> beatCreationRequestBody = new HashMap<>();
        beatCreationRequestBody.put("referenceType", "pgmid");
        beatCreationRequestBody.put("referenceValue", mid);
        List<String> tags = Collections.singletonList(EDC_ADDRESS_UPDATE_TAG);
        beatCreationRequestBody.put("tags", tags);
        beatCreationRequestBody.put("product", "EDC");
        beatCreationRequestBody.put("deviceSerialNumber", deviceId);
        Map<String, Object> address = new HashMap<>();
        address.put("line1", MapUtils.getString(requestBody, "addressLine1"));
        address.put("line2", MapUtils.getString(requestBody, "addressLine2"));
        address.put("city", MapUtils.getString(requestBody, "city"));
        address.put("state", MapUtils.getString(requestBody, "state"));
        address.put("pincode", MapUtils.getString(requestBody, "postalCode"));
        beatCreationRequestBody.put("address", address);
        beatCreationRequestBody.put("typeOfProductModel", "EDC_A50");
        beatCreationRequestBody.put("languageOfProduct", "English");
        beatCreationRequestBody.put("priority", "Ultra premium");
        beatCreationRequestBody.put("freshdeskTicketNumber", createTicketResponse.get("ticketNumber"));
        String createdAt = createTicketResponse.get("created_at");
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime ldt = LocalDateTime.parse(createdAt, dtf);
        ZonedDateTime zdt = ZonedDateTime.of(ldt, ZoneOffset.UTC);
        beatCreationRequestBody.put("created_at", zdt.toString());

        HttpHeaders headers = new HttpHeaders();
        headers.add("session_token", SecurityUtils.getLoggedInUser().getPaytmSSOToken());

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeFsmRequestHystrix(url, HttpMethod.POST.name(), null, headers, beatCreationRequestBody, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Received failure from FSM");
            throw new UMPIntegrationException("Beat creation on FSM failed", IntegrationErrorCodes.CREATE_BEAT_FAILURE);
        }
    }

    @Override
    public Map<String, Object> getRelevantOpenBeat(String mid, String deviceId) throws Exception {
        String url = environment.getRequiredProperty(DomainConstants.FSM_BASE_URL) + "/beats/open-tags";

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("pgMId", mid);
        queryParams.put("deviceSerialNumber", deviceId);

        HttpHeaders headers = new HttpHeaders();
        headers.add("x-jwt-token", createFsmJwt());

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeFsmRequestHystrix(url, HttpMethod.GET.name(), queryParams, headers, null, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            if(httpResponse.getBody().contains("FSE-5416")) //filtering error code- No Active Beat Found For The Device Serial Number and PgMid
                return null;
            else {
                log.error("Received failure from FSM");
                throw new UMPIntegrationException("Open tag on FSM failed", IntegrationErrorCodes.CST_TAG_FAILURE);
            }
        }

        try {
            Map<String, Object> responseMap = objectMapper.readValue(httpResponse.getBody(), new TypeReference<Map<String, Object>>() {});
            List<Map<String, Object>> tagDetailsList = ( List<Map<String, Object>>) responseMap.get("tagDetails");
            tagDetailsList = tagDetailsList.stream().filter(tagDetail -> MapUtils.getString(tagDetail, "name").equals(EDC_ADDRESS_UPDATE_TAG)).collect(Collectors.toList());
            return tagDetailsList.isEmpty() ? null : tagDetailsList.get(0);
        } catch (Exception e) {
            log.error("Failed to fetch relevant open tags from fsm response");
            throw  e;
        }
    }

    private String createFsmJwt() {
        Map<String, String> claims = new HashMap<>();
        claims.put("ts", String.valueOf(System.currentTimeMillis()));
        claims.put("cust_id",  environment.getRequiredProperty(PayTmPGConstants.FSM_CUST_ID));
        claims.put("client_id",  environment.getRequiredProperty(PayTmPGConstants.FSM_CLIENT_ID));
        return JwtUtil.createJwtTokenHS256(claims, null, AWSSecretManager.awsSecretsMap.get(AWSSecrets.FSM_CLIENT_SECRET.getValue()));
    }
}
