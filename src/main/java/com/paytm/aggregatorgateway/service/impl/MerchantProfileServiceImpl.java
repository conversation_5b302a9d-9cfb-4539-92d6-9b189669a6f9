package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.ErrorCodeConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.dto.*;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.service.SmsSubscriptionService;
import com.paytm.aggregatorgateway.service.SubscriptionService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.utils.metrics.MetricConstants;
import com.paytm.aggregatorgateway.utils.metrics.MetricUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.pgdashboard.commons.dto.Merchant;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.google.common.base.Preconditions;

import static com.paytm.aggregatorgateway.constants.DomainConstants.BOSS_BASE_URL;
import static com.paytm.aggregatorgateway.constants.DomainConstants.GOLDENGATE_BASE_URL;

@Service
@RefreshScope
public class MerchantProfileServiceImpl implements MerchantProfileService, PayTmPGConstants {

    private static Algorithm bossJwtAlgorithm;

    @Autowired
    private Environment commonProperties;

    @Autowired
    private ObjectMapper jacksonObjectMapper;

    @Value("${freeze.sms.banner.id}")
    private String freezeBannerId;

    @Value("${sms.subscription.segment.id}")
    private String segmentId;

    @Value("${sms.subscription.commission.value}")
    private String commissionValue;

    @Value("${p4b.force.update.check}")
    private boolean checkForP4bForceUpdate;

    @Value("#{'${p4b.force.update.mids.last.Digit}'.split(',')}")
    private Set<String> p4bForceUpdateMidsLastDigit;

    @Value("${p4b.force.update.lower.version}")
    private String p4bForceUpdateLowerVersion;

    @Value("${p4b.force.update.upper.version}")
    private String p4bForceUpdateUpperVersion;

    @Value("${force.update.ppsl.lower.version}")
    private String ppslForceUpgradeLowerVersion;

    @Value("${force.update.ppsl.upper.version}")
    private String ppslForceUpgradeUpperVersion;

    @Value("${force.update.generic.subHeading}")
    private String forceUpdateGenericSubHeading;

    @Value("${force.update.p4b.lower.and.upper.limit.subHeading}")
    private String forceUpdateP4bLowerAndUpperLimitSubHeading;

    @Value("${force.update.undefined.merchant.p4b.lower.and.upper.limit.subHeading}")
    private String forceUpdateUndefinedMerchantP4bLowerAndUpperLimitSubHeading;

    @Value("${p4b.force.update.check.ppsl}")
    private boolean checkForP4bForceUpdatePPSL;

    @Value("${lower.threshold.version.limit}")
    private String lowerThresholdVersionLimit;

    @Value("${upper.threshold.version.limit}")
    private String upperThresholdVersionLimit;

    @Value("${force.update.p4b.lower.and.upper.limit.subHeading.new}")
    private String forceUpdateP4bLowerAndUpperLimitSubHeadingNew;

    @Value("${force.update.enabled:false}")
    private boolean forceUpdateEnabled;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private SmsSubscriptionService smsSubscriptionService;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    MetricUtils metricUtils;

    @Autowired
    private HomepageWidgetDao homepageWidgetDao;

    private static final String PPSL_MIGRATED_PREF = "PPSL_MIGRATED";


    private static final Logger LOGGER = LogManager.getLogger(MerchantProfileServiceImpl.class);
    @Override
    public Map<String, Object> getCommunicationConfiguration(String mid, String type, String client) throws Exception {
        //LOGGER.info("Into getCommunicationConfiguration->mid:{}", mid);
        String url = commonProperties.getRequiredProperty(BOSS_BASE_URL) + "/api/v1/communication/"
                + mid + "/" + type;

        Map<String, Object> result = new HashMap<>();
        boolean isSmsAllowed = false;
        boolean isFreeze = true;
        boolean isOfflineMerchant=StringUtils.equalsIgnoreCase(SecurityUtils.getCurrentMerchant().getSolutionType(),"OFFLINE");
        if(!isOfflineMerchant){
            isFreeze=false;
        }
        ResponseEntity<String> httpResponse = executeBossGET(url);


        if (httpResponse.getStatusCode().is2xxSuccessful()) {
            //LOGGER.info("mid:{}, boss resp:{}", mid, httpResponse.getBody());
            Map<String, Object> r = jacksonObjectMapper.readValue(httpResponse.getBody(), Map.class);
            if (Objects.nonNull(r)) {
                Map<String, Object> defaultMap = new HashMap<>();
                defaultMap.put("event", "DEFAULT");// this is added todiffern mock and actual boss response
                defaultMap.put("smsAllowed", false);
                defaultMap.put("emailAllowed", false);
                if (!r.containsKey("transaction")) {
                    r.put("transaction", defaultMap);
                }
                if (!r.containsKey("refund")) {
                    r.put("refund", defaultMap);
                }
            }
            Map<String,Object> transaction = MapUtils.getMap(r, "transaction", null);
            boolean txnSms = MapUtils.getBoolean(transaction, "smsAllowed", null);
            Map<String,Object> refund = MapUtils.getMap(r, "refund", null);
            boolean refundSms = MapUtils.getBoolean(refund, "smsAllowed", null);
            if(txnSms || refundSms)
                isSmsAllowed = true;

            result.put("notifications", r);
        } else {
            if (httpResponse.getStatusCode().is4xxClientError())
                throw new ValidationException(httpResponse.getStatusCode().toString(),
                        "Error from boss for API: /api/v1/communication/{mid}/{type} while fetching merchant configuration");
            else
                throw new RuntimeException(
                        "Error from boss for API: /api/v1/communication/{mid}/{type} while fetching merchant configuration");
        }

        String settlementType = SecurityUtils.getCurrentMerchant().getSettlementType();
        boolean isTwsMerchant = PayTmPGConstants.TRANSACTION_WISE_SETTLEMENT.equalsIgnoreCase(settlementType);
        if(isOfflineMerchant) {
            if (isTwsMerchant) {
                isFreeze = true;
                result.put(PayTmPGConstants.ALERT_MESSAGE, "SMS Service is not available for your Account");
            } else if (isSmsAllowed) {
                isFreeze = false;
            }
            else if (!isSmsAllowed) {
                isFreeze = true;
                result.put(PayTmPGConstants.ALERT_MESSAGE, "Please reach out to Helpdesk to activate SMS");
            }
        }
        result.put("isFreeze", isFreeze);
        if (StringUtils.isBlank(mid)) {
            throw new ValidationException("INVALID_MID", "Please provide valid mid");
        }
        MerchantDetailsVO secondaryDetail = fetchMerchantDetails();
        Map<String, String> merchantInfo = new HashMap<>();
        String email = SecurityUtils.getCurrentMerchant().getEmail();
        String mobile = SecurityUtils.getCurrentMerchant().getMobile();
        merchantInfo.put("primaryMobile", mobile);
        merchantInfo.put("primaryEmail", email);
        merchantInfo.put("secondaryEmail", secondaryDetail.getSecondaryMailId());
        merchantInfo.put("secondaryMobile", secondaryDetail.getSecondaryMobileNumber());
        merchantInfo.put("additionalMobile", secondaryDetail.getCommunicationContact());

        result.put("receiverInfo", merchantInfo);
        LOGGER.info("Exiting getCommunicationConfiguration method...");
        return result;
    }

    @Override
    public Map<String, Object> setCommunicationConfiguration(Map<String, Object> requestBody, String mid,
                                                             HttpServletRequest httpRequest) throws Exception {

        LOGGER.info("Entering setCommunicationConfiguration - mid: {}", mid);
        String url = commonProperties.getRequiredProperty(BOSS_BASE_URL) + "/api/v1/communication/"
                + mid;
        Map<String, Object> result = new HashMap<>(requestBody);
        Map<String, Object> transaction = MappingUtils.convertObjectToType(requestBody.get("transaction"), Map.class);
        Map<String, Object> refund = MappingUtils.convertObjectToType(requestBody.get("refund"), Map.class);
        Boolean smsAllowed = (Boolean) transaction.get("smsAllowed");
        String appClient = httpRequest.getHeader("client");
        if (StringUtils.isNotBlank(appClient) && appClient.equals("androidapp")) {
            boolean isOldApp = true; // TODO: fetch appversion from headers and do the needful
            String version = httpRequest.getHeader("appVersion");
            Preconditions.checkArgument(StringUtils.isNoneBlank(version), "version is mandatory in headers");
            String[] splitVersion = version.split("-");
            if (convertAppVersionToInteger(splitVersion[0]) >= convertAppVersionToInteger("4.24.0"))
                isOldApp = false;
            if (isOldApp) {
                handleSMSOnSubscriptionEngine(smsAllowed, mid, result);
                // fetch updated requestBody from boss
                Map<String, Object> updatedStatus = getCommunicationConfiguration(mid, "ALL", appClient);
                Map<String, Object> notifications = MapUtils.getMap(updatedStatus, "notifications", null);
                Map<String, Object> updatedTransaction = MapUtils.getMap(notifications, "transaction", null);
                transaction.put("smsAllowed", MapUtils.getBoolean(updatedTransaction, "smsAllowed", null));
                requestBody.put("transaction", transaction);

                Map<String, Object> updatedRefund = MapUtils.getMap(notifications, "refund", null);
                refund.put("smsAllowed", MapUtils.getBoolean(updatedRefund, "smsAllowed", null));
                requestBody.put("refund", refund);
            }
        }
        handleEmailAlertsOnBoss(requestBody, url);
        result = new HashMap<>(requestBody);
        return result;
    }

    private ResponseEntity<String> executeBossGET(String url) throws Exception {
        LOGGER.debug("Executing GET to BOSS with url: {}", url);

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeBOSSRequestHystrix(url,
                HttpMethod.GET.name(), null, generateBossHeaders(), null, String.class);

        String responseData = httpResponse.getBody();
        LOGGER.debug("Response from BOSS, statusCode:{}", httpResponse.getStatusCode());

        if (responseData != null && responseData.contains("BO_401")) {
            LOGGER.info("Error Response from BOSS: BO_401, clearing admin-token cache");
            throw new RuntimeException("OAuth Failed");
        } else {
            LOGGER.debug("Returning");
            return httpResponse;
        }
    }
    @Override
    public Map<String,Object> getMerchantPref(String mid, List<String> preferencesList) throws Exception {
		LOGGER.info("Getting merchant preference data for mid {}" + mid + " and preferences {}" + preferencesList);
        String preferences = "";
        String url = "";
        for (int i = 0; i < preferencesList.size(); i++) {
            if (i == 0) {
                preferences += preferencesList.get(i);
            } else {
                preferences += ("," + preferencesList.get(i));
            }
        }
        url = commonProperties.getRequiredProperty(BOSS_BASE_URL) + "/api/v1/merchant/preference/" + mid
                + "?prefValues=" + preferences;
        try {
            ResponseEntity<String> response = restProcessorDelegate.executeBOSSRequestHystrix(url,
                    HttpMethod.GET.name(), null, generateBossHeaders(), null, String.class);
            if (response.getStatusCode().equals(HttpStatus.OK)) {
                String apiResponse = response.getBody().toString();
                return jacksonObjectMapper.readValue(apiResponse,Map.class);
            } else {
                throw new ValidationException("error", response.getBody());
            }
        } catch (Exception e) {
            LOGGER.warn("Error in getting merchant preference data for mid {}" + mid + " and preferences {}"
                    + preferencesList);
            throw e;
        }
    }

    @Override
    public Map<String, Object> getMerchantPreference(String mid,List<String> preferences,Boolean filterByStatus) throws Exception {
        String url = commonProperties.getProperty(BOSS_BASE_URL) + "/api/v1/merchant/preference/" + mid +"?prefValues="+StringUtils.join(preferences, ",");
        if(filterByStatus){
            url+="&filterByStatus=Y";
        }
        LOGGER.info("Entering into get merchant preference List API with url {} and with preferences {} with filterByStatus {}", url,preferences,filterByStatus);
        ResponseEntity<String> response = executeBossGET(url);
        LOGGER.debug("Exiting from getMerchantPreference with response");
        return jacksonObjectMapper.readValue(response.getBody(), Map.class);
    }

    private HttpHeaders generateBossHeaders() {
        if (bossJwtAlgorithm == null) {
            String clientKey = AWSSecretManager.awsSecretsMap.get(AWSSecrets.BOSS_CLIENT_KEY.getValue());
            byte[] decodedKey = java.util.Base64.getDecoder().decode(clientKey);
            bossJwtAlgorithm = Algorithm.HMAC512(decodedKey);
        }
        String clientId = AWSSecretManager.awsSecretsMap.get(AWSSecrets.BOSS_CLIENT_ID.getValue());
        String token = JWT.create().withIssuedAt(new Date()).withClaim(PayTmPGConstants.CLIENT_ID, clientId)
                .sign(bossJwtAlgorithm);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.add(PayTmPGConstants.X_CLIENT_TOKEN, token);
        headers.add(PayTmPGConstants.X_CLIENT_ID, clientId);
        return headers;
    }

    @Override
    public MerchantDetailsVO fetchMerchantDetails() throws Exception {
        LOGGER.info("Enter to the fetchMerchantDetails");
        String mid = SecurityUtils.getCurrentMerchant().getMid();
        StringBuilder url = new StringBuilder(commonProperties.getProperty(BOSS_BASE_URL) + "/api/v1/merchant/" + mid + "/info?isUIDrequired=false");

        ResponseEntity<String> httpResponse = executeBossAppGET(url.toString());

        if (HttpStatus.OK != httpResponse.getStatusCode()) {
            LOGGER.info("Getting failure response from Boss | Http Status: {}", httpResponse.getStatusCode());
            if (httpResponse.getStatusCode().is4xxClientError())
                throw new ValidationException(httpResponse.getStatusCode().toString(), "Failure Response from Boss");
            else
                throw new RuntimeException("Failure Response from boss");
        }

        MerchantDetailsVO merchantDetailsVO = MappingUtils.convertJsonToType(httpResponse.getBody(),
                MerchantDetailsVO.class);

        LOGGER.info("Exiting from fetchMerchantDetails");
        return merchantDetailsVO;
    }

    public void handleSMSEmailAlertsOnBoss(Map<String, Object> requestBody, String url) throws Exception
    {
        LOGGER.info("Inside handleSMSEmailAlertsOnBoss method with request body {}",requestBody);
        String reqBody = jacksonObjectMapper.writeValueAsString(requestBody);
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeBOSSRequestHystrix(url,
                HttpMethod.PUT.name(), null, generateBossHeaders(), reqBody, String.class);
        if (httpResponse.getStatusCode().is2xxSuccessful()) {
            // LOGGER.info("mid:{}, boss resp:{}", mid, httpResponse.getBody());
            // Map<String, Object> r = jacksonObjectMapper.readValue(httpResponse.getBody(),
            // Map.class);
            // result.put("bossResponse", r);
        } else {
            if (httpResponse.getStatusCode().is4xxClientError())
                throw new ValidationException(httpResponse.getStatusCode().toString(),
                        "Error from boss for API: /api/v1/communication/{mid} while setting merchant configuration: "
                                + httpResponse.getBody());
            else
                throw new RuntimeException(
                        "Error from boss for API: /api/v1/communication/{mid} while setting merchant configuration: "
                                + httpResponse.getBody());
        }
    }

    private ResponseEntity<String> executeBossAppGET(String url)
            throws Exception {
        LOGGER.debug("Executing GET to BOSS with url: {}", url);

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeBOSSAppRequestHystrix(url,
                HttpMethod.GET.name(), null, generateBossHeaders(), null, String.class);

        String responseData = httpResponse.getBody();
        LOGGER.debug("Response from BOSS, statusCode:{}", httpResponse.getStatusCode());

        if (responseData != null && responseData.contains("BO_401")) {
            LOGGER.debug("Error Response from BOSS: BO_401, clearing admin-token cache");
            throw new RuntimeException("OAuth Failed");
        } else {
            LOGGER.debug("Returning");
            return httpResponse;
        }
    }

    private int convertAppVersionToInteger(String version) {
        int firstIndex = version.indexOf(".");
        int lastIndex = version.lastIndexOf(".");
        StringBuilder sb = new StringBuilder().append(version.substring(0, firstIndex));
        if (lastIndex - firstIndex == 2) {
            sb.append("0");
        }
        sb.append(version.substring(firstIndex + 1, lastIndex));
        if (version.length() - 1 - lastIndex == 1) {
            sb.append("0");
        }
        sb.append(version.substring(lastIndex + 1));
        String newVersion = sb.toString();
        int target = Integer.parseInt(newVersion);
        return target;
    }

    private Map<String, Object> handleSMSOnSubscriptionEngine(Boolean smsAllowed, String mid,
                                                              Map<String, Object> result) throws Exception, InterruptedException, IOException {
        LOGGER.info("inside new flow");
        String custId = SecurityUtils.getLoggedInUser().getId();
        String subscriptionStatus = "ACTIVE,SUSPEND";
        if (smsAllowed.equals(true)) {
            LOGGER.info("inside enabling sms");
            Map<String, Object> fetchResponse = subscriptionService.fetchSubscription(mid, mid, "SMS_CHARGE", null,
                    null, subscriptionStatus);
            if ((fetchResponse.get("statusCode").equals("200"))) {
                LOGGER.info("Enter to update subscription for enable");
                Map<String, Object> results = MappingUtils.extractTypeSafe(fetchResponse, "results", Map.class);
                List<Map<String, Object>> subscriptions = MappingUtils.extractTypeSafe(results, "subscriptions",
                        List.class);
                Map<String, Object> subscription = subscriptions.get(0);
                UpdateSubscriptionStatusDTO request = createUpdateData(mid, custId, subscription.get("usn").toString(),
                        "SMS_CHARGE", subscription.get("subscriptionType").toString(), "RESUME");
                request.setInstantSmsEnable(true);
                String updateResponse = smsSubscriptionService.updateSubscriptionStatus(request,false,"androidapp");
                if(StringUtils.isBlank(updateResponse)
                        || updateResponse.toLowerCase().contains("failure")) {
                    LOGGER.info("sms subscription update failed: updateResponse: " + updateResponse);
                    metricUtils.pushCounterMetrics(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/oldApp", "POST", ErrorCodeConstants.SMS_BUG_SUP_UPDATE_FAILED));
                }

                return result;
            } else if (fetchResponse.get("statusCode").equals("204")) {
//				String id = smsSubscriptionService.fetchSegmentId(custId, "android", "S2(New)", null, null, null, null,
//						null);
                String id = smsSubscriptionService.fetchIdFromCleverTap("androidapp", custId,segmentId);
                LOGGER.info("id is {}", id);
                if (StringUtils.isNotBlank(id) && (id.equals(segmentId))) {
                    LOGGER.info("creating subscription call");
                    SmsSubscriptionDTO request = createSubscriptionData(mid, custId);
                    String response = smsSubscriptionService.createSubscription(request);
                    if(StringUtils.isBlank(response)
                            || response.toLowerCase().contains("failure")) {
                        LOGGER.info("sms subscription creation failed: response: " + response);
                        metricUtils.pushCounterMetrics(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/oldApp", "POST", ErrorCodeConstants.SMS_BUG_SUP_CREATE_FAILED));
                    }

                    return result;
                } else if (StringUtils.isNotBlank(id) && (id.equals("1"))) {
                    return result;
                }
            } else {
                LOGGER.info("Error from Subscription Engine can't enable");
                return result;
            }
        } else {
            LOGGER.info("Inside Disabling Sms");
            Map<String, Object> fetchResponse = subscriptionService.fetchSubscription(mid, mid, "SMS_CHARGE", null,
                    null, subscriptionStatus);
            if (((fetchResponse.get("statusCode")).equals("200"))) {
                LOGGER.info("Enter to update subscription for disable");
                Map<String, Object> results = MappingUtils.extractTypeSafe(fetchResponse, "results", Map.class);
                List<Map<String, Object>> subscriptions = MappingUtils.extractTypeSafe(results, "subscriptions",
                        List.class);
                Map<String, Object> subscription = subscriptions.get(0);
                UpdateSubscriptionStatusDTO request = createUpdateData(mid, custId, subscription.get("usn").toString(),
                        "SMS_CHARGE", subscription.get("subscriptionType").toString(), "SUSPEND");
                request.setInstantSmsDisable(true);// temp fix
                LOGGER.info("request generated is {}", request.toString());
                String updateResponse = smsSubscriptionService.updateSubscriptionStatus(request,false,"androidapp");
                if(StringUtils.isBlank(updateResponse)
                        || updateResponse.toLowerCase().contains("failure")) {
                    LOGGER.info("sms subscription update failed: updateResponse: " + updateResponse);
                    metricUtils.pushCounterMetrics(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/oldApp", "POST", ErrorCodeConstants.SMS_BUG_SUP_UPDATE_FAILED));
                }

                return result;
            } else if (!(fetchResponse.get("statusCode").equals("204"))) {
                LOGGER.info("Error from Subscription Engine can't disable");
                return result;
            }
        }
        return result;
    }

    private void handleEmailAlertsOnBoss(Map<String, Object> requestBody, String url)
            throws JsonProcessingException, InterruptedException {

        String reqBody = jacksonObjectMapper.writeValueAsString(requestBody);
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeBOSSRequestHystrix(url,
                HttpMethod.PUT.name(), null, generateBossHeaders(), reqBody, String.class);
        if (httpResponse.getStatusCode().is2xxSuccessful()) {
            // LOGGER.info("mid:{}, boss resp:{}", mid, httpResponse.getBody());
            // Map<String, Object> r = jacksonObjectMapper.readValue(httpResponse.getBody(),
            // Map.class);
            // result.put("bossResponse", r);
        } else {
            LOGGER.info("notification set failed on boss");
            metricUtils.pushCounterMetrics(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/oldApp", "POST", ErrorCodeConstants.SMS_BUG_BOSS_SET_COMM_FAILED));
            if (httpResponse.getStatusCode().is4xxClientError())
                throw new ValidationException(httpResponse.getStatusCode().toString(),
                        "Error from boss for API: /api/v1/communication/{mid} while setting merchant configuration: "
                                + httpResponse.getBody());
            else
                throw new RuntimeException(
                        "Error from boss for API: /api/v1/communication/{mid} while setting merchant configuration: "
                                + httpResponse.getBody());
        }

    }
    private UpdateSubscriptionStatusDTO createUpdateData(String mid, String custId, String usn, String serviceName,
                                                         String subscriptionType, String status) {
        UpdateSubscriptionStatusDTO updateSubscriptionStatusDTO = new UpdateSubscriptionStatusDTO();
        updateSubscriptionStatusDTO.setMid(mid);
        updateSubscriptionStatusDTO.setCustId(custId);
        updateSubscriptionStatusDTO.setUsn(usn);
        updateSubscriptionStatusDTO.setServiceName(serviceName);
        updateSubscriptionStatusDTO.setSubscriptionType(subscriptionType);
        updateSubscriptionStatusDTO.setStatus(status);
        return updateSubscriptionStatusDTO;
    }

    private SmsSubscriptionDTO createSubscriptionData(String mid, String custId) {
        SmsSubscriptionDTO smsSubscriptionDTO = new SmsSubscriptionDTO();
        smsSubscriptionDTO.setMid(mid);
        smsSubscriptionDTO.setCustId(custId);
        smsSubscriptionDTO.setUsn(mid);
        smsSubscriptionDTO.setServiceName("SMS_CHARGE");
        smsSubscriptionDTO.setServiceType("SMS_PILOT");
        smsSubscriptionDTO.setSubscriptionType("RENTAL");
        smsSubscriptionDTO.setPlanPrice(commissionValue);
        smsSubscriptionDTO.setFrequency("1M");

        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String todayDate = currentDateTime.format(formatter);

        smsSubscriptionDTO.setOnboardingDate(todayDate);
        smsSubscriptionDTO.setDeductionStartDate(todayDate);
        return smsSubscriptionDTO;
    }

    @Override
    public ResponseUmp forceUpdateMerchants(String version, String source, String merchantType,
                                            HttpServletResponse httpServletResponse) throws Exception {
        ResponseUmp response = new ResponseUmp();
        String mid = SecurityUtils.getCurrentMerchant().getMid();
        if(StringUtils.isBlank(merchantType)){
            merchantType = SecurityUtils.getCurrentMerchant().getMerchantType();
        }

        boolean versionCheck = (convertAppVersionToInteger(version) >= convertAppVersionToInteger(lowerThresholdVersionLimit) && convertAppVersionToInteger(version) < convertAppVersionToInteger(upperThresholdVersionLimit));
        if(forceUpdateEnabled && versionCheck && source.equalsIgnoreCase("P4B_ANDROID")){
            boolean isEntryExist = homepageWidgetDao.isEntryExist(mid);
            if(isEntryExist) {
                LOGGER.info("Status isEntryExist: {}", isEntryExist);
                ResponseUmp resUmp = new ResponseUmp();
                resUmp.setStatus("SUCCESS");
                Map<String, Object> responseMap = new HashMap<>();
                responseMap.put("forceUpdate", true);
                responseMap.put("heading", "Please upgrade your App");
                responseMap.put("subHeading",
                        forceUpdateP4bLowerAndUpperLimitSubHeadingNew);
                responseMap.put("cta", "Update App Now");
                resUmp.setResults(responseMap);
                return resUmp;
            }
        }

        if (source.equalsIgnoreCase("P4B_ANDROID") || source.equalsIgnoreCase("P4B_IOS")
                || source.equalsIgnoreCase("CAPP_ANDROID") || source.equalsIgnoreCase("CAPP_IOS")) {
            String merchantSourceMapping = mapMerchantToSource(source, merchantType);
            String requiredVersion = commonProperties.getProperty(merchantSourceMapping, "");
            Map<String, Object> responseMap = new HashMap<>();
            if (merchantType.equalsIgnoreCase("NonSD") || merchantType.equalsIgnoreCase("50K")
                    || merchantType.equalsIgnoreCase("500K") || merchantType.equalsIgnoreCase("100K")
                    || merchantType.equalsIgnoreCase("UNLIMITED_SD")) {
                LOGGER.info("Merchant Type matches the required type : {}", merchantType);
                if (StringUtils.isNotBlank(requiredVersion)) {
                    if (convertAppVersionToInteger(version) < convertAppVersionToInteger(requiredVersion)) {
                        responseMap.put("heading", "Please upgrade your App");
                        responseMap.put("subHeading",
                                forceUpdateGenericSubHeading);
                        responseMap.put("cta", "Update App Now");
                        responseMap.put("forceUpdate", true);
                        response.setStatusCode(HttpStatus.OK.toString());
                        response.setStatus("SUCCESS");
                        response.setResults(responseMap);
                        httpServletResponse.setHeader("cache-control", "max-age=1800");
                    }
                    else if(checkForP4bForceUpdate) {
                        String midLastDigit = "" + (StringUtils.isNotBlank(mid) ? mid.charAt(mid.length() - 1) : "");
                        if (p4bForceUpdateMidsLastDigit.contains(midLastDigit)
                                && StringUtils.isNotBlank(p4bForceUpdateLowerVersion) && StringUtils.isNotBlank(p4bForceUpdateUpperVersion)
                                && StringUtils.isNotBlank(version) && convertAppVersionToInteger(version) >= convertAppVersionToInteger(p4bForceUpdateLowerVersion)
                                && convertAppVersionToInteger(version) <= convertAppVersionToInteger(p4bForceUpdateUpperVersion)) {
                            LOGGER.info("force update version for version : {}", version);
                            responseMap = new HashMap<>();
                            responseMap.put("heading", "Please upgrade your App");
                            responseMap.put("subHeading",
                                    forceUpdateP4bLowerAndUpperLimitSubHeading);
                            responseMap.put("cta", "Update App Now");
                            responseMap.put("forceUpdate", true);
                            response.setStatusCode(HttpStatus.OK.toString());
                            response.setStatus("SUCCESS");
                            response.setResults(responseMap);
                            httpServletResponse.setHeader("cache-control", "max-age=1800");

                        }
                        else {
                            responseMap.put("forceUpdate", false);
                            response.setStatusCode(HttpStatus.OK.toString());
                            response.setStatus("SUCCESS");
                            response.setResults(responseMap);
                            httpServletResponse.setHeader("cache-control", "max-age=1800");
                        }
                    }
                    else if(checkForP4bForceUpdatePPSL){
                        responseMap = forceUpdateForPPSL(version);
                        response.setStatusCode(HttpStatus.OK.toString());
                        response.setStatus("SUCCESS");
                        response.setResults(responseMap);
                        httpServletResponse.setHeader("cache-control", "max-age=1800");
                    }
                    else {
                        responseMap.put("forceUpdate", false);
                        response.setStatusCode(HttpStatus.OK.toString());
                        response.setStatus("SUCCESS");
                        response.setResults(responseMap);
                        httpServletResponse.setHeader("cache-control", "max-age=1800");
                    }
                } else {
                    responseMap.put("forceUpdate", false);
                    response.setStatusCode(HttpStatus.OK.toString());
                    response.setStatus("SUCCESS");
                    response.setResults(responseMap);
                    httpServletResponse.setHeader("cache-control", "max-age=1800");
                }
            }
            else if(checkForP4bForceUpdate) {
                String midLastDigit = "" + (StringUtils.isNotBlank(mid) ? mid.charAt(mid.length() - 1) : "");
                if (p4bForceUpdateMidsLastDigit.contains(midLastDigit)
                        && StringUtils.isNotBlank(p4bForceUpdateLowerVersion) && StringUtils.isNotBlank(p4bForceUpdateUpperVersion)
                        && StringUtils.isNotBlank(version) && convertAppVersionToInteger(version) >= convertAppVersionToInteger(p4bForceUpdateLowerVersion)
                        && convertAppVersionToInteger(version) <= convertAppVersionToInteger(p4bForceUpdateUpperVersion)) {
                    LOGGER.info("force update version for version : {}", version);
                    responseMap.put("heading", "Please upgrade your App");
                    responseMap.put("subHeading",
                            forceUpdateUndefinedMerchantP4bLowerAndUpperLimitSubHeading);
                    responseMap.put("cta", "Update App Now");
                    responseMap.put("forceUpdate", true);
                    response.setStatusCode(HttpStatus.OK.toString());
                    response.setStatus("SUCCESS");
                    response.setResults(responseMap);
                    httpServletResponse.setHeader("cache-control", "max-age=1800");

                }
                else {
                    throw new ValidationException("INVALID_MERCHANT_TYPE", "Invalid Merchant Type");
                }
            }
            else if(checkForP4bForceUpdatePPSL) {
                responseMap = forceUpdateForPPSL(version);
                response.setStatusCode(HttpStatus.OK.toString());
                response.setStatus("SUCCESS");
                response.setResults(responseMap);
                httpServletResponse.setHeader("cache-control", "max-age=1800");
            }
            else {
                throw new ValidationException("INVALID_MERCHANT_TYPE", "Invalid Merchant Type");
            }
        } else {
            throw new ValidationException("PARAM_MISSING", "Param Missing");
        }
        return response;
    }
    private Map<String,Object> forceUpdateForPPSL(String version)
    {
        Merchant merchant = SecurityUtils.getCurrentMerchant();
        String mid = merchant.getMid();
        Map<String, Object> responseMap = new HashMap<>();
        try {

            boolean ppslMerchant = (merchant.getPpslCandidate() != null && merchant.getPpslCandidate()) || (merchant.getPpslMigrated() != null && merchant.getPpslMigrated());
            LOGGER.info("ppslMigrated {}", ppslMerchant);
            if(ppslMerchant && StringUtils.isNotBlank(version) &&
                    StringUtils.isNotBlank(ppslForceUpgradeLowerVersion)
                    && StringUtils.isNotBlank(ppslForceUpgradeUpperVersion)){
                int lowerVersion = convertAppVersionToInteger(ppslForceUpgradeLowerVersion);
                int upperVersion = convertAppVersionToInteger(ppslForceUpgradeUpperVersion);
                int appVersion = convertAppVersionToInteger(version);
                if(appVersion >= lowerVersion && appVersion < upperVersion) {
                    responseMap.put("heading", "Please upgrade your App");
                    responseMap.put("subHeading",
                            forceUpdateP4bLowerAndUpperLimitSubHeading);
                    responseMap.put("cta", "Update App Now");
                    responseMap.put("forceUpdate", true);
                }
                else {
                    responseMap.put("forceUpdate", false);
                }
            }
            else {
                responseMap.put("forceUpdate", false);
            }
        }catch (Exception e) {
            LOGGER.error("Error while PPSL force update {}",e.getMessage());
            responseMap.put("forceUpdate", false);
        }
        return responseMap;
    }

    @Override
    public MerchantInfoDto fetchMerchantDetailsByMid(String mid) throws Exception
    {

        LOGGER.info("Into fetchMerchantDetailsByMid for mid: {}", mid);
        MerchantInfoDto merchantInfo = null;
        String url = commonProperties.getRequiredProperty(BOSS_BASE_URL) + "/api/v1/merchant/" + mid + "/basic/info";
        Map<String, Object> response=new HashMap<>();
        try
        {
            LOGGER.info("Request to BOSS: url: {}", url);

            ResponseEntity<String> httpResponse =restProcessorDelegate.executeBOSSRequestHystrix(url,
                    HttpMethod.GET.name(), null, generateBossHeaders(), null, String.class);
            String responseData = httpResponse.getBody();
            LOGGER.info("Response from BOSS: {}, statusCode: {}", responseData,httpResponse.getStatusCode());

            if (responseData != null && responseData.contains("BO_401"))
            {
                LOGGER.error("Error Response from BOSS: BO_401, clearing admin-token cache");
                throw new RuntimeException("OAuth Failed");
            }
            else
            {
                LOGGER.debug("Returning");
            }

            if (httpResponse.getStatusCode() != HttpStatus.OK) {
                throw new RuntimeException(httpResponse.getBody());
            }

            merchantInfo = MappingUtils.convertJsonToType(httpResponse.getBody(),
                    MerchantInfoDto.class);
        }
        catch (Exception e)
        {
            LOGGER.error("Error while fetching merchant details for mid: {} , {}",mid,e.getMessage());
        }
        return merchantInfo;
    }

    private String mapMerchantToSource(String source, String merchantType) {
        String baseSource = source.toUpperCase();
        StringBuilder mapMerchantToSource = new StringBuilder().append(merchantType).append("_").append(baseSource);

        return mapMerchantToSource.toString();
    }

    public OutletAndBusinessPreference getMerchantOutletAndBusinessPreference(String mid) {
        OutletAndBusinessPreference outletAndBusinessPreference = new OutletAndBusinessPreference();
        Map<String, Object> responseData = null;
        try {
            responseData = getMerchantPreference(mid, Arrays.asList("IS_OUTLET","IS_BUSINESS"),true);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<String> merchantPref = MappingUtils.extractTypeSafe(responseData, "merchantPref", List.class);

        if(null!=merchantPref && merchantPref.contains("IS_OUTLET")){
            outletAndBusinessPreference.setOutlet(true);
        }
        if(null!=merchantPref && merchantPref.contains("IS_BUSINESS")){
            outletAndBusinessPreference.setBusiness(true);
        }
        return outletAndBusinessPreference;
    }


    public ResponseUmp checkAggEligibility(String mid) throws InterruptedException, JsonProcessingException {
        LOGGER.info("Into checkAggEligibility, mid-->{}",mid);
        String url = commonProperties.getProperty(GOLDENGATE_BASE_URL) + "/MerchantService/v1/profile/update/checkFlowEligibility";

        try {
            Map<String, String> queryParam = new HashMap<>();
            queryParam.put("solutionType", "pg_profile_update");
            queryParam.put("solutionSubType", "BANK_DETAIL_UPDATE");
            queryParam.put("mid", mid);

            String session_token = SecurityUtils.getLoggedInUser()!=null?SecurityUtils.getLoggedInUser().getPaytmSSOToken():null;

            HttpHeaders headerParams = new HttpHeaders();
            headerParams.setContentType(MediaType.APPLICATION_JSON);
            headerParams.add("session_token", session_token);

            ResponseEntity<String> httpResponse =
                    restProcessorDelegate.executeOERequestHystrix(url,HttpMethod.GET.name(), queryParam, headerParams, null,String.class);

            JsonNode responseNode = jacksonObjectMapper.readValue(httpResponse.getBody(), JsonNode.class);
            
            // Extract IFSC from bankDetailsSRO and add bank icon URL
            JsonNode bankDetailsSRO = responseNode.get("bankDetailsSRO");
            if (bankDetailsSRO != null && bankDetailsSRO.has("ifsc")) {
                String ifsc = bankDetailsSRO.get("ifsc").asText();
                if (StringUtils.isNotBlank(ifsc)) {
                    String bankIconUrl = MappingUtils.getBankIconUrl(ifsc);
                    ((com.fasterxml.jackson.databind.node.ObjectNode) bankDetailsSRO).put("bankIconUrl", bankIconUrl);
                }
            }

            return new ResponseUmp( "Success", "200",null, responseNode);
        } catch (Exception e) {
            LOGGER.error("error while checkAggEligibility");
            throw e;
        }
    }
}
