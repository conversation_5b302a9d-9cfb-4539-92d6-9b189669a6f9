package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.dto.DeductionInitiateRequest;
import com.paytm.aggregatorgateway.dto.QualificationCallbackRequest;
import com.paytm.aggregatorgateway.dto.QualificationCallbackResponse;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.OcrService;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class OcrServiceImpl implements OcrService, DomainConstants {

    private static final Logger LOGGER = LogManager.getLogger(OcrServiceImpl.class);

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${ocr.client.id}")
    private String ocrClientID;

    @Value("${ocr.base.url}")
    private String ocrBaseUrl;

    @Value("${bank.proof.cache.ttl:3600}")
    private Long bankProofCacheTtl;  //TODO how many seconds ?

    @Value("${ump.base.url}")
    private String umpBaseUrl;

    @Autowired
    private Environment env;

    @Override
    public ResponseUmp qualificationCallBack(QualificationCallbackRequest request) {
        LOGGER.info("Processing qualification callback for requestId: {}", request.getRequestId());
        
        try {
            String mid = SecurityUtils.getCurrentMerchant().getMid();
            String redisKey = mid + "BANK_PROOF_UPLOAD";
            
            // Check if request is successful
            if (!"SUCCESS".equals(request.getStatus())) {
                LOGGER.info("Request failed with status: {}", request.getStatus());
                return updateRedisWithFailure(redisKey, request);
            }
            
            // Validate qualification parameters
            QualificationCallbackResponse validationResult = validateQualificationParameters(request);
            if (!"QUALIFICATION_PASSED".equals(validationResult.getRequestStatus())) {
                LOGGER.info("Qualification validation failed");
                return updateRedisWithValidationFailure(redisKey, validationResult);
            }
            
            // Update Redis with success
            updateRedisWithSuccess(redisKey, request);
            
            // Call deduction API and handle response
            boolean deductionApiSuccess = callDeductionAPI(request);
            
            if (deductionApiSuccess) {
                return new ResponseUmp("SUCCESS", "200", "Qualification callback processed successfully", null);
            } else {
                return new ResponseUmp("SUCCESS", "200", "Qualification callback processed with deduction API failure", null);
            }
            
        } catch (Exception e) {
            LOGGER.error("Error processing qualification callback: ", e);
            return new ResponseUmp("FAILED", "500", "Internal server error", null);
        }
    }

    @Override
    public ResponseUmp deductionsCallBack(Map<String, Object> request) {
        LOGGER.info("Processing deductions callback: {}", request);
        
        try {
            String mid = SecurityUtils.getCurrentMerchant().getMid();
            String redisKey = mid + "BANK_PROOF_UPLOAD";
            
            // Extract status from request
            String status = (String) request.get("status");
            String statusCode = (String) request.get("statusCode");
            String statusMessage = (String) request.get("statusMessage");
            String requestId = (String) request.get("requestId");
            
            if ("SUCCESS".equals(status)) {
                // Handle success case
                updateRedisWithDeductionSuccess(redisKey, request);
            } else {
                // Handle failure case
                updateRedisWithDeductionFailure(requestId, status, statusCode, statusMessage);
            }
            
            // Return the current Redis state as response
            return getCurrentRedisState(redisKey);
            
        } catch (Exception e) {
            LOGGER.error("Error processing deductions callback: ", e);
            return new ResponseUmp("FAILED", "500", "Internal server error", null);
        }
    }

    private QualificationCallbackResponse validateQualificationParameters(QualificationCallbackRequest request) {
        if (request.getResponse() == null || request.getResponse().getImageWiseParameters() == null) {
            return new QualificationCallbackResponse("QUALIFICATION_FAILED", 
                new ResponseUmp("FAILED", "400", "Invalid response structure", null));
        }
        
        for (Map.Entry<String, QualificationCallbackRequest.ImageParameters> entry : request.getResponse().getImageWiseParameters().entrySet()) {
            
            QualificationCallbackRequest.ImageParameters params = entry.getValue();
            
            // Validate document_type
            if (!isValidDocumentType(params.getDocument_type())) {
                return new QualificationCallbackResponse("QUALIFICATION_FAILED", params);
            }
            
            // Validate liveliness
            if (!isValidLiveliness(params.getDocument_type(), params.getLiveliness())) {
                return new QualificationCallbackResponse("QUALIFICATION_FAILED", params);
            }
            
            // Validate completeness
            if (!isValidCompleteness(params.getCompleteness())) {
                return new QualificationCallbackResponse("QUALIFICATION_FAILED", params);
            }
            
            // Validate valid
            if (!isValidImage(params.getValid())) {
                return new QualificationCallbackResponse("QUALIFICATION_FAILED", params);
            }
        }
        
        return new QualificationCallbackResponse("QUALIFICATION_PASSED", null);
    }

    private boolean isValidDocumentType(QualificationCallbackRequest.ParameterValue documentType) {
        if (documentType == null || documentType.getValue() == null) {
            return false;
        }
        String value = documentType.getValue();
        return "cancelledChequePhoto".equals(value) || "BankStatement".equals(value);
    }

    private boolean isValidLiveliness(QualificationCallbackRequest.ParameterValue documentType, 
                                    QualificationCallbackRequest.ParameterValue liveliness) {
        if (liveliness == null || liveliness.getValue() == null || liveliness.getConfidence() == null) {
            return false;
        }
        
        if (liveliness.getConfidence() < 0.99) {
            return false;
        }
        
        String docType = documentType.getValue();
        String livelinessValue = liveliness.getValue();
        
        if ("cancelledChequePhoto".equals(docType)) {
            return "original".equals(livelinessValue);
        } else if ("BankStatement".equals(docType)) {
            return "original".equals(livelinessValue) || "photocopy".equals(livelinessValue);
        }
        
        return false;
    }

    private boolean isValidCompleteness(QualificationCallbackRequest.ParameterValue completeness) {
        if (completeness == null || completeness.getValue() == null || completeness.getConfidence() == null) {
            return false;
        }
        
        return "complete_image".equals(completeness.getValue()) && completeness.getConfidence() >= 0.93;
    }

    private boolean isValidImage(QualificationCallbackRequest.ParameterValue valid) {
        if (valid == null || valid.getValue() == null || valid.getConfidence() == null) {
            return false;
        }
        
        return "valid_image".equals(valid.getValue()) && valid.getConfidence() >= 0.999;
    }

    private ResponseUmp updateRedisWithFailure(String redisKey, QualificationCallbackRequest request) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("status", request.getStatus());
            result.put("statusCode", request.getStatusCode());
            result.put("statusMessage", request.getStatusMessage());
            result.put("requestId", request.getRequestId());
            
            QualificationCallbackResponse response = new QualificationCallbackResponse("QUALIFICATION_FAILED", result);
            String responseJson = objectMapper.writeValueAsString(response);
            
            redisTemplate.opsForValue().set(redisKey, responseJson, bankProofCacheTtl, TimeUnit.SECONDS);
            LOGGER.info("Updated Redis with failure status for key: {}", redisKey);
            
        } catch (Exception e) {
            LOGGER.error("Error updating Redis with failure: ", e);
        }
        
        return new ResponseUmp("SUCCESS", "200", "Redis updated with failure status", null);
    }

    private ResponseUmp updateRedisWithValidationFailure(String redisKey, QualificationCallbackResponse validationResult) {
        try {
            String responseJson = objectMapper.writeValueAsString(validationResult);
            redisTemplate.opsForValue().set(redisKey, responseJson, bankProofCacheTtl, TimeUnit.SECONDS);
            LOGGER.info("Updated Redis with validation failure for key: {}", redisKey);
            
        } catch (Exception e) {
            LOGGER.error("Error updating Redis with validation failure: ", e);
        }
        
        return new ResponseUmp("SUCCESS", "200", "Redis updated with validation failure", null);
    }

    private void updateRedisWithSuccess(String redisKey, QualificationCallbackRequest request) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("status", request.getStatus());
            result.put("statusCode", request.getStatusCode());
            result.put("statusMessage", request.getStatusMessage());
            result.put("requestId", request.getRequestId());
            
            // Store document type for later use in deductions callback
            String documentType = getFirstDocumentType(request.getResponse().getImageWiseParameters());
            result.put("documentType", documentType);
            
            QualificationCallbackResponse response = new QualificationCallbackResponse("QUALIFICATION_PASSED", result);
            String responseJson = objectMapper.writeValueAsString(response);
            
            redisTemplate.opsForValue().set(redisKey, responseJson, bankProofCacheTtl, TimeUnit.SECONDS);
            LOGGER.info("Updated Redis with success status for key: {}", redisKey);
            
        } catch (Exception e) {
            LOGGER.error("Error updating Redis with success: ", e);
        }
    }

    private boolean callDeductionAPI(QualificationCallbackRequest request) {
        try {
            String url = env.getProperty(DomainConstants.OCR_BASE_URL)+ "/bank-ocr-deduction/api/v1/initiate";
            
            DeductionInitiateRequest deductionRequest = new DeductionInitiateRequest();
            deductionRequest.setClientRequestId(request.getRequestId());
            
            // Get customer ID from current user context
            String customerId = getCustomerIdFromContext();
            deductionRequest.setCustomerId(customerId);
            
            // Set callback URL with proper base URL
            String callbackUrl = umpBaseUrl + "/api/v1/ocr/bankProof/deductions/callBack";
            deductionRequest.setCallbackUrl(callbackUrl);
            
            List<DeductionInitiateRequest.ImageInfo> images = new ArrayList<>();
            for (String imageId : request.getResponse().getProcessedImageFileIds()) {
                // Get document type from imageWiseParameters
                String documentType = getDocumentTypeForImage(request.getResponse().getImageWiseParameters());
                images.add(new DeductionInitiateRequest.ImageInfo(imageId, documentType));
            }
            deductionRequest.setImages(images);
            
            HttpHeaders headers = generateOCRHeaders();
            
            ResponseEntity<String> response = restProcessorDelegate.executeOCRRequestHystrix(url, "POST", null, headers, deductionRequest, String.class);
            
            LOGGER.info("Deduction API response: {}", response.getBody());
            
            // Parse response and update Redis based on success/failure
            return handleDeductionAPIResponse(response, request.getRequestId());
            
        } catch (Exception e) {
            LOGGER.error("Error calling deduction API: ", e);
            // Update Redis with deduction failure
            updateRedisWithDeductionFailure(request.getRequestId(), "FAILED", "500", "Internal server error during deduction API call");
            return false;
        }
    }


    private String getCustomerIdFromContext() {
        try {
            // Try to get customer ID from current user
            com.paytm.pgdashboard.commons.dto.User user = SecurityUtils.getLoggedInUser();
            if (user != null && user.getId() != null) {
                return user.getId();
            }
            
            // Fallback to merchant admin user ID
            com.paytm.pgdashboard.commons.dto.Merchant merchant = SecurityUtils.getCurrentMerchant();
            if (merchant != null && merchant.getAdminUserId() != null) {
                return merchant.getAdminUserId();
            }
            
            LOGGER.warn("Could not determine customer ID from context, using default");
            return "UNKNOWN_CUSTOMER_ID";
            
        } catch (Exception e) {
            LOGGER.error("Error getting customer ID from context: ", e);
            return "UNKNOWN_CUSTOMER_ID";
        }
    }

    private String getDocumentTypeForImage(Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters) {
        // Get the first (and only) object from imageWiseParameters and extract document_type
        if (imageWiseParameters != null && !imageWiseParameters.isEmpty()) {
            QualificationCallbackRequest.ImageParameters firstParams = imageWiseParameters.values().iterator().next();
            if (firstParams.getDocument_type() != null && firstParams.getDocument_type().getValue() != null) {
                return firstParams.getDocument_type().getValue();
            }
        }
 
        throw new RuntimeException("Document type not found in imageWiseParameters");
    }

    private boolean handleDeductionAPIResponse(ResponseEntity<String> response, String requestId) {
        try {
            String responseBody = response.getBody();
            if (responseBody == null) {
                updateRedisWithDeductionFailure(requestId, "FAILED", "500", "Empty response from deduction API");
                return false;
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);
            String status = (String) responseMap.get("status");
            String statusCode = (String) responseMap.get("statusCode");
            String statusMessage = (String) responseMap.get("statusMessage");
            
            if ("SUCCESS".equals(status)) {
                // Update Redis with DEDUCTION_AWAITING status
                updateRedisWithDeductionAwaiting(requestId, status, statusCode, statusMessage);
                return true;
            } else {
                // Update Redis with DEDUCTION_FAILED status
                updateRedisWithDeductionFailure(requestId, status, statusCode, statusMessage);
                return false;
            }
            
        } catch (Exception e) {
            LOGGER.error("Error parsing deduction API response: ", e);
            updateRedisWithDeductionFailure(requestId, "FAILED", "500", "Error parsing deduction API response");
            return false;
        }
    }

    private void updateRedisWithDeductionAwaiting(String requestId, String status, String statusCode, String statusMessage) {
        try {
            String mid = SecurityUtils.getCurrentMerchant().getMid();
            String redisKey = mid + "BANK_PROOF_UPLOAD";
            
            Map<String, Object> result = new HashMap<>();
            result.put("status", status);
            result.put("statusCode", statusCode);
            result.put("statusMessage", statusMessage);
            result.put("requestId", requestId);
            
            QualificationCallbackResponse response = new QualificationCallbackResponse("DEDUCTION_AWAITING", result);
            String responseJson = objectMapper.writeValueAsString(response);
            
            redisTemplate.opsForValue().set(redisKey, responseJson, bankProofCacheTtl, TimeUnit.SECONDS);
            LOGGER.info("Updated Redis with DEDUCTION_AWAITING status for key: {}", redisKey);
            
        } catch (Exception e) {
            LOGGER.error("Error updating Redis with DEDUCTION_AWAITING: ", e);
        }
    }

    private void updateRedisWithDeductionFailure(String requestId, String status, String statusCode, String statusMessage) {
        try {
            String mid = SecurityUtils.getCurrentMerchant().getMid();
            String redisKey = mid + "BANK_PROOF_UPLOAD";
            
            Map<String, Object> result = new HashMap<>();
            result.put("status", status);
            result.put("statusCode", statusCode);
            result.put("statusMessage", statusMessage);
            result.put("requestId", requestId);
            
            QualificationCallbackResponse response = new QualificationCallbackResponse("DEDUCTION_FAILED", result);
            String responseJson = objectMapper.writeValueAsString(response);
            
            redisTemplate.opsForValue().set(redisKey, responseJson, bankProofCacheTtl, TimeUnit.SECONDS);
            LOGGER.info("Updated Redis with DEDUCTION_FAILED status for key: {}", redisKey);
            
        } catch (Exception e) {
            LOGGER.error("Error updating Redis with DEDUCTION_FAILED: ", e);
        }
    }

    private void updateRedisWithDeductionSuccess(String redisKey, Map<String, Object> request) {
        try {
            // Get existing Redis data to preserve document type
            String existingData = redisTemplate.opsForValue().get(redisKey);
            String documentType = "";
            
            if (existingData != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> existingMap = objectMapper.readValue(existingData, Map.class);
                @SuppressWarnings("unchecked")
                Map<String, Object> existingResult = (Map<String, Object>) existingMap.get("result");
                if (existingResult != null && existingResult.containsKey("documentType")) {
                    documentType = (String) existingResult.get("documentType");
                }
            }
            
            // Extract deduction data from request
            @SuppressWarnings("unchecked")
            Map<String, Object> response = (Map<String, Object>) request.get("response");
            @SuppressWarnings("unchecked")
            Map<String, Object> imageWiseParameters = (Map<String, Object>) response.get("imageWiseParameters");
            @SuppressWarnings("unchecked")
            List<String> processedImageFileIds = (List<String>) response.get("processedImageFileIds");
            
            // Get the first image's parameters
            Map<String, Object> firstImageParams = null;
            for (Map.Entry<String, Object> entry : imageWiseParameters.entrySet()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> params = (Map<String, Object>) entry.getValue();
                firstImageParams = params;
                break;
            }
            
            Map<String, Object> result = new HashMap<>();
            if (processedImageFileIds != null && !processedImageFileIds.isEmpty()) {
                result.put("dmsIds", processedImageFileIds.get(0));
            }
            
            if (firstImageParams != null) {
                result.put("account_number", firstImageParams.get("account_number"));
                result.put("ifsc_code", firstImageParams.get("ifsc_code"));
                result.put("account_holder_name", firstImageParams.get("account_holder_name"));
            }
            
            result.put("documentType", documentType);
            
            QualificationCallbackResponse callbackResponse = new QualificationCallbackResponse("DEDUCTION_PASSED", result);
            String responseJson = objectMapper.writeValueAsString(callbackResponse);
            
            redisTemplate.opsForValue().set(redisKey, responseJson, bankProofCacheTtl, TimeUnit.SECONDS);
            LOGGER.info("Updated Redis with DEDUCTION_PASSED status for key: {}", redisKey);
            
            // Call UMP API to set the bank OCR response data
            callUmpRedisSetKeyAPI(redisKey, result);
            
        } catch (Exception e) {
            LOGGER.error("Error updating Redis with DEDUCTION_PASSED: ", e);
        }
    }

    private ResponseUmp getCurrentRedisState(String redisKey) {
        try {
            String redisData = redisTemplate.opsForValue().get(redisKey);
            if (redisData != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> redisMap = objectMapper.readValue(redisData, Map.class);
                return new ResponseUmp("SUCCESS", "200", "Current state retrieved", redisMap);
            } else {
                return new ResponseUmp("FAILED", "404", "No data found in Redis", null);
            }
        } catch (Exception e) {
            LOGGER.error("Error getting current Redis state: ", e);
            return new ResponseUmp("FAILED", "500", "Error retrieving Redis state", null);
        }
    }

    private String getFirstDocumentType(Map<String, QualificationCallbackRequest.ImageParameters> imageWiseParameters) {
        for (QualificationCallbackRequest.ImageParameters params : imageWiseParameters.values()) {
            if (params.getDocument_type() != null && params.getDocument_type().getValue() != null) {
                return params.getDocument_type().getValue();
            }
        }
        return "cancelledChequePhoto"; // Default fallback
    }

    private HttpHeaders generateOCRHeaders() {
        HttpHeaders ocrHeaders = new HttpHeaders();
        String ocrClientSecret = AWSSecretManager.awsSecretsMap.get(AWSSecrets.OCR_CLIENT_SECRET.getValue());
        
        if (ocrClientSecret == null) {
            LOGGER.error("OCR client secret not found in AWS secrets");
            throw new RuntimeException("OCR client secret not configured");
        }
        
        ocrHeaders.add("client-id", ocrClientID);
        ocrHeaders.add("client-secret", ocrClientSecret);
        ocrHeaders.add("Content-Type", "application/json");
        return ocrHeaders;
    }

    public void callUmpRedisSetKeyAPI(String redisKey, Map<String, Object> result) {
        try {
            String url = umpBaseUrl + "/api/v1/redis/setKey";
            
            // Create bank OCR data map from the result - loose coupling for future flexibility
            Map<String, Object> bankOcrData = new HashMap<>();
            
            // Extract account_number
            if (result.get("account_number") != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> accountNumberMap = (Map<String, Object>) result.get("account_number");
                bankOcrData.put("account_number", accountNumberMap.get("value"));
            }
            
            // Extract ifsc_code
            if (result.get("ifsc_code") != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> ifscCodeMap = (Map<String, Object>) result.get("ifsc_code");
                bankOcrData.put("ifsc_code", ifscCodeMap.get("value"));
            }
            
            // Extract account_holder_name
            if (result.get("account_holder_name") != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> accountHolderMap = (Map<String, Object>) result.get("account_holder_name");
                bankOcrData.put("account_holder_name", accountHolderMap.get("value"));
            }
            
            // Extract dmsIds (convert single string to list)
            if (result.get("dmsIds") != null) {
                String dmsId = (String) result.get("dmsIds");
                bankOcrData.put("dmsIds", List.of(dmsId));
            }
            
            // Extract documentType
            if (result.get("documentType") != null) {
                bankOcrData.put("documentType", result.get("documentType"));
            }
            
            // Create the request payload using Map for flexibility
            Map<String, Object> request = new HashMap<>();
            request.put("key", redisKey);
            request.put("value", bankOcrData);

            HttpHeaders headers = generateUMPHeaders();
            
            // Make the API call using UMP method
            ResponseEntity<String> response = restProcessorDelegate.executeUMPRequestHystrix(url, "POST", null, headers, request, String.class);
            
            LOGGER.info("UMP Redis SetKey API response: {}", response.getBody());
            
            // Parse and log the response
            if (response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseMap = objectMapper.readValue(response.getBody(), Map.class);
                String status = (String) responseMap.get("status");
                String message = (String) responseMap.get("message");
                
                if ("SUCCESS".equals(status)) {
                    LOGGER.info("Successfully set bank OCR data in UMP Redis: {}", message);
                } else {
                    LOGGER.error("Failed to set bank OCR data in UMP Redis: {}", message);
                }
            }
            
        } catch (Exception e) {
            LOGGER.error("Error calling UMP Redis SetKey API: ", e);
        }
    }

    private HttpHeaders generateUMPHeaders() { //TODO check headers
        HttpHeaders headers = new HttpHeaders();
        headers.add("x-auth-ump", "umpapp-3754-36d-aqr-cn7");
        return headers;
    }
}
