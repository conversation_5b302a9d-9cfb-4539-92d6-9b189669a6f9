package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.constants.PromoConstants;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.PromoEngineService;
import com.paytm.aggregatorgateway.utils.*;
import com.paytm.aggregatorgateway.vo.PromoRequestVO;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import io.reactivex.schedulers.Schedulers;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class PromoEngineServiceImpl implements PromoEngineService {

	@Autowired
	private RestProcessorDelegate restProcessorDelegate;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private Environment env;

	@Autowired
	StorefrontUtils storefrontUtils;

	private static final Logger LOGGER = LogManager.getLogger(PromoEngineServiceImpl.class);
	private static final List<String> VALID_SECTIONS = Arrays.asList("HOME", "REDEEM_NOW", "ACTIVE_OFFERS", "ALL_OFFERS");

	private HttpHeaders generateHeaders() {
		String jwtToken = PromoServiceUtil.createJwtToken(AWSSecretManager.awsSecretsMap.get(AWSSecrets.PROMO_ENGINE_SECRET_KEY.getValue()),
				env.getProperty(PayTmPGConstants.PROMO_ISSUER));
		HttpHeaders headerParam = new HttpHeaders();
		headerParam.set(PayTmPGConstants.X_CLIENT_ID, PayTmPGConstants.PROMO_CLIENT_ID);
		headerParam.set(PayTmPGConstants.X_JWT_TOKEN, jwtToken);
		return headerParam;
	}

	@Override
	public String getGameList(PromoRequestVO request) throws Exception {
		String url = env.getRequiredProperty(DomainConstants.PROMO_BASE_URL)
				+ env.getRequiredProperty(PayTmPGConstants.PROMO_BASE_PATH) + "supercash";
		Map<String, String> queryMap = new HashMap<>();

		queryMap.put(PromoConstants.MERCHANT_ID, request.getMerchant_id());
		if (request.getStatus() != null)
			queryMap.put(PromoConstants.STATUS, request.getStatus());
		if (request.getPage_number() != null)
			queryMap.put(PromoConstants.PAGE_NO, Objects.toString(request.getPage_number(), null));
		if (request.getPage_size() != null)
			queryMap.put(PromoConstants.PAGE_SIZE, Objects.toString(request.getPage_size(), null));
		if (request.getPage_offset() != null)
			queryMap.put(PromoConstants.PAGE_OFFSET, Objects.toString(request.getPage_offset(), null));
		if (request.getAfter_id() != null)
			queryMap.put(PromoConstants.PAGE_OFFSET, request.getAfter_id());
		
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryMap.put(PromoConstants.LOCALE, locale);
		
		HttpHeaders headerParam = generateHeaders();
		LOGGER.info("game list request is:URL {}", url);
		long startTime = System.currentTimeMillis();
		String responseData = null;
		HttpStatus responseStatus = null;
		try {
			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.GET.name(), queryMap, headerParam,null, String.class);
			responseData = entity.getBody();
			responseStatus = (HttpStatus) entity.getStatusCode();
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}
		/*LOGGER.info("GamesList api response is {} : Time taken : {}", responseStatus,
				(System.currentTimeMillis() - startTime));*/

		return responseData;
	}

	@Override
	public String getGameListV2(PromoRequestVO request) throws Exception {
		String url = env.getRequiredProperty(DomainConstants.PROMO_BASE_URL) + "/v2/mpromocard/s2s/supercash";

		Map<String, String> queryMap = new HashMap<>();
		queryMap.put(PromoConstants.MERCHANT_ID, request.getMerchant_id());
		if (request.getStatus() != null)
			queryMap.put(PromoConstants.STATUS, request.getStatus());
		if (request.getPage_number() != null)
			queryMap.put(PromoConstants.PAGE_NO, Objects.toString(request.getPage_number(), null));
		if (request.getPage_size() != null)
			queryMap.put(PromoConstants.PAGE_SIZE, Objects.toString(request.getPage_size(), null));
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryMap.put(PromoConstants.LOCALE, locale);
		
		HttpHeaders headerParam = generateHeaders();
		LOGGER.info("game list request is:URL {} ", url);
		String responseData = null;
		HttpStatus responseStatus = null;
		long startTime = System.currentTimeMillis();
		try {
			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.GET.name(), queryMap, headerParam,null, String.class);
			responseData = entity.getBody();
			responseStatus = (HttpStatus) entity.getStatusCode();
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}
		/*LOGGER.info("GamesList api  V2 response is {} : Time taken : {}", responseStatus,
				(System.currentTimeMillis() - startTime));*/


		return responseData;

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.paytm.dashboard.promoengine.service.PromoEngineService#getTxnDetail(com.
	 * paytm.dashboard. promoengine.vo.PromoRequestVO)
	 */
	@Override
	public String getTxnDetail(PromoRequestVO request) throws Exception {
		String url = env.getProperty(DomainConstants.PROMO_BASE_URL)
				+ env.getProperty(PayTmPGConstants.PROMO_BASE_PATH) + "supercash";
		if (StringUtils.isNotBlank(request.getGame_id())) {
			url = url + "/" + request.getGame_id();
		} else {
			LOGGER.warn("URL does not have Game_id");
		}
		url = url + "/transactions";
		Map<String, String> queryMap = new HashMap<>();
		queryMap.put(PromoConstants.MERCHANT_ID, request.getMerchant_id());
		if (request.getStage() != null)
			queryMap.put(PromoConstants.STAGE, request.getStage());
		if (request.getOldest_txn_time() != null)
			queryMap.put(PromoConstants.OLDEST_TXN_TIME, request.getOldest_txn_time());
		if (request.getPage_size() != null)
			queryMap.put(PromoConstants.PAGE_SIZE, Objects.toString(request.getPage_size(), null));
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryMap.put(PromoConstants.LOCALE, locale);
		HttpHeaders headerParam = generateHeaders();
		LOGGER.info("TxnDetail request is :URL{}", url);
		long startTime = System.currentTimeMillis();
		String responseData = null;
		HttpStatus responseStatus = null;
		try {
			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.GET.name(), queryMap, headerParam,null, String.class);
			responseData = entity.getBody();
			responseStatus = (HttpStatus) entity.getStatusCode();
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}
		/*LOGGER.info("TxnDetail api response is {} : Time taken : {}", responseStatus,
				(System.currentTimeMillis() - startTime));*/
		return responseData;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.paytm.dashboard.promoengine.service.PromoEngineService#getAllOffers(com.
	 * paytm.dashboard. promoengine.vo.PromoRequestVO)
	 */
	@Override
	public String getAllOffers(PromoRequestVO request) throws Exception {
		StringBuilder url = new StringBuilder(env.getProperty(DomainConstants.PROMO_BASE_URL));
		url.append(env.getProperty(PayTmPGConstants.PROMO_BASE_PATH));
		url.append("campaigns");
		Map<String, String> queryMap = new HashMap<>();
		queryMap.put(PromoConstants.MERCHANT_ID, request.getMerchant_id());
		if (request.getPage_number() != null)
			queryMap.put(PromoConstants.PAGE_NO, Objects.toString(request.getPage_number(), null));
		if (request.getPage_size() != null)
			queryMap.put(PromoConstants.PAGE_SIZE, Objects.toString(request.getPage_size(), null));
		if (request.getPage_offset() != null)
			queryMap.put(PromoConstants.PAGE_OFFSET, Objects.toString(request.getPage_offset(), null));
		if (request.getAfter_id() != null)
			queryMap.put(PromoConstants.AFTER_ID, request.getAfter_id());
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryMap.put(PromoConstants.LOCALE, locale);
		HttpHeaders headerParam = generateHeaders();
		//LOGGER.info("AllOffers request is : URL {}", url);
		long startTime = System.currentTimeMillis();
		String responseData = null;
		HttpStatus responseStatus = null;
		try {

			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.GET.name(), queryMap, headerParam,null, String.class
					);
			responseData = entity.getBody();
			responseStatus = (HttpStatus) entity.getStatusCode();
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}
		/*LOGGER.info("AllOffers api response is {} : Time taken : {}", responseStatus,
				(System.currentTimeMillis() - startTime));*/
		return responseData;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.paytm.dashboard.promoengine.service.PromoEngineService#activateOffer(com.
	 * paytm.dashboard. promoengine.vo.PromoRequestVO)
	 */
	@Override
	public String activateOffer(PromoRequestVO request) throws Exception {
		StringBuilder url = new StringBuilder(env.getProperty(DomainConstants.PROMO_BASE_URL));
		url.append(env.getProperty(PayTmPGConstants.PROMO_BASE_PATH));
		url.append("campaigns");
		if (StringUtils.isNotBlank(request.getCampaign_id())) {
			url.append("/" + request.getCampaign_id());
		} else {
			LOGGER.warn("URL does not have Campaign_id ");
		}
		Map<String, String> queryMap = new HashMap<>();
		queryMap.put(PromoConstants.MERCHANT_ID, request.getMerchant_id());
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryMap.put(PromoConstants.LOCALE, locale);
		HttpHeaders headerParam = generateHeaders();
		//LOGGER.info("Activate Offer request is :URL {} with header params:{}", url, headerParam);
		long startTime = System.currentTimeMillis();
		String responseData = null;
		HttpStatus responseStatus = null;
		try {
			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.POST.name(), queryMap, headerParam,
					null,String.class);
			responseData = entity.getBody();
			responseStatus = (HttpStatus) entity.getStatusCode();
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}
		/*LOGGER.info("Activate Offer api response is {} : Time taken : {}", responseStatus,
				(System.currentTimeMillis() - startTime));*/
		return responseData;
	}

	@Override
	public String getCampaignGameV2(String id, String mid) throws Exception {
		String responseData = null;
		StringBuilder url = new StringBuilder(env.getProperty(DomainConstants.PROMO_BASE_URL));
		url.append("/v2/mpromocard/s2s/supercash/campaign-games/");
		url.append(id);

		HttpHeaders headerParam = generateHeaders();
		long startTime = System.currentTimeMillis();
		HttpStatus responseStatus = null;
		Map<String, String> queryParam = new HashMap<>();
		queryParam.put(PromoConstants.MERCHANT_ID, mid);
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryParam.put(PromoConstants.LOCALE, locale);
		try {
			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.GET.name(), queryParam, headerParam,null,String.class);
			responseData = entity.getBody();
			responseStatus = (HttpStatus) entity.getStatusCode();
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}

		return responseData;
	}

	@Override
	public String selectOffer(String campaignId, String body, String mid) throws Exception {
		LOGGER.debug("Entering into Select Offers");

		StringBuilder url = new StringBuilder(env.getProperty(DomainConstants.PROMO_BASE_URL));
		url.append("/v2/mpromocard/s2s/campaigns/").append(campaignId);

		Map<String, String> queryParam = new HashMap<>();
		queryParam.put(PromoConstants.MERCHANT_ID, mid);
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryParam.put(PromoConstants.LOCALE, locale);
		LOGGER.debug("Sending request to Promo Engine");
		ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.POST.name(), queryParam, generateHeaders(), body,String.class);
		LOGGER.debug("Exiting from Select offer");
		return entity.getBody();
	}

	@Override
	public String gameDetails(String id, String mid) throws Exception {
		String responseData = null;
		StringBuilder url = new StringBuilder(env.getProperty(DomainConstants.PROMO_BASE_URL));
		url.append("/v2/mpromocard/s2s/supercash/");
		url.append(id);
		HttpHeaders headerParam = generateHeaders();
		Map<String, String> queryParam = new HashMap<>();
		queryParam.put(PromoConstants.MERCHANT_ID, mid);
		Locale locale1=LocaleContextHolder.getLocale();
		String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
		queryParam.put(PromoConstants.LOCALE, locale);
		try {
			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.GET.name(), queryParam, headerParam,null,String.class);
			responseData = entity.getBody();
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}
		return responseData;
	}

	public Map<String,Object> campaignGames() throws Exception{
		PromoRequestVO request = new PromoRequestVO();
		request.setMerchant_id(SecurityUtils.getCurrentMerchant().getMid());
		request.setPage_size(5);
		request.setPage_number(1);
		//request.setStatus("INPROGRESS,INITIALIZED,COMPLETED");
		request.setStatus("INPROGRESS,INITIALIZED");
		Map<String,Object> map = new HashMap<> ();
		try{
			map.put("campaignsList", MappingUtils.convertJsonToNode(getAllOffers(request)));
		}catch(Exception e){}
		try{
			JsonNode rootNode=MappingUtils.convertJsonToNode(getGameListV2(request));
//			if(rootNode.hasNonNull("data")){
//				JsonNode dataNode=rootNode.get("data");
//
//				if(dataNode.hasNonNull("stages")){
//					int totalCompletedGames=0;
//					JsonNode stagesNode=rootNode.get("stages");
//
//				}
//			}
			map.put("gameList", rootNode);
			map.put("totalCompletedGames",2);
		}catch(Exception e){}
		return map;
	}

	@Override
	public Map<String, Object> fetchActiveGames(Map<String, Object> request, String pageSize, String mid) throws Exception {
		StringBuilder url = new StringBuilder(env.getProperty(DomainConstants.PROMO_BASE_URL));
		url.append(env.getProperty(PayTmPGConstants.PROMO_BASE_PATH));
		url.append("supercash/tag-offers");

		Map<String, String> queryMap = new HashMap<>();
		queryMap.put(PromoConstants.MERCHANT_ID, mid);
		if(StringUtils.isNotBlank(pageSize)){
			queryMap.put(PromoConstants.PAGE_SIZE, pageSize);
		}

		HttpHeaders headerParam = generateHeaders();
		//LOGGER.info("Active Games request is :URL {} with header params:{}", url, headerParam);

		Map<String, Object> result = new HashMap<>();
		try{
			ResponseEntity<String> response = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.POST.name(), queryMap, headerParam,
					request,String.class);
			if(!response.getStatusCode().is2xxSuccessful()){
				LOGGER.error("Received failure from Promo");
				throw new RuntimeException("Received failure from Promo");
			}

			JsonNode rootNode = objectMapper.readTree(response.getBody());
			JsonNode dataNode = rootNode.get("data");
			JsonNode tagsDataNode = dataNode.get("tags_data");
			if(tagsDataNode.has("sb_lp_offers")) {
				JsonNode sbLpOffersNode = tagsDataNode.get("sb_lp_offers");
				Map<String, Object> sbLpOffers = new HashMap<>();
				JsonNode gamesListNode = sbLpOffersNode.get("games_list");
				JsonNode campaignListNode = sbLpOffersNode.get("campaign_list");
				List<Map<String, Object>> gamesList = objectMapper.convertValue(gamesListNode, new TypeReference<List<Map<String, Object>>>() {
				});
				List<Map<String, Object>> campaignList = objectMapper.convertValue(campaignListNode, new TypeReference<List<Map<String, Object>>>() {
				});
				if (gamesList != null && !gamesList.isEmpty()) {
					sbLpOffers.put("campaign_object", new HashMap<>());
					sbLpOffers.put("games_object", gamesList.get(0));
				} else if (campaignList != null && !campaignList.isEmpty()) {
					sbLpOffers.put("games_object", new HashMap<>());
					DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
					LocalDateTime maxValidUpto = LocalDateTime.MIN;
					for (Map<String, Object> campaign : campaignList) {
						if (campaign.containsKey("valid_upto")) {
							LocalDateTime validUpto = LocalDateTime.parse((String) campaign.get("valid_upto"), dtf);
							if (validUpto.isAfter(maxValidUpto)) {
								maxValidUpto = validUpto;
								sbLpOffers.put("campaign_object", campaign);
							}
						}
					}
				} else {
					sbLpOffers.put("games_object", new HashMap<>());
					sbLpOffers.put("campaign_object", new HashMap<>());
				}
				result.put("sb_lp_offers", sbLpOffers);
			}
			else if(tagsDataNode.has("edc_lp_offers"))
			{
				JsonNode edcLpOffersNode = tagsDataNode.get("edc_lp_offers");
				Map<String, Object> edcLpOffers = new HashMap<>();
				JsonNode gamesListNode = edcLpOffersNode.get("games_list");
				JsonNode campaignListNode = edcLpOffersNode.get("campaign_list");
				List<Map<String, Object>> gamesList = objectMapper.convertValue(gamesListNode, new TypeReference<List<Map<String, Object>>>() {
				});
				List<Map<String, Object>> campaignList = objectMapper.convertValue(campaignListNode, new TypeReference<List<Map<String, Object>>>() {
				});
				edcLpOffers.put("games_object", gamesList);
				edcLpOffers.put("campaign_object", campaignList);
				result.put("edc_lp_offers", edcLpOffers);
			}
		} catch (Exception e) {
			LOGGER.error("Error Occurred for promo api {}", e.getMessage());
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}

		return result;
	}
	@Override
	public ResponseUmp fetchPromoPages(String sectionName, Map<String, String> requestBody) throws Exception {
		LOGGER.info("Processing promo pages request for section: {}, request body: {}", sectionName, requestBody);
		try {
			// Validate section name
			if (StringUtils.isBlank(sectionName) || !VALID_SECTIONS.contains(sectionName)) {
				throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION,
						"Invalid section name. Must be one of: " + String.join(", ", VALID_SECTIONS));
			}
			String mid = SecurityUtils.getCurrentMerchant().getMid();
			String sso_token = SecurityUtils.getLoggedInUser().getPaytmSSOToken();
			
			// Construct URL based on section
			String baseUrl = env.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL);
			String endpoint="";
			switch (sectionName) {
				case "ACTIVE_OFFERS":
					endpoint = "/v2/h/cbo-your-active-offers";
					break;
				case "ALL_OFFERS":
					endpoint = "/v2/h/cbo-offers-for-you";
					break;
				case "REDEEM_NOW":
					endpoint = "/v2/h/cbo-redeem-now";
					break;
				case "HOME":
					endpoint = "/v2/h/p4b-cashback-page";
					break;
			}
			String url = baseUrl + endpoint;

			HttpHeaders headers = storefrontUtils.generateStoreFrontHeaders(mid, sso_token);
			ResponseEntity<String>httpResponse = restProcessorDelegate.executeStoreFrontRequestHystrix(url,HttpMethod.POST.toString(),requestBody,headers,null,String.class);
			if (!httpResponse.getStatusCode().equals(HttpStatus.OK)) {
				LOGGER.info("Error occurred while fetching promo page from storefront");
				throw new RuntimeException();
			}
			Map<String, Object> responseMap = objectMapper.readValue(httpResponse.getBody(), Map.class);
			return new ResponseUmp("Success", "200", "Successfully Fetched Details from store Front", responseMap);
		} catch(Exception e){
			LOGGER.info("exception occur while fetch Promo Pages "+e);
			throw e;
		}
	}

	@Override
	public ResponseUmp fetchCBOdata(String sectionName) throws Exception {
		try {
			if (StringUtils.isBlank(sectionName) || !VALID_SECTIONS.contains(sectionName)) {
				throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION,
						"Invalid section name. Must be one of: " + String.join(", ", VALID_SECTIONS));
			}
			io.reactivex.Observable<String> activeOfferObservable = null;
			io.reactivex.Observable<String> redeemNowObservable = null;
			io.reactivex.Observable<String> allOfferObservable = null;

			HashMap<String, Object> responseMap = new HashMap<String, Object>();
			String mid = SecurityUtils.getCurrentMerchant().getMid();
			String ssotoken = SecurityUtils.getLoggedInUser().getPaytmSSOToken();
			String custId = SecurityUtils.getLoggedInUser().getId();

			allOfferObservable = io.reactivex.Observable.fromCallable(() -> getAllOffers(mid, responseMap))
					.timeout(2, TimeUnit.SECONDS, Schedulers.io()).subscribeOn(Schedulers.io());

			activeOfferObservable = io.reactivex.Observable.fromCallable(() -> getActiveOffer(mid, responseMap))
						.timeout(2, TimeUnit.SECONDS, Schedulers.io()).subscribeOn(Schedulers.io());

			redeemNowObservable = io.reactivex.Observable.fromCallable(() -> getRedeemNowData(custId, responseMap,ssotoken))
					.timeout(2, TimeUnit.SECONDS, Schedulers.io()).subscribeOn(Schedulers.io());


			if (sectionName.equalsIgnoreCase("HOME") && allOfferObservable!=null &&  redeemNowObservable!=null && activeOfferObservable!=null) {
				io.reactivex.Observable.mergeArrayDelayError(allOfferObservable, redeemNowObservable, activeOfferObservable).blockingLast();
			}
			else if (sectionName.equalsIgnoreCase("REDEEM_NOW") && redeemNowObservable!=null) {
				io.reactivex.Observable.mergeArrayDelayError(redeemNowObservable).blockingLast();
			}
			else if (sectionName.equalsIgnoreCase("ALL_OFFERS") && allOfferObservable!=null) {
				io.reactivex.Observable.mergeArrayDelayError(allOfferObservable).blockingLast();
			}
			else if (sectionName.equalsIgnoreCase("ACTIVE_OFFERS") && activeOfferObservable!=null) {
				io.reactivex.Observable.mergeArrayDelayError(activeOfferObservable).blockingLast();
			}
			return new ResponseUmp("SUCCESS", "200", "Data fetched successfully", responseMap);
		}catch(Exception e){
			LOGGER.error("Error while merging response for CBO data api");
			throw e;
		}
	}

	private String getRedeemNowData(String custId, HashMap<String, Object> responseMap, String ssotoken) throws InterruptedException {
		String url = env.getProperty(DomainConstants.VOUCHER_BASE_URL)+"/api/voucher-disc/v1/vouchers";
		Map<String, String> voucherRequestBody = new HashMap<>();
		voucherRequestBody.put("customerId", custId);
		voucherRequestBody.put("excludeExpired", "true");
		//voucherRequestBody.put("isMerchant", "true");
		voucherRequestBody.put("isUsed", "false");
		//voucherRequestBody.put("limit", "1");

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("sso_token", ssotoken);

		ResponseEntity<String> entity = restProcessorDelegate.executeVoucherRequestHystrix(url.toString(),HttpMethod.POST.name(), null, headers, voucherRequestBody,String.class);
		if(!entity.getStatusCode().is2xxSuccessful()) {
			throw new RuntimeException("error while getting all offers");
		}
		try {
			JsonNode redeemNowNode = objectMapper.readTree(entity.getBody());
			responseMap.put("redeemNow", redeemNowNode);
		} catch (Exception e) {
			LOGGER.error("Error parsing redeemNow response", e);
			responseMap.put("redeemNow", entity.getBody());
		}
		return entity.getBody();
	}

	private String getActiveOffer(String mid, HashMap<String, Object> responseMap) throws Exception {
		PromoRequestVO promoRequestVO = new PromoRequestVO();
		promoRequestVO.setPage_size(20);
		promoRequestVO.setPage_number(1);
		promoRequestVO.setMerchant_id(mid);
		promoRequestVO.setStatus("Active");
		String superCashResponse = getGameListV2(promoRequestVO);
		try {
			JsonNode activeOffersNode = objectMapper.readTree(superCashResponse);
			responseMap.put("activeOffers", activeOffersNode);
		} catch (Exception e) {
			LOGGER.error("Error parsing activeOffers response", e);
			responseMap.put("activeOffers", superCashResponse);
		}
		return superCashResponse;
	}

	private String getAllOffers(String mid, HashMap<String, Object> responseMap) throws Exception {
			LOGGER.info("Entering into getAllOffers");
			StringBuilder url = new StringBuilder(env.getProperty(DomainConstants.PROMO_BASE_URL));
			url.append("/v1/mpromocard/s2s/campaigns");
			Map<String, String> queryParam = new HashMap<>();
			queryParam.put(PromoConstants.MERCHANT_ID, mid);
			Locale locale1=LocaleContextHolder.getLocale();
			String locale=locale1.getLanguage() + "-" + locale1.getCountry().toUpperCase();
			queryParam.put(PromoConstants.LOCALE, locale);
			queryParam.put("page_size","20");
			ResponseEntity<String> entity = restProcessorDelegate.executePromoRequestHystrix(url.toString(),HttpMethod.GET.name(), queryParam, generateHeaders(), null,String.class);
			if(!entity.getStatusCode().is2xxSuccessful())
				  throw new RuntimeException("error while getting all offers");
			try {
				JsonNode allOffersNode = objectMapper.readTree(entity.getBody());
				responseMap.put("allOffers", allOffersNode);
			} catch (Exception e) {
				LOGGER.error("Error parsing allOffers response", e);
				responseMap.put("allOffers", entity.getBody());
			}
			return entity.getBody();
	}


	@Override
	public ResponseUmp fetchActiveOffers(Integer page_size, Integer page_number, String status) throws Exception {
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		PromoRequestVO promoRequest = new PromoRequestVO();
		if(null!=page_size){promoRequest.setPage_size(page_size);}
		if(null!=page_number){promoRequest.setPage_number(page_number);}
		promoRequest.setMerchant_id(mid);
		if(null!=status){promoRequest.setStatus(status);}

		try {
			String superCashResponse = getGameListV2(promoRequest);
			JsonNode superCashJson = MappingUtils.convertJsonToNode(superCashResponse);

			List<String> offerIds = new ArrayList<>();

			JsonNode superCashList = superCashJson.path("data").path("supercash_list");
			if (superCashList.isArray()) {
				for (JsonNode offer : superCashList) {
					String offerId = offer.path("offer_id").asText(null);
					if (offerId != null) {
						offerIds.add(offerId);
					}
				}
			}

			LOGGER.info("Fetched offer_ids: {}", offerIds);
			Map<String,Object> responseMap = new HashMap<>();
			responseMap.put("offer_ids",offerIds);
			return new ResponseUmp("SUCCESS","UMP-200","SUCCESS",responseMap);

		} catch (Exception e) {
			LOGGER.info("superCash API failed");
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION.getErrorCode(), e.getMessage());
		}
	}


}
