package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.enums.IntegrationErrorCodes;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.*;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import io.reactivex.Observable;
import io.reactivex.schedulers.Schedulers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class AddressUpdateEdcServiceImpl implements AddressUpdateEdcService {

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private Environment environment;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private FsmService fsmService;

    @Value("${fresh.desk.url}")
    private String freshDeskUrl;

    @Value("${product.id}")
    private String productId;

    @Value("${cst.l1.IssueCategory}")
    private String l1IssueCategoryValue;

    @Value("${cst.l2.IssueCategory}")
    private String l2IssueCategoryValue;

    @Value("${cst.l3.IssueCategory}")
    private String l3IssueCategoryValue;

    @Autowired
    private KybService kybService;

    @Autowired
    private CentralToolKitService centralToolKitService;

    @Autowired
    EdcService edcService;

    @Autowired
    HomepageWidgetDao homepageWidgetDao;

    @Override
    public ResponseUmp updateEdcAddress(Map<String, Object> requestBody, String mid) throws Exception {
        String deviceId = MapUtils.getString(requestBody, "deviceId");

        //check if an open ticket is already present or not
        /*Map<String, Object> relevantOpenTicket;
        try {
            relevantOpenTicket = getRelevantOpenTicket(mid, deviceId);
        } catch (Exception e) {
            log.error("Error occurred while fetching open tickets on FD");
            throw new ResponseUmpException("Failure", "400", "Failed to fetch open tickets from FD", null);
        }*/

        //if open, update the ticket, else create a new ticket
        /*Map<String, String> createOrUpdateTicketResponse;
        try {
            createOrUpdateTicketResponse = createOrUpdateTicket(requestBody, mid, relevantOpenTicket);
            if (createOrUpdateTicketResponse.size()!=2)//required? (not in update ticket case, but might be in create ticket)
                throw new ResponseUmpException("Failure", "400", "Ticket creation/updation on FD failed", null);
        } catch (Exception e) {
            log.error("Error occurred while creating or updating ticket at FD");
            throw new ResponseUmpException("Failure", "400", "Ticket creation/updation on FD failed", null);
        }*/

        Map<String, String> createTicketResponse;
        try{
            createTicketResponse = createTicket(requestBody, mid,deviceId);
        } catch (Exception e){
            log.error("Error occurred while creating ticket at FD: {}", e.getMessage());
            throw e;
        }

        //create a beat using the ticketNumber from create ticket
        try {
            fsmService.createBeat(requestBody, mid, deviceId, createTicketResponse);
        } catch (Exception e) {
            log.error("Error occurred while creating beat on FSM: {}", e.getMessage());
            throw e;
        }

        //update the DB in order to disable the tip on homepage
        Date cardExpiryTimeToSet = new Date(System.currentTimeMillis());
        log.info("Inactivating card");
        homepageWidgetDao.updateStatusAndFlag(mid, deviceId, null, "CM_TRANSACTION_BLOCKED", PayTmPGConstants.InActiveStatus, cardExpiryTimeToSet, PayTmPGConstants.ActiveStatus, "CARD" ,null);

        Map<String, String> result = new HashMap<>();
        result.put("ticketNumber", createTicketResponse.get("ticketNumber"));
        return new ResponseUmp("SUCCESS", "200", "Ticket creation, beat creation, and disabling tip was successful", result);
    }

    private Map<String, String> createOrUpdateTicket(Map<String, Object> requestBody, String mid, Map<String, Object> relevantOpenTicket) throws Exception {
        Map<String, String> result = new HashMap<>();
        if (relevantOpenTicket != null) {//open ticket present, update ticket
            String ticketNumber = MapUtils.getString(relevantOpenTicket, "id");
            //String url = environment.getRequiredProperty(DomainConstants.CST_MGW_URL) + "/cst-mgw/crm/1.0.0/fw/v1/ticket/update/" + ticketNumber;

            /*HttpHeaders headers = new HttpHeaders();
            headers.add("x-mgw-token", "");
            headers.add("sso_token", SecurityUtils.getLoggedInUser().getPaytmSSOToken());*/

            Map<String, Object> updateTicketRequestBody = new HashMap<>();
            Map<String, Object> ticketDetails = new HashMap<>();
            Map<String, String> customFields = new HashMap<>();
            customFields.put("cf_address_line_1", MapUtils.getString(requestBody, "addressLine1"));
            customFields.put("cf_address_line_2", MapUtils.getString(requestBody, "addressLine2"));
            customFields.put("cf_city", MapUtils.getString(requestBody, "city"));
            customFields.put("cf_state", MapUtils.getString(requestBody, "state"));
            customFields.put("cf_postal_code", MapUtils.getString(requestBody, "postalCode"));
            customFields.put("cf_device_serial_number", MapUtils.getString(requestBody, "deviceId"));
            ticketDetails.put("custom_fields", customFields);
            ticketDetails.put("unique_external_id", mid);
            updateTicketRequestBody.put("ticketDetails", ticketDetails);
            updateTicketRequestBody.put("freshDeskUrl", freshDeskUrl);

            /*ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstMGWRequestHystrix(url, HttpMethod.POST.name(), null, headers, updateTicketRequestBody, String.class);
            if (!httpResponse.getStatusCode().is2xxSuccessful()) {
                log.error("Received failure from CST-MGW : FD -> while updating ticket");
                throw new RuntimeException("Ticket updation on FD failed");
            }*/

            updateTicket(ticketNumber, updateTicketRequestBody);

            result.put("ticketNumber", ticketNumber);
            result.put("created_at", MapUtils.getString(relevantOpenTicket, "created_at"));
            return result;
        } else {//No open ticket present, create a new ticket
            String url = environment.getRequiredProperty(DomainConstants.CST_URL) + "/crm/fw/v1/ticket";

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("appClient", "P4B");
            queryParams.put("mhdMID", mid);

            HttpHeaders headers = generateCstMGWHeaders(false);

            //address fields and deviceId are already present in the requestBody
            requestBody.put("cstentity", "p4bedc");
            requestBody.put("mhdMID", mid);
            requestBody.put("source", "100");
            requestBody.put("productId", productId);
            requestBody.put("priority", "1");
            requestBody.put("subject", "Ticket related to p4bedc");
            requestBody.put("status", "2");
            requestBody.put("freshDeskUrl", freshDeskUrl);

            ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.POST.name(), queryParams, headers, requestBody, String.class);
            if (!httpResponse.getStatusCode().is2xxSuccessful()) {
                log.error("Received failure from CST : FD -> while creating a new ticket");
                throw new RuntimeException("Ticket creation on FD failed");
            }

            try {
                Map<String, Object> createTicketResponse = objectMapper.convertValue(httpResponse.getBody(), new TypeReference<Map<String, Object>>() {
                });
                result.put("ticketNumber", MapUtils.getString(createTicketResponse, "ticketNumber"));
                result.put("created_at", MapUtils.getString(createTicketResponse, "createdAt"));
                return result;
            } catch (Exception e) {
                log.error("Failed to fetch ticketNumber from create ticket response");
            }
        }
        return result;
    }

    private Map<String, String> createTicket(Map<String, Object> requestBody, String mid, String deviceId) throws Exception {
        Map<String, String> result = new HashMap<>();
        String url = environment.getRequiredProperty(DomainConstants.CST_URL) + "/crm/fw/v1/ticket";

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("appClient", "P4B");
        queryParams.put("mhdMID", mid);

        HttpHeaders headers = generateCstMGWHeaders(true);

        //address fields and deviceId are already present in the requestBody
        requestBody.put("cstentity", "p4bedc");
        requestBody.put("deviceId", deviceId);
        requestBody.put("mhdMID", mid);
        requestBody.put("source", "100");
        requestBody.put("productId", productId);
        requestBody.put("priority", "1");
        requestBody.put("subject", "Ticket related to p4bedc");
        requestBody.put("status", "2");
        requestBody.put("freshDeskUrl", freshDeskUrl);
        requestBody.put("issueCategoryL1", l1IssueCategoryValue);
        requestBody.put("issueCategoryL2", l2IssueCategoryValue);
        requestBody.put("issueCategoryL3", l3IssueCategoryValue);

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.POST.name(), queryParams, headers, requestBody, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Received failure from CST : FD -> while creating a new ticket");
            throw new UMPIntegrationException("Ticket creation on FD failed", IntegrationErrorCodes.CREATE_TICKET_FAILURE);
        }

        try {
            Map<String, Object> createTicketResponse = objectMapper.readValue(httpResponse.getBody(), new TypeReference<Map<String, Object>>() {
            });
            result.put("ticketNumber", MapUtils.getString(createTicketResponse, "ticketNumber"));
            result.put("created_at", MapUtils.getString(createTicketResponse, "createdAt"));
            return result;
        } catch (Exception e) {
            log.error("Failed to parse create ticket response");
            throw new UMPIntegrationException("Failed to parse create ticket response");
        }
    }

    private void updateTicket(String ticketNumber, Map<String, Object> requestBody) throws Exception {
        String url = environment.getRequiredProperty(DomainConstants.CST_MGW_URL) + "/cst-mgw/crm/1.0.0/fw/v1/ticket/update/" + ticketNumber;

        HttpHeaders headers = new HttpHeaders();
        headers.add("x-mgw-token", "");
        headers.add("sso_token", SecurityUtils.getLoggedInUser().getPaytmSSOToken());

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstMGWRequestHystrix(url, HttpMethod.POST.name(), null, headers, requestBody, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Received failure from CST-MGW : FD -> while updating ticket");
            throw new RuntimeException("Ticket updation on FD failed");
        }
    }

    private HttpHeaders generateCstMGWHeaders(boolean useAnotherClient) throws Exception {
        //TODO: plugin client id and secret key from bff v1
        String clientId = "";
        String secretKey ="";
        if(useAnotherClient) {
            secretKey = AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_SECRET_KEY_V2.getValue());
            clientId = environment.getRequiredProperty(PayTmPGConstants.CST_CLIENT_ID_V2);
        } else {
            secretKey = AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_SECRET_KEY.getValue());
            clientId =environment.getRequiredProperty(PayTmPGConstants.CST_SERVICE_CLIENT_ID);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.add("jwt-client-id", clientId);
        headers.add("Authorization", generateCSTJwtToken(clientId, secretKey));
        return headers;
    }

    private String generateCSTJwtToken(String clientId, String secretKey) throws Exception {
        return Jwts.builder()
                .setSubject(clientId)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .signWith(SignatureAlgorithm.HS512, secretKey.getBytes(StandardCharsets.UTF_8))
                .compact();
    }
    @Override
    public ResponseUmp getEdcAddress(String deviceId, Boolean deploymentAddressStatus, Boolean lastTransactionAddress) throws Exception {
        try {
            Observable<String> deploymentAddressObservable = null;
            Observable<String> lastTransactionAddressObservable = null;
            Observable<String> ticketStatusObservable = null;

            HashMap<String, Object> responseMap = new HashMap<String, Object>();
            String mid = SecurityUtils.getCurrentMerchant().getMid();
            String ssotoken = SecurityUtils.getLoggedInUser().getPaytmSSOToken();

            deploymentAddressObservable = Observable.fromCallable(() -> kybService.getDeploymentAddress(mid, deviceId, responseMap))
                    .timeout(2, TimeUnit.SECONDS, Schedulers.io()).subscribeOn(Schedulers.io());

            if (lastTransactionAddress!=null && lastTransactionAddress == true) {
                lastTransactionAddressObservable = Observable.fromCallable(() -> lastTransactionAddressObservable(ssotoken,deviceId, responseMap))
                        .timeout(2, TimeUnit.SECONDS, Schedulers.io()).subscribeOn(Schedulers.io());
            }

            if (deploymentAddressStatus!=null && deploymentAddressStatus == true) {
                ticketStatusObservable = Observable.fromCallable(() -> getOpenTicket(mid, deviceId, responseMap))
                        .timeout(2, TimeUnit.SECONDS, Schedulers.io()).subscribeOn(Schedulers.io());
            }

            if (deploymentAddressObservable != null && lastTransactionAddressObservable != null && ticketStatusObservable != null)
                Observable.mergeArrayDelayError(deploymentAddressObservable, lastTransactionAddressObservable, ticketStatusObservable).blockingLast();

            else if (deploymentAddressObservable != null && lastTransactionAddressObservable != null)
                Observable.mergeArrayDelayError(deploymentAddressObservable, lastTransactionAddressObservable).blockingLast();

            else if (deploymentAddressObservable != null && ticketStatusObservable != null)
                Observable.mergeArrayDelayError(deploymentAddressObservable, ticketStatusObservable).blockingLast();

            else if (deploymentAddressObservable != null)
                Observable.mergeArrayDelayError(deploymentAddressObservable).blockingLast();

            return new ResponseUmp("200", "SUCCESS", "Address fetch successfully", responseMap);
        }catch(Exception e){
            log.error("Error while merging response in edc address update");
            throw e;
        }

    }

    private String getOpenTicket(String mid, String deviceId, HashMap<String, Object> responseMap) throws Exception {
        Map<String, Object> relevantOpenBeat;
        try {
            relevantOpenBeat = fsmService.getRelevantOpenBeat(mid, deviceId);
            if(relevantOpenBeat!=null)
                responseMap.put("deploymentAddressStatus","Pending");
            else
                responseMap.put("deploymentAddressStatus","Verified");
            return objectMapper.writeValueAsString(relevantOpenBeat);
            //return "";
        } catch (Exception e) {
            log.error("Error occurred while fetching open tags on FSM");
            throw e;
        }
    }

    private String lastTransactionAddressObservable( String ssotoken,String deviceId, HashMap<String, Object> responseMap) throws Exception {
            Map<String, Object> latLongMap = edcService.fetchLatLong(deviceId,ssotoken);

             Map<String, String> latLongBodyMap = (Map<String, String> )latLongMap.get("body");
             String latitude = latLongBodyMap.get("userLBSLatitude");
             String longitude = latLongBodyMap.get("userLBSLongitude");
             //log.info("latitude "+latitude+" longitude "+longitude);

             if(StringUtils.isNotBlank(latitude) && StringUtils.isNotBlank(longitude)) {
                 Map<String, Object> lastTransactionAddressMap = centralToolKitService.getAddressFromLatLong(latitude, longitude);
                 ArrayList<Map<String, Object>> resultList = (ArrayList<Map<String, Object>>) lastTransactionAddressMap.get("result");
                 Map<String, String> addressMap = (Map<String, String>) resultList.get(0).get("address");
                 addressMap.put("latitude", latitude);
                 addressMap.put("longitude", longitude);
                 responseMap.put("lastTransactionAddress", addressMap);
                 return objectMapper.writeValueAsString(lastTransactionAddressMap);
             }
             else
                throw new RuntimeException("latitude ,longitude cannot be null ");
    }
}
