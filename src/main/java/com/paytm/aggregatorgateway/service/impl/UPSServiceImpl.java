package com.paytm.aggregatorgateway.service.impl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.UPSIntegrationConstants;
import com.paytm.aggregatorgateway.dto.*;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.service.helper.UPSServiceHelper;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import static com.paytm.aggregatorgateway.constants.UPSUpdateConstants.SECURITY_SHIELD;

@Service
public class UPSServiceImpl implements UPSService {

    private static Logger LOGGER = LogManager.getLogger(UPSServiceImpl.class);

    @Autowired
    UPSServiceHelper upsServiceHelper;

    @Autowired
    Environment environment;

    @Autowired
    ObjectMapper jsonMapper;


    @Autowired
    RestProcessorDelegate restProcessorDelegate;


    @Override
    public ResponseUmp updateNFCStatus(ReqParamsToUpdateNFCStatus reqParamsToUpdateNFCStatus)
            throws Exception {
        ResponseUmp response = new ResponseUmp();
        try {
            validateRequest(response, reqParamsToUpdateNFCStatus);
            if (response.getStatus().equals("FAILURE")) {
                return response;
            }
            List<ReqParamsToUpdateNFCStatus> requestListForPut = new ArrayList<ReqParamsToUpdateNFCStatus>();
            List<String> prefsKeysList = new ArrayList<String>();
            List<ReqParamsToFetchUPSTagUntag> requestList = new ArrayList<ReqParamsToFetchUPSTagUntag>();
            ReqParamsToFetchUPSTagUntag requestObj = new ReqParamsToFetchUPSTagUntag();
            prefsKeysList.add(reqParamsToUpdateNFCStatus.getPreferenceKey());
            requestObj.setEntityId(reqParamsToUpdateNFCStatus.getEntityId());
            requestObj.setEntityType(reqParamsToUpdateNFCStatus.getEntityType());
            requestObj.setPreferenceKeys(prefsKeysList);
            ReqParamsToUpdateNFCStatus.PreferenceValue preferenceValue = new ReqParamsToUpdateNFCStatus.PreferenceValue();
            preferenceValue = reqParamsToUpdateNFCStatus.getPreferenceValue().get(0);
            requestList.add(requestObj);
            String url = environment.getProperty(UPSIntegrationConstants.BASE_URL)
                    + UPSIntegrationConstants.API_INTERNAL_ENTITY_PREFERENCE;
            String requestBody = jsonMapper.writeValueAsString(requestList);
            HttpHeaders headers = upsServiceHelper.getHeaders(HttpMethod.POST, requestBody);
            ResponseEntity<String> responseFromUPS = restProcessorDelegate.executeUPSRequestHystrix(url,
                    HttpMethod.POST.name(), null, headers, requestBody,String.class);
            //LOGGER.info("Fetch request response received=> " + responseFromUPS);
            if (responseFromUPS.getStatusCode() == HttpStatus.OK) {
                UPSNFCUpdateDto upsNFCUpdateRespDto = jsonMapper.readValue(responseFromUPS.getBody(),
                        UPSNFCUpdateDto.class);
                List<UPSNFCUpdateDto.MidDeviceDetailDto> midDeviceDetailList = upsNFCUpdateRespDto.getResponse();
                if (midDeviceDetailList != null && midDeviceDetailList.size() > 0) {
                    UPSNFCUpdateDto.MidDeviceDetailDto midDeviceDetailDto = midDeviceDetailList.get(0);
                    if (midDeviceDetailDto != null) {
                        List<UPSNFCUpdateDto.PreferenceDto> preferenceDtoList = midDeviceDetailDto.getPreferences();
                        if (preferenceDtoList != null && preferenceDtoList.size() > 0) {
                            UPSNFCUpdateDto.PreferenceDto preferenceDto = preferenceDtoList.get(0);
                            boolean prefFound = false;
                            List<UPSNFCUpdateDto.PreferenceValueDto> valueLi = new ArrayList<UPSNFCUpdateDto.PreferenceValueDto>();
                            if (preferenceDto != null) {
                                valueLi = preferenceDto.getValue();
                                if (valueLi != null && valueLi.size() > 0) {
                                    for (UPSNFCUpdateDto.PreferenceValueDto value : valueLi) {
                                        if (value.getCustId().equals(preferenceValue.getCustId())
                                                && value.getIsSubuser().equals(preferenceValue.getIsSubuser())
                                                && value.getDeviceId().equals(preferenceValue.getDeviceId())) {
                                            value.setNfcEnabled(preferenceValue.getNfcEnabled());
                                            prefFound = true;
                                        }
                                    }
                                } else {
                                    valueLi = new ArrayList<UPSNFCUpdateDto.PreferenceValueDto>();
                                }
                            }
                            if (!prefFound) {
                                UPSNFCUpdateDto.PreferenceValueDto newPrefValue = new UPSNFCUpdateDto.PreferenceValueDto();
                                newPrefValue.setCustId(preferenceValue.getCustId());
                                newPrefValue.setDeviceId(preferenceValue.getDeviceId());
                                newPrefValue.setIsSubuser(preferenceValue.getIsSubuser());
                                newPrefValue.setNfcEnabled(preferenceValue.getNfcEnabled());
                                valueLi.add(newPrefValue);
                                preferenceDto.setValue(valueLi);
                            }
                        }
                    }
                }
                // Making put request
                if (midDeviceDetailList != null && midDeviceDetailList.size() > 0) {
                    UPSNFCUpdateDto.MidDeviceDetailDto midDeviceDetailDto = midDeviceDetailList.get(0);
                    ReqParamsToUpdateNFCStatus reqObjForPut = new ReqParamsToUpdateNFCStatus();
                    reqObjForPut.setEntityId(midDeviceDetailDto.getEntityId());
                    reqObjForPut.setEntityType(midDeviceDetailDto.getEntityType());
                    if (midDeviceDetailDto != null) {
                        List<UPSNFCUpdateDto.PreferenceDto> preferenceDtoList = midDeviceDetailDto.getPreferences();
                        if (preferenceDtoList != null && preferenceDtoList.size() > 0) {
                            UPSNFCUpdateDto.PreferenceDto preferenceDto = preferenceDtoList.get(0);
                            if (preferenceDto != null) {
                                reqObjForPut.setPreferenceKey(preferenceDto.getKey());
                                reqObjForPut.setVersion(preferenceDto.getVersion());
                                List<ReqParamsToUpdateNFCStatus.PreferenceValue> prefList = new ArrayList<ReqParamsToUpdateNFCStatus.PreferenceValue>();
                                List<UPSNFCUpdateDto.PreferenceValueDto> valueLi = preferenceDto.getValue();
                                if (valueLi != null && valueLi.size() > 0) {
                                    for (UPSNFCUpdateDto.PreferenceValueDto value : valueLi) {
                                        ReqParamsToUpdateNFCStatus.PreferenceValue pref = new ReqParamsToUpdateNFCStatus.PreferenceValue();
                                        if (value != null) {
                                            pref.setCustId(value.getCustId());
                                            pref.setDeviceId(value.getDeviceId());
                                            pref.setIsSubuser(value.getIsSubuser());
                                            pref.setNfcEnabled(value.getNfcEnabled());
                                            prefList.add(pref);
                                        }
                                    }
                                    reqObjForPut.setPreferenceValue(prefList);
                                    requestListForPut.add(reqObjForPut);
                                }
                            }
                        }
                    }
                }
                String putUrl = environment.getProperty(UPSIntegrationConstants.BASE_URL)
                        + UPSIntegrationConstants.API_INTERNAL_ENTITY_PREFERENCE;
                String putRequestBody = jsonMapper.writeValueAsString(requestListForPut);
                HttpHeaders putHeaders = upsServiceHelper.getHeaders(HttpMethod.PUT, putRequestBody);
                ResponseEntity<String> putResponse = restProcessorDelegate.executeUPSRequestHystrix(putUrl,HttpMethod.PUT.name(), null,putHeaders,
                        putRequestBody,String.class);
                //LOGGER.info("Update request response received=> " + putResponse);
                if (putResponse.getStatusCode().equals(HttpStatus.OK)) {
                    response.setStatusCode("200");
                    response.setStatus("SUCCESS");
                    response.setStatusMessage(putResponse.getBody());
                } else {
                    throw new UMPIntegrationException(
                            "200 status code not received from UPS.. Response ~ " + putResponse);
                }

            } else {
                throw new UMPIntegrationException(
                        "200 status code not received from UPS.. Response ~ " + responseFromUPS);
            }
        } catch (Exception e) {
            LOGGER.info("Error in updating NFC status {} " + e.getMessage());
            response.setStatus("FAILURE");
            response.setStatusMessage(e.getMessage());
        }
        return response;
    }

    @Override
    public ResponseUmp updateStatus(ReqParamsToUpdateStatus reqParamsToUpdateStatus)
            throws Exception {
        ResponseUmp response = new ResponseUmp();
        try {
            validateRequest(response, reqParamsToUpdateStatus);
            if (response.getStatus().equals("FAILURE")) {
                return response;
            }
            List<ReqParamsToUpdateStatus> requestListForPut = new ArrayList<ReqParamsToUpdateStatus>();
            List<String> prefsKeysList = new ArrayList<String>();
            List<ReqParamsToFetchUPSTagUntag> requestList = new ArrayList<ReqParamsToFetchUPSTagUntag>();
            ReqParamsToFetchUPSTagUntag requestObj = new ReqParamsToFetchUPSTagUntag();
            prefsKeysList.add(reqParamsToUpdateStatus.getPreferenceKey());
            requestObj.setEntityId(reqParamsToUpdateStatus.getEntityId());
            requestObj.setEntityType(reqParamsToUpdateStatus.getEntityType());
            requestObj.setPreferenceKeys(prefsKeysList);
            ReqParamsToUpdateStatus.PreferenceValue preferenceValue = new ReqParamsToUpdateStatus.PreferenceValue();
            preferenceValue = reqParamsToUpdateStatus.getPreferenceValue().get(0);
            requestList.add(requestObj);
            String url = environment.getProperty(UPSIntegrationConstants.BASE_URL)
                    + UPSIntegrationConstants.API_INTERNAL_ENTITY_PREFERENCE;
            String requestBody = jsonMapper.writeValueAsString(requestList);
            HttpHeaders headers = upsServiceHelper.getHeaders(HttpMethod.POST, requestBody);
            ResponseEntity<String> responseFromUPS = restProcessorDelegate.executeUPSRequestHystrix(url,
                    HttpMethod.POST.name(), null, headers, requestBody,String.class);
           if (responseFromUPS.getStatusCode() == HttpStatus.OK) {
                UPSUpdateDto upsUpdateRespDto = jsonMapper.readValue(responseFromUPS.getBody(),
                        UPSUpdateDto.class);
                List<UPSUpdateDto.MidDeviceDetailDto> midDeviceDetailList = upsUpdateRespDto.getResponse();
                if (midDeviceDetailList != null && midDeviceDetailList.size() > 0) {
                    UPSUpdateDto.MidDeviceDetailDto midDeviceDetailDto = midDeviceDetailList.get(0);
                    if (midDeviceDetailDto != null) {
                        List<UPSUpdateDto.PreferenceDto> preferenceDtoList = midDeviceDetailDto.getPreferences();
                        if (preferenceDtoList != null && preferenceDtoList.size() > 0) {
                            UPSUpdateDto.PreferenceDto preferenceDto = preferenceDtoList.get(0);
                            boolean prefFound = false;
                            List<UPSUpdateDto.PreferenceValueDto> valueLi = new ArrayList<UPSUpdateDto.PreferenceValueDto>();
                            if (preferenceDto != null) {
                                valueLi = preferenceDto.getValue();
                                if (valueLi != null && valueLi.size() > 0) {
                                    for (UPSUpdateDto.PreferenceValueDto value : valueLi) {
                                        if (reqParamsToUpdateStatus.getPreferenceKey().equals(SECURITY_SHIELD)
                                                && value.getCustId().equals(preferenceValue.getCustId())
                                                && value.getDeviceId().equals(preferenceValue.getDeviceId())) {
                                            value.setIsSecurityShieldEnabled(preferenceValue.getIsSecurityShieldEnabled());
                                            prefFound = true;
                                        }
                                    }
                                } else {
                                    valueLi = new ArrayList<UPSUpdateDto.PreferenceValueDto>();
                                }
                            }
                            if (!prefFound) {
                                UPSUpdateDto.PreferenceValueDto newPrefValue = new UPSUpdateDto.PreferenceValueDto();
                                if (reqParamsToUpdateStatus.getPreferenceKey().equals(SECURITY_SHIELD)) {
                                    newPrefValue.setCustId(preferenceValue.getCustId());
                                    newPrefValue.setDeviceId(preferenceValue.getDeviceId());
                                    newPrefValue.setIsSecurityShieldEnabled(preferenceValue.getIsSecurityShieldEnabled());
                                    valueLi.add(newPrefValue);
                                }
                                preferenceDto.setValue(valueLi);
                            }
                        }
                    }
                }
                // Making put request
                if (midDeviceDetailList != null && midDeviceDetailList.size() > 0) {
                    UPSUpdateDto.MidDeviceDetailDto midDeviceDetailDto = midDeviceDetailList.get(0);
                    ReqParamsToUpdateStatus reqObjForPut = new ReqParamsToUpdateStatus();
                    reqObjForPut.setEntityId(midDeviceDetailDto.getEntityId());
                    reqObjForPut.setEntityType(midDeviceDetailDto.getEntityType());
                    if (midDeviceDetailDto != null) {
                        List<UPSUpdateDto.PreferenceDto> preferenceDtoList = midDeviceDetailDto.getPreferences();
                        if (preferenceDtoList != null && preferenceDtoList.size() > 0) {
                            UPSUpdateDto.PreferenceDto preferenceDto = preferenceDtoList.get(0);
                            if (preferenceDto != null) {
                                reqObjForPut.setPreferenceKey(preferenceDto.getKey());
                                reqObjForPut.setVersion(preferenceDto.getVersion());
                                List<ReqParamsToUpdateStatus.PreferenceValue> prefList = new ArrayList<ReqParamsToUpdateStatus.PreferenceValue>();
                                List<UPSUpdateDto.PreferenceValueDto> valueLi = preferenceDto.getValue();
                                if (valueLi != null && valueLi.size() > 0) {
                                    for (UPSUpdateDto.PreferenceValueDto value : valueLi) {
                                        ReqParamsToUpdateStatus.PreferenceValue pref = new ReqParamsToUpdateStatus.PreferenceValue();
                                        if (value != null && reqParamsToUpdateStatus.getPreferenceKey().equals(SECURITY_SHIELD)) {
                                            pref.setCustId(value.getCustId());
                                            pref.setDeviceId(value.getDeviceId());
                                            pref.setIsSecurityShieldEnabled(value.getIsSecurityShieldEnabled());
                                            prefList.add(pref);
                                        }
                                    }
                                    reqObjForPut.setPreferenceValue(prefList);
                                    requestListForPut.add(reqObjForPut);
                                }
                            }
                        }
                    }
                }
                String putUrl = environment.getProperty(UPSIntegrationConstants.BASE_URL)
                        + UPSIntegrationConstants.API_INTERNAL_ENTITY_PREFERENCE;
                String putRequestBody = jsonMapper.writeValueAsString(requestListForPut);
                HttpHeaders putHeaders = upsServiceHelper.getHeaders(HttpMethod.PUT, putRequestBody);
                ResponseEntity<String> putResponse = restProcessorDelegate.executeUPSRequestHystrix(putUrl,HttpMethod.PUT.name(),null, putHeaders,
                        putRequestBody,String.class);
                if (putResponse.getStatusCode().equals(HttpStatus.OK)) {
                    response.setStatusCode("200");
                    response.setStatus("SUCCESS");
                    response.setStatusMessage(putResponse.getBody());
                } else {
                    throw new UMPIntegrationException(
                            "200 status code not received from UPS.. Response ~ " + putResponse);
                }
            } else {
                throw new UMPIntegrationException(
                        "200 status code not received from UPS.. Response ~ " + responseFromUPS);
            }
        } catch (Exception e) {
            LOGGER.info("Error in updating status {} " + e.getMessage());
            response.setStatus("FAILURE");
            response.setStatusMessage(e.getMessage());
        }
        return response;
    }

    @Override
    public Map<String, Boolean> getDevicePreferences(String mid) {
        LOGGER.info("Api- {} called with mid {} from UPS",UPSIntegrationConstants.API_INTERNAL_ENTITY_PREFERENCE);
        Map<String,Boolean> devicePref = null;
        try {
            String url = environment.getProperty(UPSIntegrationConstants.BASE_URL) +UPSIntegrationConstants.API_INTERNAL_ENTITY_PREFERENCE;
            List<ReqBodyToFetchDevicePref> requestList = new ArrayList<ReqBodyToFetchDevicePref>();
            List<String> preferenceList = new ArrayList<String>();
            ReqBodyToFetchDevicePref requestObj = new ReqBodyToFetchDevicePref();
            preferenceList.add("ocl.boss.merchant.edc");
            preferenceList.add("ocl.iot.merchant.soundbox");
            /*preferenceList.add("ocl.boss.merchant.tapnpay");*/
            requestObj.setEntityId(mid);
            requestObj.setEntityType("MID");
            requestObj.setPreferenceKeys(preferenceList);
            requestList.add(requestObj);
            String request = jsonMapper.writeValueAsString(requestList);
            HttpHeaders header = upsServiceHelper.getHeaders(HttpMethod.POST,request);
            ResponseEntity<String> responseFromUPS = restProcessorDelegate.executeUPSRequestHystrix(url,HttpMethod.POST.name(), null, header, request,String.class);
            if (responseFromUPS.getStatusCode() == HttpStatus.OK){
                Map<String,Object>upsResponseMap = jsonMapper.readValue(responseFromUPS.getBody(), new TypeReference<Map<String, Object>>(){});
                devicePref = new HashMap<>();
                List<Map<String,Object>> responseList = (List<Map<String,Object>>)upsResponseMap.get("response");
                if(responseList != null && responseList.size()>0) {
                    List<Map<String, Object>> prefsList = (List<Map<String, Object>>) responseList.get(0).get("preferences");
                    for (Map<String, Object> ele : prefsList) {
                        if (ele.containsKey("key")) {
                            String key = MapUtils.getString(ele, "key");
                            Boolean value = false;
                            if(ele.get("value")!= null){
                                value = ((List<Boolean>)ele.get("value")).get(0);
                            }
                            devicePref.put(key,value);
                        }
                    }
                }
            }

        }
        catch(Exception e){
            LOGGER.error("Exception while fetching preferences {} ",e.getMessage());
            return null;
        }
        return devicePref;
    }

    private void validateRequest(ResponseUmp response, ReqParamsToUpdateStatus reqParamsToUpdateStatus) {
        boolean error = false;
        if (reqParamsToUpdateStatus == null) {
            error = true;
            response.setStatusMessage("Request cant be null or empty");
        } else if (StringUtils.isBlank(reqParamsToUpdateStatus.getEntityId())) {
            error = true;
            response.setStatusMessage("EntityId cant be null or empty");
        } else if (StringUtils.isBlank(reqParamsToUpdateStatus.getEntityType())) {
            error = true;
            response.setStatusMessage("EntityType cant be null or empty");
        } else if (StringUtils.isBlank(reqParamsToUpdateStatus.getPreferenceKey())) {
            error = true;
            response.setStatusMessage("PreferenceKey cant be null or empty");
        } else if (reqParamsToUpdateStatus.getPreferenceValue() == null
                || reqParamsToUpdateStatus.getPreferenceValue().size() == 0) {
            error = true;
            response.setStatusMessage("PreferenceValue cant be null or empty");
        }
        if (error) {
            response.setStatus("FAILURE");
            response.setStatusCode(UMPErrorCodeEnums.MISSING_PARAM.getErrorCode());
        } else {
            response.setStatus("SUCCESS");
        }
    }

    private void validateRequest(ResponseUmp response, ReqParamsToUpdateNFCStatus reqParamsToUpdateNFCStatus) {
        boolean error = false;
        if (reqParamsToUpdateNFCStatus == null) {
            error = true;
            response.setStatusMessage("Request cant be null or empty");
        } else if (StringUtils.isBlank(reqParamsToUpdateNFCStatus.getEntityId())) {
            error = true;
            response.setStatusMessage("EntityId cant be null or empty");
        } else if (StringUtils.isBlank(reqParamsToUpdateNFCStatus.getEntityType())) {
            error = true;
            response.setStatusMessage("EntityType cant be null or empty");
        } else if (StringUtils.isBlank(reqParamsToUpdateNFCStatus.getPreferenceKey())) {
            error = true;
            response.setStatusMessage("PreferenceKey cant be null or empty");
        } else if (reqParamsToUpdateNFCStatus.getPreferenceValue() == null
                || reqParamsToUpdateNFCStatus.getPreferenceValue().size() == 0) {
            error = true;
            response.setStatusMessage("PreferenceValue cant be null or empty");
        }
        if (error) {
            response.setStatus("FAILURE");
            response.setStatusCode(UMPErrorCodeEnums.MISSING_PARAM.getErrorCode());
        } else {
            response.setStatus("SUCCESS");
        }
    }
}