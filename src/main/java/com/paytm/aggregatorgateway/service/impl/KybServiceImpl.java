package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.enums.IntegrationErrorCodes;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.KybService;
import com.paytm.aggregatorgateway.utils.JwtUtil;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
@Slf4j
@Service
public class KybServiceImpl implements KybService {

    @Value("${kyb.address.base.url}")
    private String kybAddressBaseUrl;

    @Value("${kyb.client.id}")
    private String kybClientId;

    @Value("${kyb.client.uid}")
    private String kybClientUserId;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private ObjectMapper jsonMapper;

    @Override
    public String getDeploymentAddress(String mid, String deviceId, HashMap<String, Object> responseMap) throws Exception {
        try {
            Map<String, String> queryParam = new HashMap<String, String>();
            queryParam.put("pgMid", mid);
            queryParam.put("fields", "deploymentAddress");
            queryParam.put("deviceId", deviceId);
            String token = createKYBJwtToken();
            //log.info("KYB token: {}", token);

            HttpHeaders headerParams = new HttpHeaders();
            headerParams.setContentType(MediaType.APPLICATION_JSON);
            headerParams.add("x-jwt-token", token);
            String kybUrl = kybAddressBaseUrl + "/web/kyb/get/address";
            ResponseEntity<String> kybHttpResponse = restProcessorDelegate.executeKybAddressRequestHystrix(kybUrl, HttpMethod.GET.name(), queryParam, headerParams, null, String.class);
            Map<String, Object> kybMap = null;
            if (kybHttpResponse.getStatusCode().equals(HttpStatus.OK))
                kybMap = jsonMapper.readValue(kybHttpResponse.getBody(), Map.class);
            else
                throw new RuntimeException("Error while fetching Address from kyb ");
            if (kybMap == null)
                throw new ValidationException(UMPErrorCodeEnums.KYB_INVALID_RESPONSE);

            Map<String, Object> deploymentAddress = (Map<String, Object>)kybMap.get("deploymentAddress");
            if(deploymentAddress.containsKey("deviceAddress")) {
                ArrayList<Map<String, Object>> deviceAddressList = (ArrayList<Map<String, Object>>) deploymentAddress.get("deviceAddress");
                if (!deviceAddressList.isEmpty())
                    responseMap.put("deploymentAddress", deviceAddressList.get(0).get("address"));
                else
                    throw new UMPIntegrationException("Deployment address list is blank", IntegrationErrorCodes.DONSTREAM_INVALID_RESPONSE);
            }
            else
                responseMap.put("deploymentAddress", deploymentAddress);
            return kybHttpResponse.getBody();
        }catch(Exception e){
            log.error("Exception while fetching from kyb ",e);
            throw e;
        }
    }

    private String createKYBJwtToken() {
        {
            Map<String, String> claims = new HashMap<>();
            claims.put("ts", String.valueOf(System.currentTimeMillis()));
            claims.put("cust_id", kybClientUserId);
            claims.put("client_id", kybClientId);
            return JwtUtil.createJwtTokenHS256(claims, null, AWSSecretManager.awsSecretsMap.get(AWSSecrets.KYB_CLIENT_SECRET.getValue()));
        }
    }
}
