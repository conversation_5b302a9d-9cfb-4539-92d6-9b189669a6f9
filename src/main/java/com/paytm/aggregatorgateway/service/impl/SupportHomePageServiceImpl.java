package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.SupportHomePageConstants;
import com.paytm.aggregatorgateway.dao.SupportHomePageDao;
import com.paytm.aggregatorgateway.dto.*;
import com.paytm.aggregatorgateway.enums.IntegrationErrorCodes;
import com.paytm.aggregatorgateway.enums.JwtAlgorithm;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.CstService;
import com.paytm.aggregatorgateway.service.SupportHomePageService;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.service.helper.SupportHomePageTitleMapping;
import com.paytm.aggregatorgateway.utils.JwtUtil;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.utils.StorefrontUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class SupportHomePageServiceImpl extends SupportHomePageConstants implements SupportHomePageService {

    private static final Logger LOGGER = LogManager.getLogger(SupportHomePageServiceImpl.class);

    @Autowired
    Environment environment;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    RedisHelper redisHelper;

    @Value("${fresh.desk.url}")
    private String freshdeskUrl;

    @Value("${soundbox.storefront.id}")
    private String soundBoxStoreFrontId;

    @Value("${card.machine.storefront.id}")
    private String cardMachineStoreFrontId;

    @Value("${payment.settlement.storefront.id}")
    private String paymentSettlementStoreFrontId;

    @Value("${bussiness.loan.storefront.id}")
    private String bussinessLoanStoreFrontId;

    @Value("${account.setting.storefront.id}")
    private String accountSettingStoreFrontId;

    @Value("${deals.storefront.id}")
    private String dealStoreFrontId;

    @Value("${others.storefront.id}")
    private String otherStoreFrontId;

    @Value("#{'${open.ticket.status}'.split(',')}")
    private List<String> openTicketStatus;

    @Value("#{'${resolved.ticket.status}'.split(',')}")
    private List<String> resolvedTicketStatus;

    @Value("#{'${closed.ticket.status}'.split(',')}")
    private List<String> closedTicketStatus;

    @Value("${fresh.desk.url.callback}")
    private String freshdeskUrlCallBack;

    @Value("${product.id.callback}")
    private String productIdCallBack;

    @Value("${source.cst.callback}")
    private String sourceCstCallBack;

    @Value("${status.cst.call.scheduler.api.failed}")
    private Integer statusCallSchedulerApiFailed;

    @Value("${status.cst.pending.for.callback}")
    private Integer statusPendingForCallback;

    @Value("${status.cst.closed}")
    private Integer statusClosed;

    @Value("${status.cst.resolved}")
    private Integer statusResolved;

    @Value("${customer.issue.category.l1.callback}")
    private String customerIssueCategoryL1Code;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CstService cstService;

    @Autowired
    private SupportHomePageTitleMapping supportHomePageTitleMapping;

    @Autowired
    SupportHomePageDao supportHomePageDao;

    @Autowired
    private UPSService upsService;

    public static final String CAMPAIGN_NAME_CALLBACK_EDC = "CallMeBack_EDC";
    public static final String CAMPAIGN_NAME_CALLBACK_SB = "MHD_CallMeBack_SNB";
    private final String DESCRIPTION_CALLBACK = "Ticket related to P4B callmeback EDC";
    private final String CUSTOMER_ISSUE_CATEGORY_L1_CALLBACK = "Call back request";
    private final String CST_ENTITY_CALLBACK = "p4bCallmeback";
    private final String TXN_ID_CALLBACK = "Callback request";
    private final String SUBJECT_CALLBACK = "P4B Callmeback";
    private final String IND_DIAL_CODE = "+91";
    private final String CALLBACK_AGENT_MOBILE = "0120-4443130";
    private final int MAX_CALL_ATTEMPTS = 3;
    private final int DMID_MAX_LENGTH = 18;
    private final int TICKET_NUMBER_MAX_LENGTH = 12;
    private final int CUST_ID_MAX_LENGTH = 10;
    private final String LANG_CODE_HINDI = "02";
    private final String IVR_MERCHANT_TYPE = "50";
    private final String IDENTIFIER = "0";

    @Autowired
    StorefrontUtils storefrontUtils;

    @Override
    public String fetchIssueCategory(Map<String,String> requestBody) throws Exception
    {
        try
        {
            String mid = SecurityUtils.getCurrentMerchant().getMid();
            String sso_token=SecurityUtils.getLoggedInUser().getPaytmSSOToken();
            LOGGER.info("Entering into fetchIssueCategory for mid {} , requestBody {}", mid, requestBody);
            boolean sbRented = false;
            boolean edcRented = false;
            if (requestBody != null && requestBody.containsKey("sbRented") && requestBody.get("sbRented") != null)
            {
                sbRented = Boolean.valueOf(requestBody.get("sbRented"));
                requestBody.remove("sbRented");
            }
            if (requestBody != null && requestBody.containsKey("edcRented") && requestBody.get("edcRented") != null)
            {
                edcRented = Boolean.valueOf(requestBody.get("edcRented"));
                requestBody.remove("edcRented");
            }
            String url = environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL) + "/v2/h/category-listing-on-support-p4b";
            HttpHeaders headers = storefrontUtils.generateStoreFrontHeaders(mid,sso_token);
            //HttpHeaders headers = new HttpHeaders();
            //headers.add("Content-Type","application/json");
            ResponseEntity<String>httpResponse = restProcessorDelegate.executeStoreFrontRequestHystrix(url,HttpMethod.POST.toString(),requestBody,headers,null,String.class);
            if(!httpResponse.getStatusCode().equals(HttpStatus.OK))
            {
                LOGGER.info("Error occured while fetching category from storefront");
                throw new RuntimeException();
            }
            Map<String,Object>responseMap=objectMapper.readValue(httpResponse.getBody(),Map.class);
            if(responseMap!=null && responseMap.containsKey("page"))
            {
                List<PageDTO> pageDTOS = objectMapper.convertValue(responseMap.get("page"),new TypeReference<List<PageDTO>>() {});
                if(pageDTOS!=null && pageDTOS.size()>0)
                {
                    PageDTO pageDTO = pageDTOS.get(0);
                    if(pageDTO!=null){
                        List<ViewsDTO>viewsDTOS = pageDTO.getViews();
                        if(viewsDTOS!=null && viewsDTOS.size()>0)
                        {
                            ViewsDTO viewsDTO = viewsDTOS.get(0);
                            if(viewsDTO!=null){
                                List<ItemDTO>itemDTOS = viewsDTO.getItems();
                                if(itemDTOS!=null && itemDTOS.size()>0)
                                {
                                    List<CategoryDTO> items = getCategories(sbRented,edcRented,itemDTOS);//reordering of items accordiing to flags sbRented,edcRented
                                    List<Object>pageList = (List<Object>)responseMap.get("page");
                                    Map<String,Object>pageMap = (Map<String, Object>) pageList.get(0);
                                    List<Object>viewsList = (List<Object>)pageMap.get("views");
                                    Map<String,Object> viewsMap= (Map<String, Object>) viewsList.get(0);
                                    viewsMap.put("items",items);
                                }
                                else
                                {
                                    LOGGER.info("item list is empty");
                                    throw new RuntimeException("item list is empty");
                                }
                            }
                            else
                            {
                                LOGGER.info("viewDTO us null");
                                throw new RuntimeException("item list is empty");
                            }
                        }
                        else
                        {
                            LOGGER.info("viewsDtos is empty");
                            throw new RuntimeException("item list is empty");
                        }
                    }
                    else
                    {
                        LOGGER.info("page dto is null");
                        throw new RuntimeException("item list is empty");
                    }
                }
                else
                {
                    LOGGER.info("page info missing from storefront response");
                    throw new RuntimeException("item list is empty");
                }
            }
            else
            {
                LOGGER.info("page info missing from storefront response");
                throw new RuntimeException("item list is empty");
            }
            return objectMapper.writeValueAsString(responseMap);
        }
        catch (Exception e)
        {
            LOGGER.info("Exception occured while getting info from store front {},",e);
            throw new RuntimeException(e);
        }
    }

    private List<CategoryDTO> getCategories(boolean sbRented, boolean edcRented, List<ItemDTO> itemDTOS)
    {
        List<CategoryDTO> categoryList = new ArrayList<>();
        for(ItemDTO itemDTO:itemDTOS)
        {
            CategoryDTO categoryDTO = new CategoryDTO(itemDTO);
            if(sbRented && edcRented)
            {
                if(itemDTO.getId()==Integer.parseInt(soundBoxStoreFrontId))
                {
                    categoryDTO.setPriority(1);
                }
                else if(itemDTO.getId()==Integer.parseInt(cardMachineStoreFrontId))
                {
                    categoryDTO.setPriority(2);
                }
                else if(itemDTO.getId()==Integer.parseInt(paymentSettlementStoreFrontId))
                {
                    categoryDTO.setPriority(3);
                }
                else if(itemDTO.getId()==Integer.parseInt(bussinessLoanStoreFrontId))
                {
                    categoryDTO.setPriority(4);
                }
                else if(itemDTO.getId() == Integer.parseInt(accountSettingStoreFrontId))
                {
                    categoryDTO.setPriority(5);
                }
                else
                {
                    categoryDTO.setPriority(8); // handling for default category
                }
            }
            else if(sbRented && !edcRented)
            {
                if(itemDTO.getId()==Integer.parseInt(soundBoxStoreFrontId))
                {
                    categoryDTO.setPriority(1);
                }
                else if(itemDTO.getId()==Integer.parseInt(paymentSettlementStoreFrontId))
                {
                    categoryDTO.setPriority(2);
                }
                else if(itemDTO.getId()==Integer.parseInt(bussinessLoanStoreFrontId))
                {
                    categoryDTO.setPriority(3);
                }
                else if(itemDTO.getId()==Integer.parseInt(accountSettingStoreFrontId))
                {
                    categoryDTO.setPriority(4);
                }
                else if(itemDTO.getId()==Integer.parseInt(cardMachineStoreFrontId))
                {
                    categoryDTO.setPriority(5);
                }
                else
                {
                    categoryDTO.setPriority(8);// handling for default category
                }
            }
            else if(!sbRented && edcRented)
            {
                if(itemDTO.getId()==Integer.parseInt(cardMachineStoreFrontId))
                {
                    categoryDTO.setPriority(1);
                }
                else if(itemDTO.getId()==Integer.parseInt(paymentSettlementStoreFrontId))
                {
                    categoryDTO.setPriority(2);
                }
                else if(itemDTO.getId()==Integer.parseInt(bussinessLoanStoreFrontId))
                {
                    categoryDTO.setPriority(3);
                }
                else if(itemDTO.getId()==Integer.parseInt(accountSettingStoreFrontId))
                {
                    categoryDTO.setPriority(4);
                }
                else if(itemDTO.getId()==Integer.parseInt(soundBoxStoreFrontId))
                {
                    categoryDTO.setPriority(5);
                }
                else
                {
                    categoryDTO.setPriority(8);// handling for default category
                }
            }
            else if(!sbRented && !edcRented)
            {
                if(itemDTO.getId()==Integer.parseInt(paymentSettlementStoreFrontId))
                {
                    categoryDTO.setPriority(1);
                }
                else if(itemDTO.getId()==Integer.parseInt(bussinessLoanStoreFrontId))
                {
                    categoryDTO.setPriority(2);
                }
                else if(itemDTO.getId()==Integer.parseInt(accountSettingStoreFrontId))
                {
                    categoryDTO.setPriority(3);
                }
                else if(itemDTO.getId()==Integer.parseInt(soundBoxStoreFrontId))
                {
                    categoryDTO.setPriority(4);
                }
                else if(itemDTO.getId()==Integer.parseInt(cardMachineStoreFrontId))
                {
                    categoryDTO.setPriority(5);
                }
                else{
                    categoryDTO.setPriority(8);// handling for default category
                }
            }
            if(itemDTO.getId()==Integer.parseInt(dealStoreFrontId))
            {
                categoryDTO.setPriority(6);
            }
            else if(itemDTO.getId()==Integer.parseInt(otherStoreFrontId))
            {
                categoryDTO.setPriority(7);
            }
            categoryList.add(categoryDTO);
        }
        categoryList.sort(Comparator.comparingInt(CategoryDTO::getPriority));
        return categoryList;
    }

    @Override
    public String reopenTicket(String ticketNumber, String description, String mid, Boolean closeTicket) throws Exception
    {

        Map<String, Object> requestBody=new HashMap<>();
        Map<String, Object> ticketDetails=new HashMap<>();

        ticketDetails.put("unique_external_id","P4B-"+mid);
        ticketDetails.put("description",description);
        ticketDetails.put("priority",REOPEN_TICKET_PRIORITY);
        if(closeTicket!=null && closeTicket){
            ticketDetails.put("status",CLOSE);
        } else{
            ticketDetails.put("status",OPEN);
        }
        requestBody.put("freshDeskUrl", freshdeskUrl);
        requestBody.put("ticketDetails",ticketDetails);

        cstService.updateTicket(ticketNumber,requestBody);

        return "Ticket number "+ticketNumber+" has been successfully reopened.";
    }

    @Override
    public String getSurvey() throws Exception
    {

        HttpHeaders headers = new HttpHeaders();
        headers.add("merchantType",SURVEY_MERCHANT_TYPE);
        headers.add("merchantLimit",SecurityUtils.getCurrentMerchant().getMerchantType());
        headers.add("x_user_mid",SecurityUtils.getCurrentMerchant().getMid());
        headers.add("x_user_uid",SecurityUtils.getLoggedInUser().getId());
        headers.add("survey_client",SURVEY_CLIENT);
        headers.add("clientId",SURVEY_CLIENT_ID);

        String url = environment.getRequiredProperty(DomainConstants.SURVEY_URL)+"/api/v1/survey/getActionMapping";

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeSurveyRequestHystrix(url, HttpMethod.GET.name(), null, headers, null, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            LOGGER.error("Received failure from Survey");
            throw new RuntimeException("Surveys could not be fetched");
        }
        return httpResponse.getBody();
    }

    @Override
    public String getTicketTimeLine(TicketInfoDTO ticketInfoDTO) throws Exception
    {
        LOGGER.info("Entering into getTicketTimeLine.");
        try
        {
            ResponseEntity<String> httpResponse = cstService.getTicketTimeLine(ticketInfoDTO.getTicketNumber(), ticketInfoDTO);
            if(!httpResponse.getStatusCode().equals(HttpStatus.OK))
            {
                LOGGER.info("Error from CST");
                throw new RuntimeException("Error from CST");
            }
            return httpResponse.getBody();
        }
        catch (Exception e)
        {
            LOGGER.info("Exception occred while fetching time line {},",e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String,Object> getAllTickets(boolean categoryWiseTicket) throws Exception
    {
        LOGGER.info("Entering into getAllTickets");
        try
        {
            ResponseEntity<String> httpResponse = cstService.getAllTicketDetails();
            if(!httpResponse.getStatusCode().equals(HttpStatus.OK))
            {
                LOGGER.info("Error fro, CST while fetching all tickets info");
                throw new RuntimeException("Error from, CST while fetching all tickets info");
            }
            Map<String,Object> response = new HashMap<>();
            List<TicketInfoDTO>tickets = new ArrayList<>();
            List<TicketInfoDTO>openTickets = new ArrayList<>();
            List<TicketInfoDTO>validForFeedBackTickets = new ArrayList<>();
            List<TicketInfoDTO>closedTickets = new ArrayList<>();
            Map<String,Object>responseMap=objectMapper.readValue(httpResponse.getBody(),Map.class);
            List<TicketInfoDTO>ticketsList= objectMapper.convertValue(responseMap.get("tickets"),new TypeReference<List<TicketInfoDTO>>() {});
            for(TicketInfoDTO ticketInfoDTO:ticketsList)
            {
                changeItemName(ticketInfoDTO);
                String ticketStatus = String.valueOf(ticketInfoDTO.getTicketStatus());
                if(openTicketStatus.contains(ticketStatus))
                {
                    openTickets.add(ticketInfoDTO);
                }
                else if(resolvedTicketStatus.contains(ticketStatus))
                {
                    validForFeedBackTickets.add(ticketInfoDTO);
                }
                else if(closedTicketStatus.contains(ticketStatus))
                {
                    closedTickets.add(ticketInfoDTO);
                }
            }
            tickets.addAll(openTickets);
            tickets.addAll(validForFeedBackTickets);
            tickets.addAll(closedTickets);
            if(categoryWiseTicket)
            {
                response.put("openTickets", openTickets);
                response.put("closedTickets", closedTickets);
                response.put("validForFeedBackTickets", validForFeedBackTickets);
            }
            else
            {
                response.put("tickets",tickets);
            }
            return response;
        }
        catch (Exception e)
        {
            LOGGER.info("Exception occured while fetch tickets info {}",e);
            throw new RuntimeException(e);
        }
    }

    private void changeItemName(TicketInfoDTO ticketInfoDTO)
    {
        if(ticketInfoDTO==null || StringUtils.isBlank(ticketInfoDTO.getSource()))
        {
            LOGGER.info("Item name not changed for ticketId : {},as source is empty",ticketInfoDTO.getTicketNumber());
            return ;
        }
        if(ticketInfoDTO.getOrigin()==CONTACT_US)
        {
            if(StringUtils.isBlank(ticketInfoDTO.getCstentity()))
            {
                LOGGER.info("Item name not changed for ticketId : {},as cstEntity is empty",ticketInfoDTO.getTicketNumber());
                return ;
            }
            if(supportHomePageTitleMapping.titleMapping.containsKey(ticketInfoDTO.getCstentity()))
            {
                ticketInfoDTO.setItemName(supportHomePageTitleMapping.titleMapping.get(ticketInfoDTO.getCstentity()));
            }
            else
            {
                ticketInfoDTO.setItemName(PAYTM_SERVICE_QUERY);
            }
        }
        else if(ticketInfoDTO.getOrigin()==PHONE)
        {
            if(StringUtils.isBlank(ticketInfoDTO.getL1IssueCategory()))
            {
                LOGGER.info("Item name not changed for ticketId : {},as l1IssueCategory is empty",ticketInfoDTO.getTicketNumber());
                return ;
            }
            if(supportHomePageTitleMapping.titleMapping.containsKey(ticketInfoDTO.getL1IssueCategory()))
            {
                ticketInfoDTO.setItemName(supportHomePageTitleMapping.titleMapping.get(ticketInfoDTO.getL1IssueCategory()));
            }
            else
            {
                ticketInfoDTO.setItemName(PAYTM_SERVICE_QUERY);
            }
        }
        else
        {
            ticketInfoDTO.setItemName(PAYTM_SERVICE_QUERY);
        }
    }

    @Override
    public Map<String, Object> getRecentTicket() throws Exception
    {
        LOGGER.info("Entering into getRecentTicket");
        try
        {
            Map<String,Object> response = new HashMap<>();
            Map<String,Object> ticketsMap = getAllTickets(true);
            if(ticketsMap!=null && ticketsMap.size()>0)
            {
                boolean pastTickets = false;
                TicketInfoDTO ticketInfoDTO = null;
                List<TicketInfoDTO> openTickets = (List<TicketInfoDTO>) ticketsMap.get("openTickets");
                List<TicketInfoDTO> validForFeedBackTickets = (List<TicketInfoDTO>) ticketsMap.get("validForFeedBackTickets");
                List<TicketInfoDTO> closedTickets = (List<TicketInfoDTO>) ticketsMap.get("closedTickets");
                if(openTickets!=null && openTickets.size()>0)
                {
                    ticketInfoDTO = openTickets.get(0);
                }
                else if(validForFeedBackTickets!=null && validForFeedBackTickets.size()>0)
                {
                    ticketInfoDTO = validForFeedBackTickets.get(0);
                }
                else if(closedTickets!=null && closedTickets.size()>0)
                {
                    pastTickets = true;
                }
                if(!pastTickets)
                {
                    String cst_entity = "";
                    if(ticketInfoDTO!=null && StringUtils.isNotBlank(ticketInfoDTO.getCstentity()))
                    {
                        cst_entity = ticketInfoDTO.getCstentity();
                    }
                    String utcCreatedAt = convertCreatedAtToUTC(ticketInfoDTO.getCreatedAt());
                    ticketInfoDTO.setCreatedAt(utcCreatedAt);
                    ResponseEntity<String> httpResponse = cstService.getTicketTimeLine(ticketInfoDTO.getTicketNumber(), ticketInfoDTO);
                    if (!httpResponse.getStatusCode().equals(HttpStatus.OK))
                    {
                        LOGGER.info("Error from CST time line api");
                        throw new RuntimeException("Error from CST time line api");
                    }
                    response.put("ticketTimeLine",objectMapper.readValue(httpResponse.getBody(),Map.class));
                    response.put("cst_entity",cst_entity);
                    response.put("itemName",ticketInfoDTO.getItemName());
                    response.put("ticketStatus",ticketInfoDTO.getTicketStatus());
                }
                else
                {
                    response.put("ticketTimeLine",null);
                }
                response.put("ticketDetails",ticketInfoDTO);
                response.put("pastTickets",pastTickets);
            }
            return response;
        }
        catch (Exception e)
        {
            LOGGER.info("Exception occured while fetching recent ticket");
            throw new RuntimeException(e);
        }
    }

    @Override
    public Object feedbackUpload(Map<String, Object> request) throws Exception
    {
        try{
            ResponseEntity<String> httpResponse = cstService.uploadFeedback(request);
            if(!httpResponse.getStatusCode().equals(HttpStatus.OK)){
                LOGGER.info("Error from CST");
                throw new RuntimeException("Error from CST: " + httpResponse.getBody());
            }
            return httpResponse.getBody();
        }catch (Exception e){
            LOGGER.info("Exception occred while fetching time line {},",e);
            throw new RuntimeException(e);
        }
    }

    private String convertCreatedAtToUTC(String createdAt)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        SimpleDateFormat ldf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date d1 = null;
        try
        {
            Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(createdAt);
            d1 = ldf.parse( sdf.format(date) );
        }
        catch (java.text.ParseException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
            System.out.println(e.getMessage());
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(d1);
    }

    public ResponseUmp getCallDetails() throws IOException, InterruptedException, UMPIntegrationException {
        String mid = SecurityUtils.getCurrentMerchant().getMid();

        Map<String, Object> results = new HashMap<>();

        Map<String, Boolean> devicesRented = getDevicesRentedForCallback(mid);
        Boolean edcRented = devicesRented.get("edcRented");
        Boolean sbRented = devicesRented.get("sbRented");

        results.put("edcRented", edcRented.toString());
        results.put("sbRented", sbRented.toString());

        Boolean midIsWhitelisted = supportHomePageDao.checkMidWhitelistedForCallBack(mid, "2");
        if (!midIsWhitelisted) {
            results.put("isEligible", "false");
            return new ResponseUmp("SUCCESS", "200", "Mid not whitelisted for call back", results);
        }

        String campaignName = getCampaignNameForCallback(edcRented,sbRented);

        Map<String, Object> latestCallDetails = getCallDetailIVR(mid, campaignName);
        results.put("isEligible", "true");
        results.put("outOfSbOperatingHours", outOfSbOperatingHours());
        results.put("maxCallAttempts", MAX_CALL_ATTEMPTS);
        results.put("latestCallDetails", latestCallDetails);

        return new ResponseUmp("SUCCESS", "200", "Callback Details request is successful", results);
    }

    private String getCampaignNameForCallback(Boolean edcRented, Boolean sbRented){
        String campaignName;
        if(edcRented){
            campaignName = CAMPAIGN_NAME_CALLBACK_EDC;
        } else if(sbRented) {
            campaignName = CAMPAIGN_NAME_CALLBACK_SB;
        } else {
            campaignName = CAMPAIGN_NAME_CALLBACK_SB;
        }

        return campaignName;
    }
    private Map<String, Boolean> getDevicesRentedForCallback(String mid) throws UMPIntegrationException, JsonProcessingException {
        String redisKey = redisHelper.generateRedisKey("SoundboxOrEDC_CACHE", mid);
        String devicePreferencesString = redisHelper.fetchDevicePreference(mid,redisKey);
        Map<String,Boolean> devicePreferences = objectMapper.readValue(devicePreferencesString,new TypeReference<Map<String, Boolean>>() {});
        if(Objects.isNull(devicePreferences)){
            throw new UMPIntegrationException("UPS Entity Preferences API failed", IntegrationErrorCodes.UPS_ENTITY_PREFERENCES_FAILURE);
        }
        devicePreferences.put("edcRented", false);
        devicePreferences.put("sbRented", false);

        if(devicePreferences.containsKey("ocl.boss.merchant.edc")){
            devicePreferences.put("edcRented", devicePreferences.remove("ocl.boss.merchant.edc"));
        }
        if(devicePreferences.containsKey("ocl.iot.merchant.soundbox")) {
            devicePreferences.put("sbRented", devicePreferences.remove("ocl.iot.merchant.soundbox"));
        }

        return devicePreferences;
    }

    private Map<String, Object> getCallDetailIVR(String mid, String campaignName) throws InterruptedException, UMPIntegrationException, IOException {
        String custId = SecurityUtils.getCurrentMerchant().getAdminUserId();

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("MID", mid);
        requestBody.put("CUST_ID", custId);
        requestBody.put("CampaignName", campaignName);

        ResponseEntity<String> httpResponse = cstService.getCallDetailIVR(requestBody);
        Map<String, Object> latestCallDetails = null;
        try{
            String httpResponseBody = httpResponse.getBody();
            httpResponseBody = convertToJSONObject(httpResponseBody);
            Map<String, Object> response = objectMapper.readValue(httpResponseBody, Map.class);

            if(response.containsKey("CustDetail")){
                List<Map<String, Object>> custDetail = (List<Map<String, Object>>) response.get("CustDetail");

                LocalDateTime maxAttemptDateTime = null;
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern("yyyy-MM-dd'T'HH:mm:ss")
                        .appendFraction(ChronoField.NANO_OF_SECOND, 0, 9, true) // Optional fractional seconds
                        .toFormatter();

                for(Map<String, Object> currentCallDetail : custDetail) {
                    if (currentCallDetail.containsKey("AttemptDateTime")) {
                        LocalDateTime currentAttemptDateTime = LocalDateTime.parse(currentCallDetail.get("AttemptDateTime").toString(), formatter);

                        if (Objects.isNull(maxAttemptDateTime) || currentAttemptDateTime.isAfter(maxAttemptDateTime)) {
                            maxAttemptDateTime = currentAttemptDateTime;
                            latestCallDetails = currentCallDetail;
                        }
                    } else {
                        latestCallDetails = currentCallDetail;
                        break;
                    }
                }
            }
        } catch(RuntimeException e){
            LOGGER.error("Error occurred while parsing the IVR response e ~~ {}",e.getMessage());
            throw new RuntimeException("Error occurred while parsing IVR response");
        }

        return latestCallDetails;
    }

    private String convertToJSONObject(String httpResponseBody) {
        httpResponseBody = httpResponseBody.substring(1, httpResponseBody.length() - 1);
        httpResponseBody = httpResponseBody.replace("\\", "");

        return httpResponseBody;
    }

    public ResponseUmp requestCallBack(Boolean edcRented, Boolean sbRented) throws Exception {
        String mid = SecurityUtils.getCurrentMerchant().getMid();

        Map<String, Object> currentTicket = getTicketForCallBack(mid);

        String ticketNumber;
        if(Objects.nonNull(currentTicket)){
            ticketNumber = currentTicket.get("id").toString();
        } else {
            currentTicket = createTicketForCallBack(mid);
            ticketNumber = currentTicket.get("ticketNumber").toString();
        }
        LOGGER.info("currentTicket for CallBack: {}", currentTicket);

        String uuid = getUUID(SecurityUtils.getCurrentMerchant().getAdminUserId(), ticketNumber);
        try {
            callMeNowIVR(mid, getCampaignNameForCallback(edcRented, sbRented), uuid);
        } catch (UMPIntegrationException e) {
            updateTicketForCallback(ticketNumber, statusCallSchedulerApiFailed, null);
            throw e;
        }
        updateTicketForCallback(ticketNumber, statusPendingForCallback, uuid);

        Map<String, Object> results = new HashMap<>();
        populateSuccessCallDetailsResult(results);
        return new ResponseUmp("SUCCESS", "200","Callback request is successful",results);
    }

    private void populateSuccessCallDetailsResult(Map<String, Object> results) {
        String userMobile = IND_DIAL_CODE + " " + SecurityUtils.getCurrentMerchant().getMobile();
        results.put("outOfSbOperatingHours", outOfSbOperatingHours());
        results.put("userMobile", userMobile);
        results.put("agentMobile", CALLBACK_AGENT_MOBILE);
    }

    private Map<String, Object> getTicketForCallBack(String mid) throws InterruptedException, IOException, UMPIntegrationException {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("transactionId", TXN_ID_CALLBACK);
        queryParams.put("customerIssueCategoryL1", CUSTOMER_ISSUE_CATEGORY_L1_CALLBACK);
        queryParams.put("cstentity", CST_ENTITY_CALLBACK);

        ResponseEntity<String> httpResponse = cstService.getTickets(mid, queryParams);
        String httpResponseBody = httpResponse.getBody();

        List<Map<String, Object>>  ticketList = objectMapper.readValue(httpResponseBody, ArrayList.class);

        return ticketList.stream()
                .filter(ticket -> ticket.containsKey("status") &&
                        !ticket.get("status").equals(statusClosed) &&
                        !ticket.get("status").equals(statusResolved))
                .findFirst()
                .orElse(null);
    }

    private Map<String, Object> createTicketForCallBack(String mid) throws InterruptedException, IOException, UMPIntegrationException {
        Map<String, Object> requestBody = getRequestBodyForCreateTicket(mid);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("workFlow", "customMerchant");

        ResponseEntity<String> httpResponse = cstService.createTicket(mid, requestBody, queryParams);
        String httpResponseBody = httpResponse.getBody();

        return objectMapper.readValue(httpResponseBody, Map.class);
    }

    private Map<String, Object> getRequestBodyForCreateTicket(String mid) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("freshDeskUrl", freshdeskUrlCallBack);
        requestBody.put("description", DESCRIPTION_CALLBACK);
        requestBody.put("productId", productIdCallBack);
        requestBody.put("subject", SUBJECT_CALLBACK);
        requestBody.put("mhdMID", mid);
        requestBody.put("cstentity", CST_ENTITY_CALLBACK);
        requestBody.put("source", sourceCstCallBack);
        requestBody.put("priority", "1");
        requestBody.put("status", statusPendingForCallback);
        requestBody.put("customerIssueCategoryL1", CUSTOMER_ISSUE_CATEGORY_L1_CALLBACK);
        requestBody.put("mhdTxnId", TXN_ID_CALLBACK);

        return requestBody;
    }

    private void updateTicketForCallback(String ticketNumber, Integer status, String uuid) throws Exception {
        Map<String, Object> requestBody = new HashMap<>();

        Map<String, Object> ticketDetails = new HashMap<>();
        ticketDetails.put("status", status);
        ticketDetails.put("unique_external_id", "P4B" + "-" + SecurityUtils.getCurrentMerchant().getMid());

        if(StringUtils.isNotBlank(uuid)) {
            Map<String, Object> customFields = new HashMap<>();
            customFields.put("cf_ob_uuid", uuid);
            ticketDetails.put("custom_fields", customFields);
        }
        requestBody.put("freshDeskUrl", freshdeskUrlCallBack);
        requestBody.put("ticketDetails", ticketDetails);

        try{
            cstService.updateTicket(ticketNumber, requestBody);
        } catch (RuntimeException e){
            throw new UMPIntegrationException("Ticket update on FD failed", IntegrationErrorCodes.UPDATE_TICKET_FAILURE);
        }
    }

    private String callMeNowIVR(String mid, String campaignName, String uuid) throws InterruptedException, UMPIntegrationException {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("MID", mid);
        requestBody.put("CustId", SecurityUtils.getCurrentMerchant().getAdminUserId());
        requestBody.put("UUID", uuid);
        requestBody.put("Channel", "0");
        requestBody.put("Priority", "0");
        requestBody.put("Campaign", campaignName);
        requestBody.put("CallbackSchedDt", getCallBackDate(campaignName)); // "yyyy-MM-dd HH:mm:ss"

        ResponseEntity<String> response = cstService.callMeIVR(requestBody);
        return response.getBody();
    }

    private String getUUID(String custId, String ticketNumber) {
        // calculate date for DMID
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyyHHmmss");
        String dateDmid = now.format(formatter);

        // generate UUID
        StringBuilder uuid = new StringBuilder();

        int custIdZeros = CUST_ID_MAX_LENGTH - custId.length();
        while(custIdZeros > 0){
            uuid.append("0");
            custIdZeros--;
        }
        uuid.append(custId);

        uuid.append(LANG_CODE_HINDI);
        uuid.append(IVR_MERCHANT_TYPE);
        uuid.append(customerIssueCategoryL1Code);
        uuid.append(IDENTIFIER);

        int ticketZeros = TICKET_NUMBER_MAX_LENGTH - ticketNumber.length();
        while(ticketZeros > 0) {
            uuid.append("0");
            ticketZeros--;
        }
        uuid.append(ticketNumber);

        int dMidZeros = DMID_MAX_LENGTH - dateDmid.length();
        while(dMidZeros > 0) {
            uuid.append("0");
            dMidZeros--;
        }
        uuid.append(dateDmid);

        return uuid.toString();
    }

    private String getCallBackDate(String campaignName) {
        String dateTime = "";
        if(campaignName.equals(CAMPAIGN_NAME_CALLBACK_SB) && outOfSbOperatingHours()) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime eightTwentyNinePmToday = LocalDateTime.of(LocalDate.now(), LocalTime.of(20, 29, 59, 999999999));
            LocalDateTime twelveAmTomorrow = LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MIN);
            LocalDateTime elevenFiftyNinePmYesterday = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MAX);
            LocalDateTime sevenAmToday = LocalDateTime.of(LocalDate.now(), LocalTime.of(7, 00));

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (now.isAfter(elevenFiftyNinePmYesterday) && now.isBefore(sevenAmToday)) {
                dateTime = sevenAmToday.format(formatter);
            } else if (now.isAfter(eightTwentyNinePmToday) && now.isBefore(twelveAmTomorrow)) {
                dateTime = sevenAmToday.plusDays(1).format(formatter);
            }
        }
        return dateTime;
    }

    private Boolean outOfSbOperatingHours(){
        LocalDateTime now = LocalDateTime.now();
        // check if request time is in operation hours
        LocalDateTime sixFiftyNineAm = LocalDateTime.of(now.toLocalDate(), LocalTime.of(6, 59, 59, 999999999));
        LocalDateTime eightThirty = LocalDateTime.of(now.toLocalDate(), LocalTime.of(20, 30));

        // return value
        return !(now.isAfter(sixFiftyNineAm) && now.isBefore(eightThirty));
    }
}
