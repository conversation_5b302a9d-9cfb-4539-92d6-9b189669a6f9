package com.paytm.aggregatorgateway.service.impl;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dto.FAQVideosDto;
import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.enums.IntegrationErrorCodes;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.CstService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
@Slf4j
@Service
public class CstServiceImpl implements CstService {

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private Environment commonProperties;

    @Value("${cst.base.url}")
    private String cstBaseUrl;

    @Value("${cst.callService.base.url}")
    private String cstCallServiceBaseUrl;

    public static Set<String> l1IssueCategorySB;
    public static Set<String> l1IssueCategoryEDC;

    private final String CST_MGW_CALL_DETAIL_ENDPOINT = "/cst-mgw/ivr/1.0.0/ws_PTMEDC/api/PTMEDC/GetCallDetail";
    private final String CST_MGW_CALL_ME_ENDPOINT = "/cst-mgw/ivr/1.0.0/CallMeNowEDC/api/values/InsertDataCallMeAPI";

    @Value("${l1.Issue.Category.SB}")
    public void setl1IssueCategorySB(String str) {
        if (StringUtils.isNotBlank(str))
            l1IssueCategorySB = new HashSet<>(Arrays.asList(str.split(",")));
        else
            l1IssueCategorySB = new HashSet<>();
    }

    @Value("${l1.Issue.Category.EDC}")
    public void setl1IssueCategoryEDC(String str) {
        if (StringUtils.isNotBlank(str))
            l1IssueCategoryEDC = new HashSet<>(Arrays.asList(str.split(",")));
        else
            l1IssueCategoryEDC = new HashSet<>();
    }

    @Override
    public ResponseUmp getTrendingTopics(String language,String source,String verticalId,String tag) throws Exception{
        log.info("In getTrendingTopics impl for mid {}",SecurityUtils.getCurrentMerchant().getMid());
        if(StringUtils.isBlank(source) || StringUtils.isBlank(language) || StringUtils.isBlank(verticalId))
            throw new ValidationException(UMPErrorCodeEnums.MISSING_PARAM," req Params are missing");
        Map<String,String> queryParams = new HashMap<>();
        queryParams.put("language",language);
        queryParams.put("verticalId",verticalId);
        queryParams.put("source",source);
        if(StringUtils.isNotBlank(tag))
            queryParams.put("tag",tag);
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_URL)+  "/cst/care/viewAll/trendingTopic/" + PayTmPGConstants.CST_CLIENT;
        ResponseEntity<String> response = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.GET.name(), queryParams, null, null, String.class);
        if(!response.getStatusCode().is2xxSuccessful()){
            log.error("Received failure from CST");
            throw new RuntimeException("Received failure from CST");
        }
        try{
            Map<String,Object> responseMap = objectMapper.readValue(response.getBody(), new TypeReference<Map<String, Object>>() {});
            List<Map<String,Object>> videoWidgets = extractAllVideoWidget(responseMap);
            List<FAQVideosDto> faqVideos = createFaqVideos(videoWidgets);
            return new ResponseUmp("SUCCESS","200","Successfully fetched details from CST",faqVideos);
        }
        catch(Exception e){
           log.error("Error occurred while parsing the response e ~~ {}",e.getMessage());
            throw new RuntimeException("Error occurred while parsing");
        }
    }
    private List<Map<String,Object>> extractAllVideoWidget(Map<String,Object> cstResponseMap)throws Exception {
        log.info("inside extractVideoWidget for mid {}", SecurityUtils.getCurrentMerchant().getMid());
        List<Map<String,Object>> videoWidgets = new ArrayList<>();
        Map<String, Object> response = MappingUtils.extractMapOrCreateIfNull(cstResponseMap, "response");
        if (response != null) {
            List<Map<String,Object>> template = (List<Map<String,Object>>)response.get("template");
            if (template!=null && !template.isEmpty()){
                Map<String,Object> templateMap = template.get(0);
                List<Map<String,Object>> card = (List<Map<String,Object>>)templateMap.get("card");
                if(card!=null && !card.isEmpty()){
                    Map<String,Object> cardMap = card.get(0);
                    List<Map<String,Object>> tiles = (List<Map<String,Object>>)cardMap.get("tile");
                    if(tiles!=null) {
                        for(Map<String,Object> tile: tiles) {
                            List<Map<String, Object>> widgets = (List<Map<String, Object>>) tile.get("widget");
                            if(widgets!=null)
                            {
                                for (Map<String, Object> widget : widgets) {
                                    if (widget != null && "video".equals(widget.getOrDefault("type", null))) {
                                        videoWidgets.add(widget);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return videoWidgets;
    }
    private List<FAQVideosDto> createFaqVideos(List<Map<String,Object>> videoWidgets) throws Exception{
        log.info("inside fetchFaqVideos for mid {}", SecurityUtils.getCurrentMerchant().getMid());
        List<FAQVideosDto> faqVideos = new ArrayList<>();
        if (videoWidgets != null) {
            for(Map<String,Object> videoWidget : videoWidgets) {
                Map<String, Object> metaData = MappingUtils.extractMapOrCreateIfNull(videoWidget, "metaData");
                if (metaData != null) {
                    int metaDataSize = metaData.size();
                    int videosNum = 1;
                    while (videosNum <= metaDataSize) {
                        FAQVideosDto video = new FAQVideosDto();
                        String src = MapUtils.getString(metaData, "video" + (videosNum++));
                        String title = MapUtils.getString(metaData, "video" + (videosNum++));
                        String thumbnail = MapUtils.getString(metaData, "video" + (videosNum++));
                        video.setSrc((src == null) ? "" : src);
                        video.setTitle((title == null) ? "" : title);
                        video.setThumbnail((thumbnail == null) ? "" : thumbnail);
                        faqVideos.add(video);
                    }
                }
            }
        }
        return faqVideos;
    }

    @Override
    public ResponseUmp getTicketDetails(String encryptedTicketNumber) throws Exception{
        log.info("In getTicketDetails impl for mid {}",SecurityUtils.getCurrentMerchant().getMid());
        if(StringUtils.isBlank(encryptedTicketNumber)){
            throw new ValidationException(UMPErrorCodeEnums.EMPTY_TICKET_NO," req Params are missing");
        }
       String ticketNumber=getDecryptedTicketNumber(encryptedTicketNumber);
       ResponseUmp result = fetchTicketDetails(ticketNumber);
       return result;
    }

    @Override
    public ResponseEntity<String> getAllTicketDetails() throws Exception
    {
        String mid   = SecurityUtils.getCurrentMerchant().getMid();
        log.info("Get all tickets for mid ",mid);
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_URL)+"/crm/fw/v1/tickets" ;
        Map<String ,String> queryMap = new HashMap<>();
        queryMap.put("mhdMID", mid);
        queryMap.put("appClient", "P4B");
        queryMap.put("isCached","true");
        queryMap.put("viewAll","true");
        queryMap.put("excludeAbandonedTickets","true");
        ResponseEntity<String> response = restProcessorDelegate.executeCstRequestHystrix(url,HttpMethod.GET.name(), queryMap,generateCSTHeaders(),null,String.class);
        return response;
    }

    @Override
    public ResponseEntity<String> getTicketTimeLine(String ticketNumber, TicketInfoDTO ticketInfoDTO) throws Exception
    {
        String mid   = SecurityUtils.getCurrentMerchant().getMid();
        log.info("Get all tickets for mid ",mid);
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_MGW_URL)+"/cst-mgw-auth/cst-timeline-service/1.0.0/api/v1/ticket/timeline" ;
        Map<String ,String> queryMap = new HashMap<>();
        queryMap.put("appClient", "P4B");
        queryMap.put("ticketNumber",ticketNumber);
        ResponseEntity<String> response = restProcessorDelegate.executeCstMGWRequestHystrix(url,HttpMethod.POST.name(), queryMap,generateCSTMGWHeaders(),ticketInfoDTO,String.class);
        return response;
    }

    @Override
    public void updateTicket(String ticketNumber, Map<String, Object> requestBody) throws Exception {
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_URL) + "/crm/fw/v1/ticket/update/" + ticketNumber;

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.POST.name(), null, generateCSTHeaders(), requestBody, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful())
        {
            log.error("Received failure from CST : FD -> while updating ticket");
            throw new RuntimeException("Ticket updation on FD failed");
        }
    }

    @Override
    public ResponseEntity<String> uploadFeedback(Map<String, Object> requestBody) throws Exception {
        requestBody.put("uniqueExternalId","P4B-"+SecurityUtils.getCurrentMerchant().getMid());
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_URL) + "/crm/api/v1/csat/journey";
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.POST.name(), null, generateCSTHeaders(), requestBody, String.class);

        return httpResponse;
    }

    public HttpHeaders generateCSTHeaders()
    {
        HttpHeaders headers = new HttpHeaders();
        headers.add("jwt-client-id", commonProperties.getRequiredProperty(PayTmPGConstants.CST_SERVICE_CLIENT_ID));
        String secretKey= AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_SECRET_KEY.getValue());
        headers.add("Authorization", getCSTJwtToken(secretKey));

        return headers;
    }

    public HttpHeaders generateCSTToolsHeaders() throws Exception
    {
        HttpHeaders headers = new HttpHeaders();
        String mgwToken=AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_CALL_SERVICE_MGW_TOKEN.getValue());
        String cstClient = commonProperties.getRequiredProperty(PayTmPGConstants.CST_CALL_SERVICE_CLIENT_ID);
        headers.add("x-mgw-token", mgwToken);
        headers.add("jwt-client-id", cstClient);
        headers.add("Authorization", getCSTCallServiceJwtToken(cstClient));
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    private String getCSTCallServiceJwtToken(String cstClient) throws UnsupportedEncodingException {
        {
            String secretKey = AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_CALL_SERVICE_SECRET_KEY.getValue());
            String token = JWT.create()
                    .withSubject(cstClient).withClaim("sub", cstClient)
                    .withClaim("iat", Instant.now().toEpochMilli())
                    .withIssuedAt(new Date(System.currentTimeMillis()))
                    .sign(Algorithm.HMAC512(secretKey.getBytes("UTF-8")));
            return token;
        }
    }
    private HttpHeaders generateCSTMGWHeaders() {
        HttpHeaders headers=new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        String mgwToken=AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_MGW_TOKEN.getValue());
        headers.add("x-mgw-token", mgwToken);
        headers.add(PayTmPGConstants.SSO_TOKEN, SecurityUtils.getLoggedInUser().getPaytmSSOToken());
        return headers;
    }
    public String getCSTJwtToken(String secretKey)
    {
        String token = Jwts.builder()
                .setSubject(commonProperties.getRequiredProperty("cst.service.client.id"))
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .signWith(SignatureAlgorithm.HS512,secretKey.getBytes(StandardCharsets.UTF_8))
                .compact();
        return token;
    }

    public String getDecryptedTicketNumber(String EncryptedTicketNumber) throws Exception{

        String cstUrl = cstCallServiceBaseUrl + "/api/v1/decrypt";

        Map<String,Object> requestBodyMap = new HashMap<>();
        Map<String,Object> dataReqMap = new HashMap<>();

        dataReqMap.put("ticketNumber", EncryptedTicketNumber);
        requestBodyMap.put("data", dataReqMap);
        HttpHeaders headers=generateCSTToolsHeaders();
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstSeviceCallRequestHystrix(cstUrl, HttpMethod.POST.name(), null, headers,requestBodyMap,String.class);

        if(httpResponse.getStatusCode() == HttpStatus.OK)
        {
            String decryptedTicketNumber = null;
            try {
                Map<String, Object> response = MappingUtils.convertJsonToType(httpResponse.getBody(), Map.class);
                Map<String, Object> dataMap=  MapUtils.getMap(response, "data", null);
                Map<String, Object> decryptedDataMap = MapUtils.getMap(dataMap, "DecryptedData", null);
                decryptedTicketNumber = MapUtils.getString(decryptedDataMap, "ticketNumber", null);
            }
            catch(Exception e)
            {
                log.info("error while parsing cst response");
                throw new ResponseUmpException(UMPErrorCodeEnums.CST_API_FAILED, "failure received from cst api & statusCode : " + httpResponse.getStatusCode());
            }
            if(decryptedTicketNumber == null)
                throw new ResponseUmpException(UMPErrorCodeEnums.CST_API_FAILED, "blank response from cst api & statusCode : "+ httpResponse.getStatusCode());

            return decryptedTicketNumber;
        }
        else {
            throw new ResponseUmpException(UMPErrorCodeEnums.CST_API_FAILED, "failure received from cst api "+httpResponse.getStatusCode());
        }

    }

    public ResponseUmp fetchTicketDetails(String ticketNumber) throws Exception {
        if(StringUtils.isBlank(ticketNumber))
            throw new ValidationException(UMPErrorCodeEnums.MISSING_PARAM," req Params are missing");
        Map<String,String> queryParams = new HashMap<>();
        HttpHeaders headers=generateCSTHeaders();
        String freshdeskUrl=commonProperties.getRequiredProperty(DomainConstants.FRESHDESK_URL);
        headers.add("freshdesk-url",freshdeskUrl);
        queryParams.put("ticketNumber",ticketNumber);
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_URL)+  "/crm/fw/v1/viewTicket";
        ResponseEntity<String> response = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.GET.name(), queryParams, headers, null, String.class);
        if(!response.getStatusCode().is2xxSuccessful()){
            log.error("Received failure from CST");
            throw new RuntimeException("Received failure from CST");
        }
        try{
            Map<String,Object> responseMap = objectMapper.readValue(response.getBody(), new TypeReference<Map<String, Object>>() {});
            log.info("Fetched ticketDetails "+ responseMap);

            if(responseMap.containsKey("l1IssueCategory")) {
                if (l1IssueCategorySB.contains(responseMap.get("l1IssueCategory").toString())) {
                    responseMap.put("entityType", "Soundbox Issue");
                } else if (l1IssueCategoryEDC.contains(responseMap.get("l1IssueCategory").toString())) {
                    responseMap.put("entityType", "EDC Issue");
                }
            }
            return new ResponseUmp("SUCCESS","200","Successfully fetched details from CST",responseMap);
        }
        catch(Exception e){
            log.error("Error occurred while parsing the response e ~~ {}",e.getMessage());
            throw new RuntimeException("Error occurred while parsing");
        }
    }

    public ResponseEntity<String> getTickets(String mid, Map<String, String> queryParams) throws InterruptedException, UMPIntegrationException {
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_URL) + "/crm/fw/v2/tickets";

        queryParams.put("appClient", "P4B");
        queryParams.put("mhdMID", mid);
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.GET.name(), queryParams, generateCSTHeaders(), null, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Received failure from CST : FD -> while fetching tickets");
            throw new UMPIntegrationException("Get tickets from FD failed", IntegrationErrorCodes.GET_TICKET_FAILURE);
        }

        return httpResponse;
    }

    @Override
    public ResponseEntity<String> createTicket(String mid, Map<String, Object> requestBody, Map<String, String> queryParams) throws InterruptedException, UMPIntegrationException {
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_URL) + "/crm/fw/v1/ticket";

        queryParams.put("appClient", "P4B");
        queryParams.put("mhdMID", mid);
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstRequestHystrix(url, HttpMethod.POST.name(), queryParams, generateCSTHeaders(), requestBody, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Received failure from CST : FD -> while creating ticket");
            throw new UMPIntegrationException("Ticket creation on FD failed", IntegrationErrorCodes.CREATE_TICKET_FAILURE);
        }

        return httpResponse;
    }

    public ResponseEntity<String> getCallDetailIVR(Map<String, Object> requestBody) throws UMPIntegrationException, InterruptedException {
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_MGW_URL) + CST_MGW_CALL_DETAIL_ENDPOINT;

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstMGWRequestHystrix(url, HttpMethod.POST.name(), null, generateCSTMGWHeaders(), requestBody, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Received failure from MGW -> while getting call Details from IVR");
            throw new UMPIntegrationException("IVR GetCallDetail API on MGW failed", IntegrationErrorCodes.IVR_CALL_DETAIL_FAILURE);
        }

        return httpResponse;
    }

    public ResponseEntity<String> callMeIVR(Map<String, Object> requestBody) throws UMPIntegrationException, InterruptedException {
        String url = commonProperties.getRequiredProperty(DomainConstants.CST_MGW_URL) + CST_MGW_CALL_ME_ENDPOINT;

        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstMGWRequestHystrix(url, HttpMethod.POST.name(), null, generateCSTMGWHeaders(), requestBody, String.class);
        if (!httpResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Received failure from MGW -> while arranging call on IVR");
            throw new UMPIntegrationException("IVR CallMe API on MGW failed", IntegrationErrorCodes.IVR_CALL_ME_FAILURE);
        }

        return httpResponse;
    }
}
