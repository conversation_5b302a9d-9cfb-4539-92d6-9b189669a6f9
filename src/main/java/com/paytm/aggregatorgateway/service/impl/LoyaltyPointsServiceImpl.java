package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.LoyaltyPointsService;
import com.paytm.aggregatorgateway.utils.HashUtils;
import com.paytm.aggregatorgateway.utils.JwtUtil;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.paytm.aggregatorgateway.vo.ResultInfo;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Service
public class LoyaltyPointsServiceImpl implements LoyaltyPointsService {
	private static final Logger LOGGER = LogManager.getLogger(LoyaltyPointsServiceImpl.class);

	@Autowired
	private ObjectMapper jsonMapper;

	@Autowired
	private RestProcessorDelegate restProcessorDelegate;

	@Value("${kyb.base.url}")
	private String kybBaseUrl;

	@Value("${kyb.client.id}")
	private String kybClientId;

	@Value("${kyb.client.uid}")
	private String kybClientUserId;

	@Value("${pg.reward.base.url}")
	private String rewardBaseUrl;
	
	@Value("${pg.reward.client.id}")
	private String rewardClientId;

	@Override
	public ResponseUmp getSummary(String uid) throws Exception {
		LOGGER.info("In getSummaryfor  uid : {}", uid);
		ResponseUmp result = new ResponseUmp();
		if (uid == null) {
			result.setStatusCode("UMP-200");
			result.setStatus("SUCCESS");
			result.setStatusMessage("kyb mapping does not exist");
			Map<String, String> body = new HashMap<String, String>();
			body.put("activeBalance", "0");
			body.put("exchangeRate", "0");
			result.setResults(body);
			return result;
		}

		String url = rewardBaseUrl + "/fund-service/fundproxy/loyaltypoints/v2/checkbalance";
		
		Map<String, String> body = new HashMap<String, String>();
		body.put("userId", uid);
		Map<String, Object> payload = generatePayload(body);

		HttpHeaders headers = getHeaders();
		String reqBody = jsonMapper.writeValueAsString(payload);
		String jwtToken = createRewardJwtToken(reqBody);
		headers.add("x-jwt-token", jwtToken);

		ResponseEntity<String> httpResponse = restProcessorDelegate.executeRewardsRequestHystrix(url,HttpMethod.POST.name(),null, headers,
				reqBody,String.class);

		int statusCode = httpResponse.getStatusCodeValue();
		if (statusCode != 200) {
			LOGGER.info("rewards status code : {}", statusCode);
			throw new ValidationException(UMPErrorCodeEnums.REWARD_FAILED_RESPONSE);
		}
		Map<String, Object> map = jsonMapper.readValue(httpResponse.getBody(), Map.class);
		try {
			Map level1 = MapUtils.getMap(map, "response");
			Map level2 = MapUtils.getMap(level1, "result");
			result.setStatusCode(statusCode + "");
			result.setStatus(level2.get("resultCode").toString());
			result.setStatusMessage(level2.get("resultMsg").toString());
			result.setResults(level1);
		} catch (NullPointerException ex) {
			throw new ValidationException(UMPErrorCodeEnums.REWARD_INVALID_RESPONSE);
		}

			return result;
	}
	
	@Override
	public String checkBalance(String uid) throws Exception {
		LOGGER.info("In getSummaryfor  uid : {}", uid);
		ResultInfo result = new ResultInfo();
		if (uid == null) {
			result.setResultCode("SUCCESS");
			result.setResultCodeId("00000000");
			result.setResultMsg("kyb mapping does not exist");
			result.setResultStatus("S");
			Map<String,Object> resp = new HashMap<>();
			Map<String,Object> resulMap = new HashMap<>();
			resulMap.put("result", result);
			resp.put("response", resulMap);
			return MappingUtils.convertObjectToJson(resp);
		}
		String url = rewardBaseUrl + "/fund-service/fundproxy/loyaltypoints/v2/checkbalance";
		Map<String, String> body = new HashMap<String, String>();
		body.put("userId", uid);
		Map<String, Object> payload = generatePayload(body);
		HttpHeaders headers = getHeaders();
		String reqBody = jsonMapper.writeValueAsString(payload);
		String jwtToken = createRewardJwtToken(reqBody);
		headers.add("x-jwt-token", jwtToken);
		ResponseEntity<String> httpResponse = restProcessorDelegate.executeRewardsRequestHystrix(url,HttpMethod.POST.name(),null, headers,
				reqBody,String.class);
		int statusCode = httpResponse.getStatusCodeValue();
		LOGGER.info("rewards status code : {}", statusCode);
		return httpResponse.getBody();
	}
	

	private Map<String, Object> generatePayload(Map body) {
		Map<String, Object> payload = new HashMap<String, Object>();
		Map<String, Object> request = new HashMap<String, Object>();
		request.put("body", body);
		payload.put("request", request);

		return payload;
	}

	@Cacheable(value = "kybCustId", key = "#mid", unless="#result==null",cacheManager = "ehCacheCacheManager")
	public String fetchUid(String mid) throws Exception {

		LOGGER.info("In fetchUid");
		ResponseUmp result = new ResponseUmp();
		/// fetch uid from kyb
		Map<String, String> queryParam = new HashMap<String, String>();
		queryParam.put("pgMid", mid);
		String token = createJwtToken();

		HttpHeaders headerParams = new HttpHeaders();
		headerParams.setContentType(MediaType.APPLICATION_JSON);
		headerParams.add("x-jwt-token", token);
		String kybUrl = kybBaseUrl + "/kyb/getRootCustid";
		ResponseEntity<String> kybHttpResponse = restProcessorDelegate.executeKybRequestHystrix(kybUrl,HttpMethod.GET.name(), queryParam, headerParams,null, String.class);
		// return kybHttpResponse;
		Map<String, Object> kybMap = null;
		int kybStatusCode = kybHttpResponse.getStatusCodeValue();
		if (kybStatusCode == 200)
			kybMap = jsonMapper.readValue(kybHttpResponse.getBody(), Map.class);
		if (kybMap == null)
			throw new ValidationException(UMPErrorCodeEnums.KYB_INVALID_RESPONSE);
		if (kybMap.containsKey("custId"))
			return kybMap.get("custId").toString();
		return null;
	}

	@Override
	public ResponseUmp getList(Map<String, Object> reqMap, String uid) throws Exception {
		LOGGER.info("In getList for  uid : {}", uid);

		ResponseUmp result = new ResponseUmp();
		if (uid == null) {
			result.setStatusCode("UMP-200");
			result.setStatus("SUCCESS");
			result.setStatusMessage("kyb mapping does not exist");
			Map<String, Object> body = new HashMap<String, Object>();
			Map<String, Object> response = new HashMap<String, Object>();
			response.put("loyaltyPoints", Collections.emptyList());
			body.put("response", response);
			result.setResults(body);
			return result;
		}
		String url = rewardBaseUrl + "/fund-service/fundproxy/loyaltypoints/v2/passbook";
		Map<String, Object> body = new HashMap<String, Object>();
		body.put("userId", uid);
		body.put("pageNum", reqMap.get("pageNum"));
		body.put("pageSize", reqMap.get("pageSize"));
		if (reqMap.get("transactionType") != null)
			body.put("transactionType", reqMap.get("transactionType"));
		if (reqMap.get("accountingType") != null)
			body.put("accountingType", reqMap.get("accountingType"));

		Map<String, Object> payload = generatePayload(body);

		HttpHeaders headers = getHeaders();
		String reqBody = jsonMapper.writeValueAsString(payload);
		String jwtToken = createRewardJwtToken(reqBody);

		headers.add("x-jwt-token", jwtToken);

		ResponseEntity<String> httpResponse = restProcessorDelegate.executeRewardsRequestHystrix(url,HttpMethod.POST.name(),null, headers,
				reqBody,String.class);

		int statusCode = httpResponse.getStatusCodeValue();
		if (statusCode != 200) {
			LOGGER.info("rewards status code : {}", statusCode);
			throw new ValidationException(UMPErrorCodeEnums.REWARD_FAILED_RESPONSE);
		}
		Map<String, Object> map = jsonMapper.readValue(httpResponse.getBody(), Map.class);
		try {
			Map level1 = MapUtils.getMap(map, "response");
			Map level2 = MapUtils.getMap(level1, "result");
			result.setStatusCode(statusCode + "");
			result.setStatus(level2.get("resultCode").toString());
			result.setStatusMessage(level2.get("resultMsg").toString());
			result.setResults(map);
		} catch (NullPointerException ex) {
			throw new ValidationException(UMPErrorCodeEnums.REWARD_INVALID_RESPONSE);
		}
		return result;
	}

	private HttpHeaders getHeaders() {
		HttpHeaders headerParams = new HttpHeaders();
		headerParams.setContentType(MediaType.APPLICATION_JSON);
		headerParams.add("clientid", rewardClientId);
		return headerParams;
	}

	private String createJwtToken() {
		Map<String, String> claims = new HashMap<>();
		claims.put("ts", String.valueOf(System.currentTimeMillis()));
		claims.put("cust_id", kybClientUserId);
		claims.put("client_id", kybClientId);
		return JwtUtil.createJwtTokenHS256(claims, null, AWSSecretManager.awsSecretsMap.get(AWSSecrets.KYB_CLIENT_SECRET.getValue()));
	}

	private String createRewardJwtToken(String body) {
		String bodyHash = HashUtils.generateHash(body);

		Algorithm algorithm = Algorithm.HMAC256(Base64.getDecoder().decode(AWSSecretManager.awsSecretsMap.get(AWSSecrets.PG_REWARD_SECRET_KEY.getValue())));

		String token = JWT.create().withClaim("ts", System.currentTimeMillis())
				.withClaim("requestBodyHash", bodyHash).sign(algorithm);
		return token;
	}
}
