package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.controller.v1.AddressUpdateAPI;
import com.paytm.aggregatorgateway.dao.HomepageWidgetDao;
import com.paytm.aggregatorgateway.dto.Address;
import com.paytm.aggregatorgateway.dto.AddressUpdateDTO;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.AddressUpdateService;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
@Service
public class AddressUpdateServiceImpl implements AddressUpdateService {

    private final Logger LOGGER = LogManager.getLogger(AddressUpdateServiceImpl.class);

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private Environment environment;

    @Autowired
    private ObjectMapper jacksonObjectMapper;

    @Autowired
    private HomepageWidgetDao homepageWidgetDao;
    @Override
    public ResponseUmp updateAddress(AddressUpdateDTO addressUpdateDTO) throws Exception {
        String mid=SecurityUtils.getCurrentMerchant().getMid();
        String ticketNo=addressUpdateDTO.getTicketNumber();
        if(checkAddress(ticketNo)){
            Date cardExpiryTimeToSet = new Date(System.currentTimeMillis());
            homepageWidgetDao.updateStatusAndFlag(mid,ticketNo,null,PayTmPGConstants.UPDATE_TICKET_ADDRESS,PayTmPGConstants.InActiveStatus,cardExpiryTimeToSet,PayTmPGConstants.ActiveStatus,"CARD", null);
            LOGGER.info("address already updated against the ticket:{}",ticketNo);
           throw new ResponseUmpException(UMPErrorCodeEnums.ADDRESS_ALREADY_UPDATED);
        }
        Address address=addressUpdateDTO.getAddress();
        String url= environment.getRequiredProperty(DomainConstants.CST_MGW_URL)+"/cst-mgw/crm/1.0.0/fw/v1/ticket/update/"+ticketNo;
        Map<String,Object> requestBody=new HashMap<>();
        Map<String,Object> ticketDetails=new HashMap<>();
        Map<String,Object> customFields=new HashMap<>();

        customFields.put("cf_address_line_2",address.getAddress_line_2());
        customFields.put("cf_district",address.getCity());
        customFields.put("cf_state",address.getState());
        customFields.put("cf_latitude",address.getLatitude());
        customFields.put("cf_longitude",address.getLongitude());
        customFields.put("cf_postal_code",address.getPostalCode());

        ticketDetails.put("unique_external_id","P4B-"+SecurityUtils.getCurrentMerchant().getMid());
        ticketDetails.put("custom_fields",customFields);
        String freshdeskUrl=environment.getRequiredProperty(DomainConstants.FRESHDESK_URL);

        requestBody.put("freshDeskUrl",freshdeskUrl);
        requestBody.put("ticketDetails",ticketDetails);
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstMGWRequestHystrix(url,
                    HttpMethod.POST.name(), new HashMap<>(), generateCSTMGWHeaders(), requestBody, String.class);
        if (httpResponse.getStatusCode().is2xxSuccessful()) {
            ResponseUmp responseUmp = new ResponseUmp();
            responseUmp.setStatus("SUCCESS");
            responseUmp.setStatusCode("200");
            responseUmp.setStatusMessage("Address updated successfully");
            Date cardExpiryTimeToSet = new Date(System.currentTimeMillis());
            homepageWidgetDao.updateStatusAndFlag(mid,ticketNo,null,PayTmPGConstants.UPDATE_TICKET_ADDRESS,PayTmPGConstants.InActiveStatus,cardExpiryTimeToSet,PayTmPGConstants.ActiveStatus,"CARD",null);
            return responseUmp;
        }
            else {
                throw new RuntimeException();
            }

    }

    private HttpHeaders generateCSTMGWHeaders() {
        HttpHeaders headers=new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        String mgwToken=AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_MGW_TOKEN.getValue());
        headers.add("x-mgw-token", mgwToken);
        headers.add(PayTmPGConstants.SSO_TOKEN, SecurityUtils.getLoggedInUser().getPaytmSSOToken());
        return headers;
    }
    private HttpHeaders generateCSTHeaders()
    {
        HttpHeaders headers = new HttpHeaders();
        headers.add("jwt-client-id", environment.getRequiredProperty(PayTmPGConstants.CST_SERVICE_CLIENT_ID));
        String secretKey=AWSSecretManager.awsSecretsMap.get(AWSSecrets.CST_SECRET_KEY.getValue());
        headers.add("Authorization", getCSTJwtToken(secretKey));

        return headers;
    }
    private String getCSTJwtToken(String secretKey)
    {
        String token = Jwts.builder()
                .setSubject(environment.getRequiredProperty("cst.service.client.id"))
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .signWith(SignatureAlgorithm.HS512,secretKey.getBytes(StandardCharsets.UTF_8))
                .compact();
        return token;
    }

    private boolean checkAddress(String ticketNo) throws InterruptedException, IOException {
        String url=environment.getRequiredProperty(DomainConstants.CST_URL)+"/crm/fw/v1/viewTicket?ticketNumber="+ticketNo;
        HttpHeaders headers=generateCSTHeaders();
        String freshdeskUrl=environment.getRequiredProperty(DomainConstants.FRESHDESK_URL);
        headers.add("freshdesk-url",freshdeskUrl);
        Map<String,String> queryParams = new HashMap<>();
        ResponseEntity<String> httpResponse = restProcessorDelegate.executeCstRequestHystrix(url,
                HttpMethod.GET.name(), queryParams, headers, null, String.class);
        if(httpResponse.getStatusCode().is2xxSuccessful())
        {
            Map<String,Object> responseBody=new HashMap<>();

            if (Objects.nonNull(httpResponse)) {
                JsonNode httpResponseBody = jacksonObjectMapper.readTree(httpResponse.getBody());
                responseBody = jacksonObjectMapper.convertValue(httpResponseBody,
                        new TypeReference<Map<String,Object>>() {
                        });
                if((responseBody.containsKey("addressLine1") && StringUtils.isNotBlank("addressLine1")) || responseBody.containsKey("addressLine2") && StringUtils.isNotBlank("addressLine2"))
                    return true;

            }
        }
        return false;
    }
}
