package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.OMSService;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class OMSServiceImpl implements OMSService, DomainConstants {

    private static final Logger log = LogManager.getLogger(OMSServiceImpl.class);

    @Autowired
    private Environment env;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private ObjectMapper objectMapper;

    public String generateToken() throws Exception {
        log.info("Generating token for checkout");
        String clientKey = env.getRequiredProperty("checkout.client.key");
        String clientSecret = AWSSecretManager.awsSecretsMap.get(AWSSecrets.CHECKOUT_SECRET_KEY.getValue());
        if(StringUtils.isBlank(clientSecret))
            clientSecret=env.getRequiredProperty("checkout.secret");
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("clientKey", clientKey);
        requestBody.put("clientSecret", clientSecret);
        String url = env.getRequiredProperty(DomainConstants.CHECKOUT_BASE_URL)+"/v1/authorize";

        try {
            ResponseEntity<String> httpResponse = restProcessorDelegate.executeCheckoutRequestHystrix(url,"POST", null, headers, requestBody, String.class);
            if(httpResponse.getStatusCode() == HttpStatus.OK) {
                Map<String, String> map = objectMapper.readValue(httpResponse.getBody(), Map.class);
                if(map.get("access_token") != null)
                    return map.get("access_token");
                else
                    throw new RuntimeException("Access Token creation failed");
            }
        } catch (Exception e) {
            log.error("Exception at fetching token :{}",e);
            throw e;
        }

        return null;
    }

    @Override
    public void acknowledgeOrder(String orderId) throws Exception {
        String accessToken = generateToken() ;
        String url = env.getRequiredProperty(DomainConstants.OMS_BASE_URL) + "/v4/admin/fulfillment/ack/" + orderId;  //TODO check grafana maping
        Map<String ,Object> requestBody = new HashMap<>();
        requestBody.put("status",246);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.add("access_token", accessToken);

        try {
            ResponseEntity<String> httpResponse = restProcessorDelegate.executeOMSRequestHystrix(url, "POST", null, headers, requestBody, String.class);
            if (!httpResponse.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to acknowledge order: {}", orderId);
                throw new RuntimeException("Failed to acknowledge order");
            }
        } catch (Exception e) {
            log.error("Exception while acknowledging order: {}", e);
            throw e;
        }
    }
} 