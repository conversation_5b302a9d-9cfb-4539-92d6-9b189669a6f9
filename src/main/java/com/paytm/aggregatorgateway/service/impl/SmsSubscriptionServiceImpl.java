package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.google.common.base.Preconditions;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.ErrorCodeConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dto.SmsSubscriptionDTO;
import com.paytm.aggregatorgateway.dto.UpdateSubscriptionStatusDTO;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.service.SmsSubscriptionService;
import com.paytm.aggregatorgateway.service.SubscriptionService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.metrics.MetricConstants;
import com.paytm.aggregatorgateway.utils.metrics.MetricUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class SmsSubscriptionServiceImpl implements SmsSubscriptionService {

    private static final Logger LOGGER = LogManager.getLogger(SmsSubscriptionServiceImpl.class);

    @Value("${sms.subscription.segment.id}")
    private String segmentId;

    @Value("${cleverTap.context}")
    private String context;

    @Autowired
    MetricUtils metricUtils;

    @Autowired
    private Environment env;

    @Autowired
    private MerchantProfileService merchantProfileService;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private SubscriptionService subscriptionService;

    String successUpdateResponse = "{\n"
            + "\"status\": \"Success\",\n"
            + "\"statusCode\": \"200\",\n"
            + "\"results\": \"User Subscriptions Updated Successfully\"\n"
            + "}";


    public String fetchIdFromCleverTap(String client, String custId, String bannerId) throws Exception
    {
        LOGGER.info("Inside fetchIdFromCleverTap method with custId {}, bannerId {}",custId,bannerId);
        String url = env.getRequiredProperty(DomainConstants.CLEVERTAP_BASE_URL) + "/pull";
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-CleverTap-Account-Id", AWSSecretManager.awsSecretsMap.get(AWSSecrets.CLEVERTAP_ACCOUNTID.getValue()));
        headers.add("X-CleverTap-Passcode", AWSSecretManager.awsSecretsMap.get(AWSSecrets.CLEVERTAP_PASSCODE.getValue()));
        headers.add("Host","in1-paytm.api.clevertap.com");
        headers.add("Content-Type", "application/json");
        if(StringUtils.isNotBlank(client))
        {
            if("androidapp".equals(client) || "android".equals(client))
                client = "Android";
            else
                client = "iOS";
        }

        Map<String,Object> requestBody = new HashMap<>();
        requestBody.put("Identity", custId);
        requestBody.put("device", client);
        List<Map<String,Object>> contexts = new ArrayList<>();
        Map<String,Object> contextMap = new HashMap<>();
        contextMap.put("context", context);
        contexts.add(contextMap);
        requestBody.put("contexts", contexts);

        //ResponseEntity<String> responseEntity = restProcessorDelegate.executeGenericRequest(url,HttpMethod.POST.name(), null, headers,requestBody, String.class);
        ResponseEntity<String> responseEntity=restProcessorDelegate.executeClevertapRequestHystrix(url, HttpMethod.POST.name(),null,headers,requestBody,String.class);
        if(!responseEntity.getStatusCode().is2xxSuccessful())
        {
            //LOGGER.info("Error From CleverTap statusCode:{} and response {}",responseEntity.getStatusCode().toString(), responseEntity.getBody());
            metricUtils.pushCounterMetrics(MetricConstants.MetricsName.CLEVERTAP_ERROR, metricUtils.createClevertapTags(ErrorCodeConstants.CLEVERTAP_ERROR_NON_2XX));
            return null;
        }

        String responseBody = responseEntity.getBody();

        try
        {
            Map<String, Object> responseMap = MappingUtils.convertJsonToMap(responseBody);
            String status = MapUtils.getString(responseMap, "status", null);
            if("fail".equals(status)) {
                LOGGER.info("FAILED status received from clevertap.");
                metricUtils.pushCounterMetrics(MetricConstants.MetricsName.CLEVERTAP_ERROR, metricUtils.createClevertapTags(ErrorCodeConstants.CLEVERTAP_ERROR_FAILED_STATUS));
                return null;
            }

            Map<String,Object> adUnit_notifs = MapUtils.getMap(responseMap, "adUnit_notifs", null);
            List<Map<String,Object>> bannerDetailsList = (List<Map<String, Object>>) MapUtils.getObject(adUnit_notifs,context, null);
            for(Map<String,Object> bannerDetails : bannerDetailsList)
            {
                Map<String,Object> custom = MapUtils.getMap(bannerDetails,"custom_kv",null);
                String bannerNumber = MapUtils.getString(custom, "bannerId", null);
                if(bannerId.equals(bannerNumber))
                    return bannerNumber;
            }
        }
        catch(Exception e)
        {
            LOGGER.error("Error occured in processing the clevertap response{}", e);
            metricUtils.pushCounterMetrics(MetricConstants.MetricsName.CLEVERTAP_ERROR, metricUtils.createClevertapTags(ErrorCodeConstants.CLEVERTAP_ERROR_EXCEPTION));
            return null;
        }

        metricUtils.pushCounterMetrics(MetricConstants.MetricsName.CLEVERTAP_ERROR, metricUtils.createClevertapTags(ErrorCodeConstants.CLEVERTAP_ERROR_RETURN_NULL));
        return null;
    }

    @Override
    public String updateSubscriptionStatus(UpdateSubscriptionStatusDTO updateStatusDTO, Boolean retry, String client) throws Exception
    {

        LOGGER.info("Into updateSubscriptionStatus method with retry {}",retry);

        String url = env.getRequiredProperty(DomainConstants.SUBSCRIPTION_BASE_URL) + "/api/v1/subscription";

        HttpHeaders headers = getSubscriptionHeaders();

        if((updateStatusDTO.getStatus()).equals("RESUME"))
            updateStatusDTO.setInstantSmsEnable(true);

        if(updateStatusDTO.getStatus().equals("SUSPEND")) {
            updateStatusDTO.setTempSuspend(true);
        }

        ResponseEntity<String> responseEntity = restProcessorDelegate.executeSubscriptionRequestHystrix(url,HttpMethod.PUT.name(), null, headers,updateStatusDTO, String.class);
        String response = responseEntity.getBody();


        try {
            if(retry && "SMS_CHARGE".equals(updateStatusDTO.getServiceName())  && response.contains("FAILURE")){
                LOGGER.info("Inside fallback for sms alerts.. Original resposnse " + response);
                metricUtils.pushCounterMetrics(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/subscribe", "PUT", ErrorCodeConstants.SMS_RETRY));
                return checkAndUpdateSubscriptionFlow(updateStatusDTO, client);
            }
        }catch(Exception e) {
            metricUtils.pushCounterMetrics(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/subscribe", "PUT", ErrorCodeConstants.SMS_RETRY_FAILED));
            LOGGER.error("Failed again ~~ " + e.getMessage(),e);
        }

        return(response);
    }

    public String checkAndUpdateSubscriptionFlow(UpdateSubscriptionStatusDTO updateStatusDTO, String client) throws Exception
    {
        final String mid = updateStatusDTO.getMid();
        final String usn = updateStatusDTO.getUsn();
        final String serviceName = updateStatusDTO.getServiceName();
        final String subscriptionType = updateStatusDTO.getSubscriptionType();
        final String custId = updateStatusDTO.getCustId();
        final String smsStatus= updateStatusDTO.getStatus();
        Map<String, Object> alertsStatusOnBoss = merchantProfileService.getCommunicationConfiguration(mid, "ALL", client);
        boolean smsAlertsOnBoss = getSmsAlertsStatusOnBoss(alertsStatusOnBoss);

        LOGGER.info("Into checkAndUpdateSubscription method with mid {} , usn {} , servicename {}, subscriptiontype {},custId {},smsStatus {} client {}",
                mid, usn, serviceName, subscriptionType,custId,smsStatus,client);
        Preconditions.checkArgument(StringUtils.isNoneBlank(mid,usn,serviceName,subscriptionType,custId,smsStatus),"mid, usn, servicename, subscriptiontype,custId,smsStatus is mandatory to proceed" );
        String response = null;
        switch(smsStatus)
        {
            case "RESUME":
                response = updateWithResumeFlow(mid,usn,serviceName,subscriptionType,custId,smsStatus,client,smsAlertsOnBoss, alertsStatusOnBoss);
                break;
            case "SUSPEND":
                response =  updateWithSuspendFlow(mid,usn,serviceName,subscriptionType,custId,smsStatus,client,smsAlertsOnBoss, alertsStatusOnBoss);
                break;
        }

        return response;
    }

    private HttpHeaders getSubscriptionHeaders() throws IllegalArgumentException, UnsupportedEncodingException {
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json");
        headers.add("x-client-id", "ump");
        headers.add("x-client-token", jwtToken());
        return headers;
    }

    private String jwtToken() throws IllegalArgumentException, UnsupportedEncodingException {
        byte[] decodedKey = Base64.getDecoder().decode(AWSSecretManager.awsSecretsMap.get(AWSSecrets.SUBSCRIPTION_RENTAL_JWT_SECRET.getValue()));
        Algorithm algorithm = Algorithm.HMAC512(decodedKey);
        String token = JWT.create().withIssuedAt(new Date()).withClaim("client-id", "ump").sign(algorithm);
        return token;
    }

    private boolean getSmsAlertsStatusOnBoss(Map<String, Object> alertsStatusOnBoss) throws Exception {
        LOGGER.info("All alerts status on boss : " + alertsStatusOnBoss);
        //get sms alert status on boss
        Map<String, Object> notifications = MapUtils.getMap(alertsStatusOnBoss, "notifications", null);
        Map<String, Object> updatedTransaction = MapUtils.getMap(notifications, "transaction", null);
        Map<String, Object> updatedRefund = MapUtils.getMap(notifications, "refund", null);
        boolean txnSms = MapUtils.getBoolean(updatedTransaction, "smsAllowed", null);
        boolean refundSms = MapUtils.getBoolean(updatedRefund, "smsAllowed", null);
        return txnSms && refundSms;
    }

    private String updateWithSuspendFlow(String mid, String usn, String serviceName, String subscriptionType,
                                         String custId, String status, String client, boolean smsAlertsOnBoss,
                                         Map<String, Object> alertsStatusOnBoss) throws Exception {
        LOGGER.info("Inside updateWithSuspendFlow method");
        Preconditions.checkArgument(smsAlertsOnBoss,"SMS alerts on BOSS should have been ON to reach here");
        String subscriptionStatus = "ACTIVE";
        Map<String,Object> fetchResponse = subscriptionService.fetchSubscription(mid,mid,serviceName,subscriptionType,null,subscriptionStatus);
        //if subscription is active
        if(fetchResponse.get("statusCode").equals("200"))
        {
            UpdateSubscriptionStatusDTO request = new UpdateSubscriptionStatusDTO();
            request = createUpdateData(mid,custId,usn,serviceName,subscriptionType,status);
            return updateSubscriptionStatus(request,false,client);
        }
        //else if no active subscription exists OR no subscription exists
        else
        {
            updateOnBoss(mid, null,false, alertsStatusOnBoss);
            return successUpdateResponse;
        }
    }

    private String updateWithResumeFlow(String mid, String usn, String serviceName, String subscriptionType,
                                        String custId, String status, String client, boolean smsAlertsOnBoss,
                                        Map<String, Object> alertsStatusOnBoss) throws Exception {
        LOGGER.info("Inside updateWithResumeFlow method");
        String subscriptionStatus = "SUSPEND";
        Map<String,Object> fetchResponse = subscriptionService.fetchSubscription(mid,mid,serviceName,subscriptionType,null,subscriptionStatus);
        if(fetchResponse.get("statusCode").equals("200")) {
           Preconditions.checkArgument(smsAlertsOnBoss,"SMS alerts on BOSS should have been ON to reach here");
            UpdateSubscriptionStatusDTO request = new UpdateSubscriptionStatusDTO();
            request = createUpdateData(mid,custId,usn,serviceName,subscriptionType,status);
            return updateSubscriptionStatus(request,false,client);
        }
        else {
            Preconditions.checkArgument(!smsAlertsOnBoss,"SMS alerts on BOSS should have been OFF to reach here");
            String id = fetchIdFromCleverTap(client,custId,segmentId);
            String response = null;
            if(StringUtils.isNotBlank(id) && id.equals(segmentId))
            {
                LOGGER.info("inside creating subscription call");
                SmsSubscriptionDTO request = createSubscriptionData(mid, custId);
                response = createSubscription(request);
            }
            else
            {
                updateOnBoss(mid, null,true, alertsStatusOnBoss);
                response = successUpdateResponse;
            }
            return response;
        }

    }
    @Override
    public String createSubscription(SmsSubscriptionDTO smsSubscriptionDTO) throws Exception
    {
        LOGGER.info("Into createSubscription method");
        String url = env.getRequiredProperty(DomainConstants.SUBSCRIPTION_BASE_URL) + "/api/v1/subscription";
        Map<String,Object> requestBody = new HashMap<>();
        requestBody.put("mid",smsSubscriptionDTO.getMid());
        requestBody.put("custId",smsSubscriptionDTO.getCustId());
        requestBody.put("usn",smsSubscriptionDTO.getUsn());
        requestBody.put("subscriptionType",smsSubscriptionDTO.getSubscriptionType());
        requestBody.put("serviceName",smsSubscriptionDTO.getServiceName());
        requestBody.put("serviceType",smsSubscriptionDTO.getServiceType());
        requestBody.put("planPrice",smsSubscriptionDTO.getPlanPrice());
        requestBody.put("frequency",smsSubscriptionDTO.getFrequency());
        requestBody.put("onboardingDate",smsSubscriptionDTO.getOnboardingDate());

        if(StringUtils.isNotBlank(smsSubscriptionDTO.getPhoneNumber()))
            requestBody.put("phoneNumber",smsSubscriptionDTO.getPhoneNumber());

        if(StringUtils.isNotBlank(smsSubscriptionDTO.getSecurityDeposit()))
            requestBody.put("securityDeposit",smsSubscriptionDTO.getSecurityDeposit());

        if(StringUtils.isNotBlank(smsSubscriptionDTO.getDeductionStartDate()))
            requestBody.put("deductionStartDate",smsSubscriptionDTO.getDeductionStartDate());

        if(StringUtils.isNotBlank(smsSubscriptionDTO.getEndDate()))
            requestBody.put("endDate",smsSubscriptionDTO.getEndDate());

        if(MapUtils.isNotEmpty(smsSubscriptionDTO.getUserSubscriptionMetadata()))
            requestBody.put("userSubscriptionMetadata",smsSubscriptionDTO.getUserSubscriptionMetadata());

        if(CollectionUtils.isNotEmpty(smsSubscriptionDTO.getOtherCharges()))
            requestBody.put("otherCharges",smsSubscriptionDTO.getOtherCharges());


        HttpHeaders headers = getSubscriptionHeaders();
        ResponseEntity<String> responseEntity = restProcessorDelegate.executeSubscriptionRequestHystrix(url,HttpMethod.POST.name(), null, headers,requestBody, String.class);
        String response = responseEntity.getBody();
        return(response);
    }

    private UpdateSubscriptionStatusDTO createUpdateData(String mid,String custId,String usn, String serviceName, String subscriptionType, String status)
    {
        UpdateSubscriptionStatusDTO updateSubscriptionStatusDTO = new UpdateSubscriptionStatusDTO();
        updateSubscriptionStatusDTO.setMid(mid);
        updateSubscriptionStatusDTO.setCustId(custId);
        updateSubscriptionStatusDTO.setUsn(usn);
        updateSubscriptionStatusDTO.setServiceName(serviceName);
        updateSubscriptionStatusDTO.setSubscriptionType(subscriptionType);
        updateSubscriptionStatusDTO.setStatus(status);
        return updateSubscriptionStatusDTO;
    }

    private void updateOnBoss(String mid, String client, Boolean smsAllowed, Map<String, Object> alertsStatusOnBoss) throws Exception {
        LOGGER.info("Inside updateOnBoss method with smsAllowed {} for mid {} and client {}",smsAllowed,mid,client);
        String bossUrl = env.getRequiredProperty(DomainConstants.BOSS_BASE_URL) + "/api/v1/communication/"
                + mid;
        Map<String,Object> finalRequest = new HashMap<>();

//		Map<String, Object> updatedStatus = merchantProfileService.getCommunicationConfiguration(mid, "ALL", client);
        Map<String, Object> notifications = MapUtils.getMap(alertsStatusOnBoss, "notifications", null);
        Map<String, Object> updatedTransaction = MapUtils.getMap(notifications, "transaction", null);
        Map<String, Object> updatedRefund = MapUtils.getMap(notifications, "refund", null);
        updatedRefund.put("smsAllowed", smsAllowed);
        updatedTransaction.put("smsAllowed", smsAllowed);

        finalRequest.put("intent", "sms");
        finalRequest.put("transaction",updatedTransaction);
        finalRequest.put("refund", updatedRefund);

        merchantProfileService.handleSMSEmailAlertsOnBoss(finalRequest,bossUrl);
    }

    private SmsSubscriptionDTO createSubscriptionData(String mid, String custId) {
        SmsSubscriptionDTO smsSubscriptionDTO = new SmsSubscriptionDTO();
        smsSubscriptionDTO.setMid(mid);
        smsSubscriptionDTO.setCustId(custId);
        smsSubscriptionDTO.setUsn(mid);
        smsSubscriptionDTO.setServiceName("SMS_CHARGE");
        smsSubscriptionDTO.setServiceType("SMS_PILOT");
        smsSubscriptionDTO.setSubscriptionType("RENTAL");
        smsSubscriptionDTO.setPlanPrice("25");
        smsSubscriptionDTO.setFrequency("1M");

        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String todayDate = currentDateTime.format(formatter);

        smsSubscriptionDTO.setOnboardingDate(todayDate);
        smsSubscriptionDTO.setDeductionStartDate(todayDate);
        return smsSubscriptionDTO;
    }


}
