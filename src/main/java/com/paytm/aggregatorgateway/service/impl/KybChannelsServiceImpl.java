package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.service.KybChannelsService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class KybChannelsServiceImpl implements KybChannelsService {
	private static final Logger LOGGER = LogManager.getLogger(KybChannelsServiceImpl.class);
	
	@Value("${"+ DomainConstants.DIGITALPROXY_BASE_URL+"}")
    private String baseUrl;
	
	@Autowired
	private RestProcessorDelegate restProcessorDelegate;
	
	public ResponseUmp fetchChannels(String kybId) throws Exception
	{
		LOGGER.info("Into Fetch channel Method with kybId");
		if(StringUtils.isBlank(kybId))
           return new ResponseUmp("FAILURE",null,"KybId not associated with this merchant",null);
		
		String url = baseUrl + "/chlbwp/v1/admin/all-channels";
//		HttpHeaders headers = generateHeaders();
//		Map<String,String> queryParams = new HashMap<>();
//		queryParams.put("kyb_ids", kybId);
		
		Map<String,Object> result = new HashMap<>();
		ResponseUmp responseUmp = new ResponseUmp();
		String chatEligible = "false";

//		ResponseEntity<String> httpResponse = restProcessorDelegate.executeDigitalProxyRequestHystrix(url,
//								HttpMethod.GET.name(), queryParams, headers, null, String.class);
//		if(!(httpResponse.getStatusCode().is2xxSuccessful()))
//		{
//			responseUmp.setStatus("FAILURE");
//			responseUmp.setStatusCode(httpResponse.getStatusCode().toString());
//			responseUmp.setStatusMessage("Error Occcured In Channels Api");
//			responseUmp.setResults(null);
//			return responseUmp;
//		}
//		Map<String,Object> responseMap = MappingUtils.convertJsonToType(httpResponse.getBody(),Map.class);
//		Map<String,Object> channelList = MapUtils.getMap(responseMap, "channelList", null);
//		if(channelList !=null)
//		{
//		   List<Map<String,Object>> storeList = (List<Map<String, Object>>) MapUtils.getObject(channelList, kybId, null);
//		   if(storeList != null && storeList.size() == 1)
//		   {
//			   chatEligible = "true";
//			   result.put("store", storeList.get(0));
//			   result.put("userDetails", MapUtils.getMap(responseMap, "userDetails", null));
//		   }
//		}
		result.put("chatEligible", chatEligible);
		
		responseUmp.setStatus("SUCCESS");
//		responseUmp.setStatusCode(httpResponse.getStatusCode().toString());
		responseUmp.setStatusCode("200");
		responseUmp.setResults(result);
		
		return responseUmp;

	}
	
	/*private HttpHeaders generateHeaders()
	{
		HttpHeaders headers = new HttpHeaders();
		headers.add("sso_token", SecurityUtils.getLoggedInUser().getPaytmSSOToken());
		headers.add("Content-Type", "application/json");
		headers.add("X-USER-ID", SecurityUtils.getLoggedInUser().getId());
		SecureRandom secureRandom=new SecureRandom();
		String traceId = secureRandom.nextInt(1000) + 100 + "" + new Date();
		headers.add("trace-id", traceId);

		return headers;
	}*/

}
