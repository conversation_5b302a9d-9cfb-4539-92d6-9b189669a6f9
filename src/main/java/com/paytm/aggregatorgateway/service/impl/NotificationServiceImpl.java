package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dto.MerchantInfoDto;
import com.paytm.aggregatorgateway.dto.NotificationReceiver;
import com.paytm.aggregatorgateway.dto.NotificationsVO;
import com.paytm.aggregatorgateway.enums.CardtypeNotification;
import com.paytm.aggregatorgateway.enums.LimitUpgradeIdentifiers;
import com.paytm.aggregatorgateway.enums.Nudges;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.NotificationService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class NotificationServiceImpl implements NotificationService {

    @Autowired
    Environment commonProperties;

    @Autowired
    RestProcessorDelegate restProcessorDelegate;

    @Autowired
    ObjectMapper objectMapper;


    public static final String NOTIFY_PUSH_V3_WHATSAPP_URL ="/v3/notify/whatsapp";

    public static final String MOBILE_NUMBER="MOBILENUMBER";

    public static Map<String,String> instrumentMapping = new HashMap<>();

    static{
        instrumentMapping.put(LimitUpgradeIdentifiers.CC.name(),"Credit Card") ;
        instrumentMapping.put(LimitUpgradeIdentifiers.DC.name(),"Debit Card") ;
        instrumentMapping.put(LimitUpgradeIdentifiers.BALANCE.name(),"Wallet") ;
        instrumentMapping.put(LimitUpgradeIdentifiers.POSTPAID.name(),"Postpaid") ;
    }

    @Override
    public void notifyPush(String mid, String identifierValue, String cardType) throws Exception{
        //log.info("in notifyPush for mid {} identifierValue {}", mid, identifierValue);
        String url = commonProperties.getRequiredProperty(DomainConstants.NOTIFICATIONS_BASE_URL) + "/v3/notify/push";
        HttpHeaders headers = generateNotificationsHeaders();
        Map<String, Object> requestBody = getNotifyPushRequestBody(mid, identifierValue,cardType);
        ResponseEntity<String> response = restProcessorDelegate.executeNotificationRequestHystrix(
                url, HttpMethod.POST.name(), null, headers, requestBody, String.class);
        if(!response.getStatusCode().is2xxSuccessful()){
            log.info("received failure from notification");
            throw new RuntimeException("Received failure from NOTIFICATIONS");
        }
        log.info("Successfully pushed notification");

    }

    @Override
    public void notifyWhatsappPush(String mid, String featureType_notification, MerchantInfoDto merchantDetails, String triggerInstrumentMonthly) throws Exception
    {
        log.info("in notifyWhatsappPush for mid {} cardType {}", mid, featureType_notification);
        featureType_notification = featureType_notification + "_WHATSAPP";


        Map<String, String> param = new HashMap<>();
        //  param.put("url",url);
        param.put("merchant_name",merchantDetails.getMerchantName());

        if(featureType_notification.equals(CardtypeNotification.LIMIT_UPGRADE_INSTRUMENT_85_WHATSAPP.name()) && StringUtils.isNotBlank(triggerInstrumentMonthly)) {
            triggerInstrumentMonthly = getInstrumentType(triggerInstrumentMonthly);
            param.put("payment_instrument", triggerInstrumentMonthly);
        }

        String templateName = CardtypeNotification.valueOf(featureType_notification).getTemplateName();
        Map<String, Object> response = sendWhatsappNotification(merchantDetails.getPrimaryMobileNumber(), param, templateName);

        if (response != null && response.containsKey("status") && response.get("status") != null && response.get("status").toString().equals("SUCCESS")) {
            log.info("SUCCESS: Sent notification for mid: {}", mid);
        }
        else {
            log.info("FAILURE: Not sent notification for mid: {}", mid);
        }


    }

    private Map<String, Object> sendWhatsappNotification(String mobileNumber, Map<String, String> param, String templateName)
    {
        log.info("Into sendNotification, mobileNumber: {},param: {}, tenplateName: {}",mobileNumber,param,templateName);
        try {

            String url = commonProperties.getRequiredProperty(DomainConstants.NOTIFICATIONS_BASE_URL) + NOTIFY_PUSH_V3_WHATSAPP_URL;

            NotificationsVO notificationsVO = new NotificationsVO();
            notificationsVO.setTemplateName(templateName);
            notificationsVO.setDynamicParams(param);

            List<String> mobileNumbers = new ArrayList<>();
            mobileNumbers.add(mobileNumber);
            notificationsVO.setNotificationReceiver(new NotificationReceiver(mobileNumbers,MOBILE_NUMBER));

            String body = objectMapper.writeValueAsString(notificationsVO);

            long startTime = System.currentTimeMillis();
            log.info("Request to notifications: url: {}, body: {}", url, body);
            ResponseEntity<String> response = restProcessorDelegate.executeNotificationRequestHystrix(
                    url, HttpMethod.POST.name(), null, generateNotificationsHeaders(), body, String.class);

            long timeTaken = System.currentTimeMillis() - startTime;
            log.info("Response from notifications :{} statusCode :{}, totalTime: {}ms", response.getBody(), response.getStatusCode(), timeTaken);

            if (response.getStatusCode()!= HttpStatus.OK)
                throw new RuntimeException(response.getBody());
            return MappingUtils.OBJECT_MAPPER.readValue(response.getBody(), Map.class);

        }
        catch (Exception ex) {
            log.error("Exception occurred while sending notifications: {}", ex.getMessage());
        }
        log.info("Exiting from sendNotification method");
        return null;
    }


    private HttpHeaders generateNotificationsHeaders(){
        String clientId = AWSSecretManager.awsSecretsMap.get(AWSSecrets.NOTIFICATIONS_CLIENT_ID.getValue());
        String secretKey = AWSSecretManager.awsSecretsMap.get(AWSSecrets.NOTIFICATIONS_SECRET_KEY.getValue());
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type","application/json");
        headers.add("client_id",clientId);
        headers.add("secret_key",secretKey);
        return headers;
    }

    private Map<String, Object> getNotifyPushRequestBody(String mid, String identifierValue, String cardType){
        log.info("in getNotifyPushRequestBody ~");
        String deeplinkFormat  = CardtypeNotification.valueOf(cardType).getDeeplink();
        try{
            if(PayTmPGConstants.ADDRESS_UPDATE_WIDGET_CARD_TYPE.equals(cardType)){
                Date date = new Date(System.currentTimeMillis());
                identifierValue = identifierValue + "_" + date.getTime();
            }
        }
        catch(Exception e){
            log.error("exception occurred while encoding {}", e.getMessage());
        }
        if(deeplinkFormat.contains("%s"))
            deeplinkFormat = String.format(deeplinkFormat,identifierValue);

        String notificationPushDeeplink = "paytmba://business-app/ump-web?url="+ commonProperties.getRequiredProperty(PayTmPGConstants.DEEPLINK_BASE_URL)
                    +"/app?redirectUrl="+deeplinkFormat;

        Map<String, String> dynamicParamsMap = new HashMap<>();
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("sendBroadcastPush",true);
        requestBody.put("templateName",CardtypeNotification.valueOf(cardType).getTemplateName());
        Map<String, Object> notificationReceiverMap = new HashMap<>();
        List<String> notificationReceiverIdentifierList = new ArrayList<>();
        notificationReceiverIdentifierList.add(mid);
        notificationReceiverMap.put("notificationReceiverType","MERCHANTID");
        notificationReceiverMap.put("notificationReceiverIdentifier",notificationReceiverIdentifierList);
        List<String> deviceTypeList = new ArrayList<>();
        deviceTypeList.add("ANDROIDAPP");
        deviceTypeList.add("IOSAPP");
        requestBody.put("notificationReceiver",notificationReceiverMap);
        requestBody.put("deviceType",deviceTypeList);
        requestBody.put("dynamicParams",dynamicParamsMap);
        Map<String, String> extraCommonParamsMap = new HashMap<>();
        if(!(PayTmPGConstants.CM_OUT_OF_PRINTING_PAPER.equals(cardType) || cardType.contains(Nudges.LIMIT_UPGRADE.toString()) )) {
            extraCommonParamsMap.put("url_type", "external");
            extraCommonParamsMap.put("url",notificationPushDeeplink);
        }

        if(cardType.equals(PayTmPGConstants.ADDRESS_UPDATE_WIDGET_CARD_TYPE))
            extraCommonParamsMap.put("bannerId","1242862");

        if(cardType.equals(PayTmPGConstants.GEOFENCING_WIDGET_CARD_TYPE) || cardType.equals(PayTmPGConstants.CM_OUT_OF_PRINTING_PAPER) ||
                cardType.equals(PayTmPGConstants.CM_OUT_OF_BATTERY) || cardType.equals(PayTmPGConstants.CM_OUT_OF_NETWORK) || cardType.equals(PayTmPGConstants.CM_BATTERY_10TO20) || cardType.equals(PayTmPGConstants.SB_OUT_OF_BATTERY_5) || cardType.equals(PayTmPGConstants.SB_OUT_OF_BATTERY_10)) {
            String maskedDeviceId = "XX"+identifierValue.substring(identifierValue.length()-3);
            dynamicParamsMap.put("device_id", maskedDeviceId);
        }

        if(cardType.equals(CardtypeNotification.LIMIT_UPGRADE_INSTRUMENT_85.name()) && StringUtils.isNotBlank(identifierValue)) {
            identifierValue = getInstrumentType(identifierValue);
            dynamicParamsMap.put("payment_instrument", identifierValue);
        }

        requestBody.put("extraCommonParams", extraCommonParamsMap);
        return requestBody;

    }

    private String getInstrumentType(String identifierValue) {
        if(instrumentMapping.containsKey(identifierValue))
            return instrumentMapping.get(identifierValue);
        else if (identifierValue.contains("_"))
            return identifierValue.replace("_"," ");
        else
            return identifierValue;
    }

}
