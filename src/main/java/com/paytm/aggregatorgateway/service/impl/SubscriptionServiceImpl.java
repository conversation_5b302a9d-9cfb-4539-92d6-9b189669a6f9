package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.SubscriptionService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;

@Service
public class SubscriptionServiceImpl implements SubscriptionService {

	private static final Logger LOGGER = LogManager.getLogger(SubscriptionServiceImpl.class);

	@Autowired
	private Environment env;

	@Autowired
	private RestProcessorDelegate restProcessorDelegate;

	@Override
	public Map<String, Object> autoPaySubscription(String mid, String upiMandate, String serviceName, String parentOrderId, String custId) throws Exception {
		String url = env.getRequiredProperty(DomainConstants.SUBSCRIPTION_BASE_URL)+"/api/v1/subscription/autopay";
		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("mid", mid);
		if (StringUtils.isNotEmpty(serviceName)) {
			requestBody.put("serviceName", serviceName);
		}
		if (StringUtils.isNotEmpty(upiMandate)) {
			requestBody.put("upiMandate", upiMandate);
		}
		if (StringUtils.isNotEmpty(parentOrderId)) {
			requestBody.put("parentOrderId", parentOrderId);
		}
		if (StringUtils.isNotEmpty(custId)) {
			requestBody.put("custId", custId);
		}
		requestBody.put("allowOverride",true);
		HttpHeaders headers = getSubscriptionHeaders();
		ResponseEntity<String> responseEntity = restProcessorDelegate.executeSubscriptionRequestHystrix(url, HttpMethod.POST.name(),null, headers,requestBody,String.class);
		if(!responseEntity.getStatusCode().is2xxSuccessful()) {
			LOGGER.info("auto Pay Subscription api failed "+responseEntity.getStatusCode());
			throw new RuntimeException("auto Pay Subscription api failed");
		}
		Map<String, Object> responseMap = null;
		try {
			responseMap = MappingUtils.convertJsonToMap(responseEntity.getBody());
		} catch (Exception e) {
			LOGGER.error("Response is not a valid json {}", e.getMessage());
			throw e;
		}
		return responseMap;

	}

	@Override
	public Map<String, Object> fetchSubscription(String mid,String usn, String serviceName,String subscriptionType, String serviceType,String subscriptionStatus) throws Exception {
		//LOGGER.info("merchant data for mid {} ", mid);
		String url = env.getRequiredProperty(DomainConstants.SUBSCRIPTION_BASE_URL)+"/api/v1/subscription";
		Map<String, String> queryParams = new HashMap<>();
		queryParams.put("mid", mid);
		if (StringUtils.isNotEmpty(serviceName)) {
			queryParams.put("serviceName", serviceName);
		}
		if (StringUtils.isNotEmpty(usn)) {
			queryParams.put("usn", usn);
		}
		if (StringUtils.isNotEmpty(subscriptionType)) {
			queryParams.put("subscriptionType", subscriptionType);
		}
		if (StringUtils.isNotEmpty(serviceType)) {
			queryParams.put("serviceType", serviceType);
		}
		if(StringUtils.isNotEmpty(subscriptionStatus))
		{
			queryParams.put("userSubscriptionStatusStr",subscriptionStatus);
		}
		HttpHeaders headers = getSubscriptionHeaders();

		ResponseEntity<String> responseEntity = restProcessorDelegate.executeSubscriptionRequestHystrix(url, HttpMethod.GET.name(),queryParams, headers,null,String.class);
		Map<String, Object> responseMap = null;
		try {
			responseMap = MappingUtils.convertJsonToMap(responseEntity.getBody());
			//LOGGER.info("responseMap from subscriptions : " + responseMap );
		} catch (IOException e) {
			LOGGER.error("Response is not a valid json {}", e.getMessage());
			Map<String, Object> response = new HashMap<>();
			response.put("statusCode", UMPErrorCodeEnums.PARSING_EXCEPTION.getErrorCode());
			response.put("status", "FAILURE");
			response.put("results", UMPErrorCodeEnums.PARSING_EXCEPTION.getErrorMsg());
			return response;
		}
		return responseMap;

	}


	private HttpHeaders getSubscriptionHeaders() throws IllegalArgumentException, UnsupportedEncodingException {
		HttpHeaders headers = new HttpHeaders();
		headers.add("x-client-id", "ump");
		headers.add("x-client-token", jwtToken());
		return headers;
	}

	private String jwtToken() throws IllegalArgumentException, UnsupportedEncodingException {
		byte[] decodedKey = Base64.getDecoder().decode(AWSSecretManager.awsSecretsMap.get(AWSSecrets.SUBSCRIPTION_RENTAL_JWT_SECRET.getValue()));
		Algorithm algorithm = Algorithm.HMAC512(decodedKey);
		String token = JWT.create().withIssuedAt(new Date()).withClaim("client-id", "ump").sign(algorithm);
		return token;
	}

}
