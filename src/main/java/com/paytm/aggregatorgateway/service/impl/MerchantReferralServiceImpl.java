package com.paytm.aggregatorgateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.service.MerchantReferralService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@Service
public class MerchantReferralServiceImpl implements MerchantReferralService {

	private static final Logger LOGGER = LogManager.getLogger(MerchantReferralServiceImpl.class);

	@Value("${msupercash.base.url}")
	private String merchantReferralBaseUrl;

	@Value("${msupercash.jwt.client}")
	private String promocardJwtClient;

	@Autowired
	RestProcessorDelegate restProcessorDelegate;

	@Override
	public String getReferral(String mid, String tag, String deviceIdentifier, String locale) throws InterruptedException, JsonProcessingException {
		String url = merchantReferralBaseUrl + "/v1/mpromocard/supercash/referral/s2s";

		Map<String, String> queryParam = new HashMap<>();
		queryParam.put("tag", tag);
		queryParam.put("deviceIdentifier", deviceIdentifier);
		queryParam.put("locale", locale);

		HttpHeaders headers = getMerchantReferralHeaders(mid);

		//LOGGER.info("Entering into Exchange with URL: {}", url);
		ResponseEntity<String> responseEntity = restProcessorDelegate.executePromoRequestHystrix(url, HttpMethod.GET.name(), queryParam, headers, null,String.class);

		if(responseEntity.getStatusCode().is2xxSuccessful()){
			//LOGGER.info("Received Successfull Response: {}", responseEntity);
			return responseEntity.getBody();
		}

		//LOGGER.info("Received Failure Response: {}", responseEntity);
		Map<String, String> failureMap = new HashMap<>();
		failureMap.put("status", PayTmPGConstants.FAILURE);
		failureMap.put("statusCode", responseEntity.getStatusCode().toString());
		LOGGER.info("Exiting from Get Referral");
		return MappingUtils.convertObjectToJson(failureMap);
	}

	private HttpHeaders getMerchantReferralHeaders(String mid){
		HttpHeaders headers = new HttpHeaders();
		headers.add("accept", "*/*");
		headers.add("client-id", promocardJwtClient);
		headers.add("jwt-token", createJwtToken());
		headers.add("x-merchant-id", mid);
		return headers;
	}

	private String createJwtToken() {
		try {
			JWTCreator.Builder jwtBuilder = JWT.create();
			jwtBuilder.withClaim("iat", new Date());
			jwtBuilder.withClaim("iss", promocardJwtClient);

			Algorithm algorithm = Algorithm.HMAC512(AWSSecretManager.awsSecretsMap.get(AWSSecrets.MSUPERCASH_JWT_SECRET.getValue()));

			return jwtBuilder.sign(algorithm);
		} catch (Throwable e) {
			LOGGER.error("Exception while generating JWT token : {} ", e);
		}
		return null;
	}
}
