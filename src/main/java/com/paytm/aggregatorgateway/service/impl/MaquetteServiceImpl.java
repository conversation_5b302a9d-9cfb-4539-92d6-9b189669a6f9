package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.service.MaquetteService;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class MaquetteServiceImpl implements MaquetteService {

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private Environment commonProperties;

    @Override
    public ResponseUmp verifyLatLong(Map<String, Object> requestBody) throws InterruptedException {
        log.info("In verifyLatLong impl for mid {}", SecurityUtils.getCurrentMerchant().getMid());

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        requestBody.put("session_id", UUID.randomUUID().toString());
        requestBody.put("evaluation_type","edc_deployment");
        Map<String, Object> requestPayloadMap = new HashMap<>();
        requestPayloadMap.put("latitude", requestBody.get("latitude"));
        requestPayloadMap.put("longitude", requestBody.get("longitude"));
        requestPayloadMap.put("flow","P4B_EDC_GEOFENCING");
        requestBody.remove("latitude");
        requestBody.remove("longitude");
        requestBody.put("request_payload", requestPayloadMap);
        requestBody.put("request_metadata", new HashMap<String, Object>());

        try{
            String url = commonProperties.getRequiredProperty(DomainConstants.MAQUETTE_BASE_URL) + "/rt/fraudcheck";
            ResponseEntity<String> response = restProcessorDelegate.executeMaquetteRequestHystrix(url, HttpMethod.POST.name(), null, headers, requestBody, String.class);
            if(!response.getStatusCode().is2xxSuccessful()){
                log.error("Received failure from Maquette");
                throw new RuntimeException("Received failure from Maquette");
            }

            Map<String, Object> responseMap = objectMapper.readValue(response.getBody(), new TypeReference<Map<String, Object>>() {});
            return new ResponseUmp("Success", "200", "Successfully fetched verification status", responseMap);
        } catch(Exception e){
            log.error("Error occurred while fetching/parsing Maquette API response");
            throw new RuntimeException("Error occurred while fetching/parsing Maquette API response");
        }
    }
}
