package com.paytm.aggregatorgateway.service.impl;

import com.paytm.aggregatorgateway.constants.Queries;
import com.paytm.aggregatorgateway.service.ConsentService;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class ConsentServiceImpl implements ConsentService {

    private static final Logger LOGGER = LogManager.getLogger(ConsentServiceImpl.class);

    @Value("${consent.cache.ttl:86400}")
    private long consentCacheTtl;

    @Autowired
    @Qualifier("masterJdbcTemplate")
    JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public ResponseUmp storeConsentTimeStamp(String mid, String deviceId, String custId, String appversion, String preference, String type) {
        String redisKey = null;
        try {
            // Generate Redis key
            redisKey = redisHelper.generateRedisKey(custId, deviceId, preference, type);

            // Check Redis for existing key
            try {
                Boolean keyExists = redisTemplate.hasKey(redisKey);
                if (keyExists != null && keyExists) {
                    return new ResponseUmp("SUCCESS", "200", "Permission already stored", null);
                }
            } catch (Exception redisCheckEx) {
                LOGGER.info("Redis key check failed: {}", redisCheckEx.getMessage());
            }

            // Write to DB
            try {
                int rowsAffected = jdbcTemplate.update(Queries.INSERT_CONSENT, custId, mid, deviceId, preference, type, appversion);

                if (rowsAffected > 0) {
                    try {
                        redisTemplate.opsForValue().set(redisKey, "true");
                        redisTemplate.expire(redisKey, consentCacheTtl, TimeUnit.SECONDS);
                    } catch (Exception redisWriteEx) {
                        LOGGER.info("Redis set/expire failed: {}", redisWriteEx.getMessage());
                    }
                    return new ResponseUmp("SUCCESS", "200", "Permission stored successfully", null);
                } else {
                    return new ResponseUmp("FAILURE", "400", "Timestamp storage failed due to DB error", null);
                }
            } catch (DataAccessException dae) {
                return new ResponseUmp("FAILURE", "400", "Database error: " + dae.getMessage(), null);
            }

        } catch (Exception e) {
            LOGGER.error("Unhandled error in storeConsentTimeStamp: ", e);
            return new ResponseUmp("FAILURE", "500", "Internal server error", null);
        }
    }

}
