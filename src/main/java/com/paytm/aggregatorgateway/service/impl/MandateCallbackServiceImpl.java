package com.paytm.aggregatorgateway.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.MandateCallbackService;
import com.paytm.aggregatorgateway.service.OMSService;
import com.paytm.aggregatorgateway.service.SubscriptionService;
import com.paytm.aggregatorgateway.service.helper.RedisHelper;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MandateCallbackServiceImpl implements MandateCallbackService {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    private OMSService omsService;

    @Override
    public ResponseUmp processMandateCallback(Map<String, Object> requestBody) throws Exception {
        if (MapUtils.isEmpty(requestBody)) {
            throw new ValidationException("400","Request body cannot be empty");
        }

        String mid = extractMid(requestBody);
        if (StringUtils.isBlank(mid)) {
            throw new ValidationException("400","MID is required");
        }

        String orderId = extractOrderId(requestBody);
        if (StringUtils.isBlank(orderId)) {
            throw new ValidationException("400","Order ID is required");
        }

        Long custId = extractCustomerId(requestBody);

        String upiMandate = extractUpiMandate(requestBody);
        if (StringUtils.isBlank(upiMandate))
            log.info("upiMandate is missing");
        
        String productId =extractProductID(requestBody);
        if (StringUtils.isNotBlank(productId))
            log.info("productId is missing");

        omsService.acknowledgeOrder(orderId);
        if(StringUtils.isNotBlank(upiMandate) && custId!=null)
            subscriptionService.autoPaySubscription(mid,upiMandate,"SOUNDBOX",orderId, String.valueOf(custId));
        else if(StringUtils.isNotBlank(productId) && StringUtils.isNotBlank(mid)){ //for PBUMP-6972 only
            log.info("TWS callback ");
            setRedisData(productId,mid);
        }
        else
            throw new ValidationException("400","Any Data from callback service is missing");
        return new ResponseUmp("200", "SUCCESS", "callback api gives success", null);
    }

    private void setRedisData(String productId, String mid) {
        String redisKey=productId+"|"+mid;
        redisHelper.setTWSflag(redisKey,"true");
    }

    private String extractProductID(Map<String, Object> requestBody) throws Exception {
        JsonNode rootNode = objectMapper.valueToTree(requestBody);
        try {
            JsonNode itemsNode = rootNode.get("items");
            if (itemsNode == null || !itemsNode.isArray() || itemsNode.isEmpty()) {
                throw new RuntimeException("Items array is null, not an array, or empty in the response " );
            }
            JsonNode firstItem = itemsNode.get(0);
            if (firstItem == null) {
                throw new RuntimeException("First item in items array is null");
            }
            JsonNode product_id = firstItem.get("product_id");
            if (product_id == null) {
                throw new RuntimeException("product_id is null in the items array");
            }
            return product_id.asText();
            
        } catch (Exception e) {
            log.error("Error extracting PID: {} with requestbody {} ", e,rootNode);
        }
        return null;
    }

    private String extractMid(Map<String, Object> requestBody) throws Exception {
        JsonNode rootNode = objectMapper.valueToTree(requestBody);
        try {
           // Get items array
            JsonNode itemsNode = rootNode.get("items");
            if (itemsNode == null || !itemsNode.isArray() || itemsNode.isEmpty()) {
                throw new RuntimeException("Items array is null, not an array, or empty in the response " );
            }
            JsonNode firstItem = itemsNode.get(0);
            if (firstItem == null) {
                throw new RuntimeException("First item in items array is null");
            }
            JsonNode metaData = firstItem.get("meta_data");
            if (metaData != null && metaData.isTextual()) {
                try {
                    metaData = objectMapper.readTree(metaData.asText());
                } catch (Exception e) {
                    log.error("Failed to parse meta_data as JSON: {}", e.getMessage());
                    throw e;
                }
            }

            if (metaData == null || !metaData.isObject()) {
                throw new RuntimeException("meta_data is null or not an object in the first item");
            }

            JsonNode midNode = metaData.get("mid");
            if (midNode == null || !midNode.isTextual()) {
                throw new RuntimeException("mid is null or not a string in meta_data");
            }
            String mid = midNode.asText();
            return mid;

        } catch (Exception e) {
            log.error("Error extracting MID: {} with requestbody {} ", e,rootNode);
        }
        return null;
    }

    private String extractOrderId(Map<String, Object> requestBody) throws Exception {
        try {
            // First try to get from parent order
            String orderId = String.valueOf(requestBody.get("id"));
            if (StringUtils.isNotBlank(orderId)) {
                return orderId;
            }
            return fetchOrderIdFromMetaData(requestBody);
        } catch (Exception e) {
            log.error("Error extracting Order ID: {}", e.getMessage());
        }
        return null;
    }

    private String fetchOrderIdFromMetaData(Map<String, Object> requestBody) throws Exception {
        JsonNode rootNode = objectMapper.valueToTree(requestBody);
        try {

            JsonNode itemsNode = rootNode.get("items");
            if (itemsNode == null || !itemsNode.isArray() || itemsNode.isEmpty()) {
                throw new RuntimeException("Items array is null, not an array, or empty in the response");
            }
            JsonNode firstItem = itemsNode.get(0);
            if (firstItem == null) {
                throw new RuntimeException("First item in items array is null");
            }
            JsonNode metaData = firstItem.get("meta_data");
            if (metaData != null && metaData.isTextual()) {
                try {
                    metaData = objectMapper.readTree(metaData.asText());
                } catch (Exception e) {
                    log.error("Failed to parse meta_data as JSON: {}", e.getMessage());
                    throw e;
                }
            }
            if (metaData == null || !metaData.isObject()) {
                throw new RuntimeException("meta_data is null or not an object in the first item");
            }

            JsonNode subscriptionOrderId = metaData.get("subscriptionOrderId");
            if (subscriptionOrderId == null || !subscriptionOrderId.isTextual()) {
                throw new RuntimeException("subscriptionOrderId is null or not a string in meta_data");
            }
            String subscriptionOrderIdstr = subscriptionOrderId.asText();
            return subscriptionOrderIdstr;
        }catch (Exception e){
            log.error("error while fetch OrderId From MetaData e: {} requesbody: {}",e,rootNode);
            throw e;
        }
    }

    private Long extractCustomerId(Map<String, Object> requestBody) {
        try {
            Object custId = requestBody.get("customer_id");
            if (custId != null) {
                return Long.valueOf(custId.toString());
            }
        } catch (Exception e) {
            log.error("Error extracting Customer ID: {}", e.getMessage());
        }
        return null;
    }

    private String extractUpiMandate(Map<String, Object> requestBody) {
        String transactionResponseStr ="";
        try {
            List<Map<String, Object>> payments = (List<Map<String, Object>>) requestBody.get("payments");
            if (payments != null && !payments.isEmpty()) {
                Map<String, Object> payment = payments.get(0);
                transactionResponseStr = (String) payment.get("transaction_response");
                if (transactionResponseStr != null) {
                    Map<String, Object> transactionResponse = objectMapper.readValue(transactionResponseStr, Map.class);
                    if (transactionResponse != null) {
                        return (String) transactionResponse.get("SUBS_ID");
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting UPI mandate: {} with transactionResponseStr {} ", e.getMessage(),transactionResponseStr);
        }
        return null;

    }

} 