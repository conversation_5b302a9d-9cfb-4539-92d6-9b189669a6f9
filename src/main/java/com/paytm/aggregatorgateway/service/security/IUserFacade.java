package com.paytm.aggregatorgateway.service.security;

import com.paytm.pgdashboard.commons.dto.User;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 
 * <AUTHOR>
 *
 */
public interface IUserFacade {

	boolean isSuperUser(User user);

	User getUser(HttpServletRequest httpServletRequest, String token) throws Exception;

	User getUserByXAuthUMPOrCookie(HttpServletRequest httpServletRequest) throws Exception;

	void checkXAuthUMPValidity(String xAuthUmp, HttpServletRequest request);

	public void validateJWT(String jwt, String clientId) throws Exception;

    void validateJWTForCheckout(String toString) throws Exception;
}
