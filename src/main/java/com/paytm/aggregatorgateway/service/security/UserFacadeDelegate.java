package com.paytm.aggregatorgateway.service.security;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.security.SecurityConstant;
import com.paytm.aggregatorgateway.exceptions.TooManyRequestException;
import com.paytm.aggregatorgateway.exceptions.UnauthorizedException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.utils.DTOCompressionDecompressionUtil;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.vo.UmpErrorResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Map;

import static com.paytm.aggregatorgateway.constants.PayTmPGConstants.*;

@Component
public class UserFacadeDelegate implements SecurityConstant {
	private static final Logger LOGGER = LogManager.getLogger(UserFacadeDelegate.class);

	@Autowired
	RestProcessorDelegate restProcessorDelegate;
	
	@Autowired
	private Environment commonProperties;

	private Gson gson = new GsonBuilder().setDateFormat("dd/MM/yyyy").create();

	@CacheEvict(value="users",key = "#token",cacheManager = "redisCacheManagerContext")
	public void evictCachedUserToken(String token,String uid) {
		LOGGER.info("Clearing cache from redis for token: {} and uid: {}", token, uid);
	}

	@Cacheable(value="users", keyGenerator = "redisCustomKeyGenerator", unless="#result==null", cacheManager = "redisCacheManagerForContext")
	public String getLoggedInUserContextCached(Map<String, Object> redisKeyMap, HttpServletRequest httpServletRequest) throws Exception {
		LOGGER.info("Redis cache not found, fetching from downstream");
		return getLoggedInUserContext(httpServletRequest);
	}

	@Cacheable(value="users", keyGenerator = "redisCustomCompressedKeyGenerator", unless="#result==null", cacheManager = "redisCacheManagerForContext")
	public String getLoggedInUserCompressedContextCached(Map<String, Object> redisKeyMap, HttpServletRequest httpServletRequest) throws Exception {
		LOGGER.info("Redis cache not found, fetching compressed context");
		return getLoggedInUserCompressedContext(httpServletRequest);
	}

	public String getLoggedInUserCompressedContext(HttpServletRequest httpServletRequest) throws Exception {
		LOGGER.info("Inside getLoggedInUserCompressedContext");
		String context = getLoggedInUserContext(httpServletRequest);
		return DTOCompressionDecompressionUtil.compressAndEncodeString(context);
	}

	public String getLoggedInUserContext(HttpServletRequest httpServletRequest) throws Exception {
		
		// to be picked from property
		ResponseEntity<String> httpServiceResponse = null;
		String xUserMid = new String();
		String cookie = new String();
		String client = null;
		try {
			HttpHeaders headerParam = new HttpHeaders();
			for (String s : Collections.list(httpServletRequest.getHeaderNames())) {
				if (s.equalsIgnoreCase("x-auth-ump") || s.equalsIgnoreCase("x-user-mid")
						|| s.equalsIgnoreCase("x-user-token") || s.equalsIgnoreCase("User-Agent")
						|| s.equalsIgnoreCase("x-ump-version") || s.equalsIgnoreCase("Cookie")
						|| s.equalsIgnoreCase("X-XSRF-TOKEN")|| s.equalsIgnoreCase("client")) {
					String headerValue = httpServletRequest.getHeader(s);
					if (StringUtils.isNotBlank(headerValue))
						headerParam.add(s, headerValue);
					if (s.equalsIgnoreCase("x-user-mid")) {
						xUserMid = headerValue;
					}
					if (s.equalsIgnoreCase("Cookie")) {
						cookie = headerValue;
					}
					if(s.equalsIgnoreCase("client")){
						client = headerValue;
					}
				}
			}
			httpServiceResponse = restProcessorDelegate.executeUMPRequestHystrix(
					commonProperties.getProperty(DomainConstants.UMP_BASE_URL)
							+ commonProperties.getProperty(DomainConstants.UMP_USER_CONTEXT),
					HttpMethod.GET.name(), null, headerParam, null, String.class);

			customErrorHandlingContextApi(httpServiceResponse, client);
			
			return httpServiceResponse.getBody().toString();
			
		} catch (Exception e) {
			LOGGER.error("Exception while context api {}", e.getMessage());
			LOGGER.error("Exception while context api {}", e.getCause());
			LOGGER.error("Exception while context api {}", e.fillInStackTrace());
			if (httpServiceResponse != null && httpServiceResponse.getBody() != null) {
				LOGGER.error("Context api response body {}", httpServiceResponse.getBody().toString());
			}
			throw e;
		}

	}

	private void customErrorHandlingContextApi(ResponseEntity<String> httpServiceResponse, String client) throws Exception {
		if(!httpServiceResponse.getStatusCode().is2xxSuccessful()) {
			LOGGER.info("Context api httpStatusCode {}", httpServiceResponse.getStatusCode());
		}
		if(httpServiceResponse.getStatusCode().is5xxServerError() || httpServiceResponse.getStatusCode().is3xxRedirection()) {
			LOGGER.info("Context api response body in case of 5XX or 3XX{}",httpServiceResponse.getBody());
			throw new Exception();
		}
		if(httpServiceResponse.getStatusCode().is4xxClientError()) {
			LOGGER.info("Context api response body in case of 4XX {}",httpServiceResponse.getBody());
			if (httpServiceResponse.getStatusCode() == HttpStatus.BAD_REQUEST) {
				String errorResponse = httpServiceResponse.getBody();
				if(PPSL_MID_FOUND.equalsIgnoreCase(errorResponse)){
					if(ANDROID_CLIENT.equalsIgnoreCase(client)) {
						throw new ValidationException("ERROR", "INVALID_TOKEN");
					}
					else if(IOS_CLIENT.equalsIgnoreCase(client)){
						throw new UnauthorizedException();
					}
				}
				UmpErrorResponse umpErrorResponse;
				try {
					umpErrorResponse = gson.fromJson(httpServiceResponse.getBody(), UmpErrorResponse.class);
				} catch (Exception e) {
					throw new ValidationException();
				}
				if (umpErrorResponse == null) {
					throw new ValidationException();
				}
				throw new ValidationException("ERROR", umpErrorResponse != null ? umpErrorResponse.getERROR() : "").
						addErrorFluent("statusCode", umpErrorResponse != null ? umpErrorResponse.getStatusCode() : "").
						addErrorFluent("statusMessage", umpErrorResponse != null ? umpErrorResponse.getStatusMessage() : "").
						addErrorFluent("rootErrorMsg", umpErrorResponse != null ? umpErrorResponse.getRootErrorMsg() : "");
			}
			else if(httpServiceResponse.getStatusCode() == HttpStatus.FORBIDDEN) {
				throw new AccessDeniedException(httpServiceResponse.getBody());
			}
			else if(httpServiceResponse.getStatusCode().equals(HttpStatus.TOO_MANY_REQUESTS)) {
				throw new TooManyRequestException(httpServiceResponse.getBody());
			} else {
				throw new ValidationException();
			}
		}
	}

	@Cacheable(key = "#xAuthUmp", value = "xAuthUmp", cacheManager = "redisCacheManager", unless = "#result==null")
	public String getClientInfoFromUMP(String xAuthUmp, HttpServletRequest request) {

		ResponseEntity<String> httpServiceResponse = null;
		String url = commonProperties.getProperty(DomainConstants.UMP_BASE_URL) + commonProperties.getProperty(DomainConstants.UMP_CLIENT_INFO);
		try {
			httpServiceResponse = restProcessorDelegate.executeUMPRequestHystrix(url, HttpMethod.GET.name(), null, getUMPHeaders(request), null, String.class);
		} catch (Exception e) {
			LOGGER.error("Exception occurred while fetch client info from UMP {}", e.getMessage());
			LOGGER.error("Exception occurred while fetch client info from UMP {}", e.getCause());
			LOGGER.error("Exception occurred while fetch client info from UMP {}", e.fillInStackTrace());
			if (httpServiceResponse != null && httpServiceResponse.getBody() != null) {
				LOGGER.error("Client api response body {}", httpServiceResponse.getBody().toString());
			}
		}

		if(httpServiceResponse.getStatusCode() == HttpStatus.FORBIDDEN) {
			throw new AccessDeniedException(httpServiceResponse.getBody());
		} else if(!httpServiceResponse.getStatusCode().is2xxSuccessful()) {
			throw new ValidationException("ERROR", "Fetching client info from UMP failed | Httpstatus" + httpServiceResponse.getStatusCode());
		}

		return httpServiceResponse.getBody();
	}

	private HttpHeaders getUMPHeaders(HttpServletRequest request) {
		HttpHeaders headerParam = new HttpHeaders();
		for(String s : Collections.list(request.getHeaderNames())){
			String headerValue = request.getHeader(s);
			if(s.equalsIgnoreCase("x-auth-ump")
					|| s.equalsIgnoreCase("x-user-mid")
					|| s.equalsIgnoreCase("x-user-token")
					|| s.equalsIgnoreCase("User-Agent")
					|| s.equalsIgnoreCase("x-ump-version")
					|| s.equalsIgnoreCase("Cookie")
					|| s.equalsIgnoreCase("X-XSRF-TOKEN")){
				if (StringUtils.isNotBlank(headerValue))
					headerParam.add(s, headerValue);
			}
		}
		return headerParam;
	}
	
}
