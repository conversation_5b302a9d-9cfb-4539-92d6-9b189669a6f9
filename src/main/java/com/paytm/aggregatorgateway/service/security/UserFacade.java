package com.paytm.aggregatorgateway.service.security;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.constants.security.SecurityConstant;
import com.paytm.aggregatorgateway.dao.JwtDao;
import com.paytm.aggregatorgateway.dto.ClientInfoDTO;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.utils.AesEncryption;
import com.paytm.aggregatorgateway.utils.DTOCompressionDecompressionUtil;
import com.paytm.dashboard.security.UserAuthentication;
import com.paytm.dashboard.security.UserDetailConfig;
import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.pgdashboard.commons.dto.Role;
import com.paytm.pgdashboard.commons.dto.User;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.security.Key;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
@Component
public class UserFacade implements IUserFacade, SecurityConstant {
	private static final Logger LOGGER = LogManager.getLogger(UserFacade.class);
	private Gson gson = new GsonBuilder().setDateFormat("dd/MM/yyyy").create();

	@Autowired
	UserFacadeDelegate userFacadeDelegate;
	
	@Autowired
	private Environment commonProperties;

	@Autowired
	private ObjectMapper jacksonObjectMapper;

	@Autowired
	private JwtDao jwtDao;

	@Override
	public User getUserByXAuthUMPOrCookie(HttpServletRequest httpServletRequest) throws Exception {
		String xAuthUmp = httpServletRequest.getHeader(("x-auth-ump"));
		String cookie = httpServletRequest.getHeader("Cookie");
		LOGGER.info("Fetching user by x-auth-ump : {}", xAuthUmp);
		if (StringUtils.isBlank(xAuthUmp) && StringUtils.isBlank(cookie)) {
			LOGGER.warn("Both xAuthUmp and cookie are blank, returning INVALID_TOKEN");
			throw new ValidationException("ERROR", AUTH_ERROR_INVALID)
					.addErrorFluent("statusCode", "UMP-400")
					.addErrorFluent("statusMessage", AUTH_ERROR_INVALID)
					.addErrorFluent("rootErrorMsg", "Blank xAuthUmp and cookie received");
		}

		return getUserFromDownstream(httpServletRequest);
	}
	
	@Override
	public boolean isSuperUser(User user) {
//		TODO: do we inject the below property in env ?
		if (StringUtils.equals(user.getId(), commonProperties.getProperty(ADMIN_ID))) {
			return true;
		} else {
			return false;
		}
	}
	public void validateJWT(String jwt, String clientId) throws Exception {
		Map<String,String> clientInfoMap;
		try {
				clientInfoMap = jwtDao.fetchClientSecret(clientId);
				String secretJwt = clientInfoMap.get("secret");;
				LOGGER.info("secretJwt fetched {}");
				Key hmacKey = new SecretKeySpec(Base64.getDecoder().decode(secretJwt),
						SignatureAlgorithm.HS256.getJcaName());

				Jws<Claims> jwtToken = Jwts.parser()
						.setSigningKey(hmacKey)
						.parseClaimsJws(jwt);
				Claims claims= jwtToken.getBody();
				if(!claims.get("clientId").equals(clientId)) {
					throw new RuntimeException("Invalid client ID");
			}
		}
		catch(JwtException e) {
			LOGGER.error("error while jwt validation "+e);
			throw e;
		}

		String permissionsString = clientInfoMap.get("permissions");
		List<String> permissions = null;
		if(StringUtils.isNotBlank(permissionsString)){
			permissions = Arrays.asList(permissionsString.split(","));
		}
		addPermision(permissions);
	}

	@Override
	public void validateJWTForCheckout(String jwtToken) throws Exception {
		try {
			LOGGER.info("in validateJWTForCheckout");
			String clientKey = decodeJWTandGetKey(jwtToken);
			if (clientKey == null || !clientKey.equals(commonProperties.getRequiredProperty(CHECKOUT_CALLBACK_CLIENT))) {
				LOGGER.error("Client key not found in token "+clientKey);
				throw new RuntimeException("Client key not found in token");
			}
			User user = new User();
			UserDetails details = new UserDetailConfig(user,false);
			Authentication auth = new UserAuthentication(details);
			SecurityContextImpl secureContext = new SecurityContextImpl();
			secureContext.setAuthentication(auth);
			SecurityContextHolder.setContext(secureContext);

		} catch(JwtException e) {
			LOGGER.error("error while jwt validation at checkout "+e);
			throw e;
		}
	}
	private static String decodeJWTandGetKey(String jwtToken)  {
		try {
			String secret = AWSSecretManager.awsSecretsMap.get(AWSSecrets.CHECKOUT_CALLBACK_SECRET_KEY.getValue());
			String base64Secret = Base64.getEncoder().encodeToString(secret.getBytes());
			SecretKeySpec hmacKey = new SecretKeySpec(
					Base64.getDecoder().decode(base64Secret),
					SignatureAlgorithm.HS256.getJcaName()
			);

			// Parse the token
			Jws<Claims> jwt = Jwts.parser()
					.setSigningKey(hmacKey)
					.parseClaimsJws(jwtToken);

			// Get the claims
			Claims claims = jwt.getBody();
			if (claims.getExpiration().before(new java.util.Date()))
				throw new RuntimeException("Token has expired");

			// Get the client key
			return claims.get("clientKey", String.class);
		}catch (Exception e){
			LOGGER.error("error occured while decodeJWTandGetKey "+e);
			throw e;
		}
	}

	private void addPermision(List<String> permissions) {
		User user = new User();
		if(Objects.nonNull(permissions)) {
			Role jwtRole = new Role();
			jwtRole.setPermissions(permissions);
			List<Role> roleList = Arrays.asList(jwtRole);
			Merchant merchant = new Merchant();
			merchant.setRoles(roleList);
			merchant.setId(0l);
			List<Merchant> merchantList = Arrays.asList(merchant);
			user.setMerchants(merchantList);
			user.setCurrentMerchant(0L);
		}
		UserDetails details = new UserDetailConfig(user,false);
		Authentication auth = new UserAuthentication(details);
		SecurityContextImpl secureContext = new SecurityContextImpl();
		secureContext.setAuthentication(auth);
		SecurityContextHolder.setContext(secureContext);
	}

	public User getUser(HttpServletRequest httpServletRequest, String token) throws Exception {
		String aesEncryptionKey = AWSSecretManager.awsSecretsMap.get(AWSSecrets.AES_ENCRYPTION_KEY.getValue());
		LOGGER.info("Encrypted token is {}", AesEncryption.encrypt(token, aesEncryptionKey));
		if (StringUtils.isBlank(token)) {
			LOGGER.warn("Token is blank, returning INVALID_TOKEN");
			throw new ValidationException("ERROR", AUTH_ERROR_INVALID)
					.addErrorFluent("statusCode", "UMP-400")
					.addErrorFluent("statusMessage", AUTH_ERROR_INVALID)
					.addErrorFluent("rootErrorMsg", "Blank token received");
		}
		return getUserFromDownstreamCached(httpServletRequest);
	}

	private User getUserFromDownstream(HttpServletRequest httpServletRequest) throws Exception {
		return mapResponseToUserObject(userFacadeDelegate.getLoggedInUserContext(httpServletRequest));
	}

	private User getUserFromDownstreamCached(HttpServletRequest httpServletRequest) throws Exception {

		Map<String, Object> redisKeyMap = new HashMap<>();
		redisKeyMap.put("method", HttpMethod.GET.name());
		redisKeyMap.put("url", commonProperties.getProperty(DomainConstants.UMP_BASE_URL) + commonProperties.getProperty(DomainConstants.UMP_USER_CONTEXT));
		Map<String, Object> headers = new HashMap<>();
		headers.put("x-user-token", httpServletRequest.getHeader("x-user-token"));
		redisKeyMap.put("headers", headers);
		String compressedContext = userFacadeDelegate.getLoggedInUserCompressedContextCached(redisKeyMap, httpServletRequest);
		String context = DTOCompressionDecompressionUtil.decodeAndDecompressString(compressedContext);
		//LOGGER.info("Context fetched from cache: {}", context);
		return mapResponseToUserObject(context);
	}

	private User mapResponseToUserObject(String umpContextResponse) throws IOException {
		User user = null;
		try {
			user = jacksonObjectMapper.readValue(umpContextResponse, User.class);
		} catch (JsonParseException | JsonMappingException e) {
			LOGGER.warn("Returning INVALID_TOKEN, for token: {}", e.getMessage());
			throw new ValidationException("ERROR", AUTH_ERROR_INVALID)
					.addErrorFluent("statusCode", "UMP-400")
					.addErrorFluent("statusMessage", AUTH_ERROR_INVALID)
					.addErrorFluent("rootErrorMsg", "couldnt parse the auth response.");
		}
		if(StringUtils.isBlank(user.getPaytmSSOToken()))
		 user.setPaytmSSOToken(StringUtils.isBlank(user.getWalletSSOToken()) ? user.getUserToken() : user.getWalletSSOToken());

		return user;
	}

	public void checkXAuthUMPValidity(String xAuthUmp, HttpServletRequest request) {
		userFacadeDelegate.getClientInfoFromUMP(xAuthUmp, request);
	}
	
}
