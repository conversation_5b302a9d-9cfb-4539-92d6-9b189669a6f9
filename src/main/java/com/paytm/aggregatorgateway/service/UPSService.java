package com.paytm.aggregatorgateway.service;

import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateNFCStatus;
import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateStatus;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service
public interface UPSService {


	ResponseUmp updateNFCStatus(ReqParamsToUpdateNFCStatus reqParamsToUpdateNFCStatus) throws Exception;

    ResponseUmp updateStatus(ReqParamsToUpdateStatus reqParamsToUpdateStatus) throws Exception;

    Map<String, Boolean> getDevicePreferences(String mid);
}
