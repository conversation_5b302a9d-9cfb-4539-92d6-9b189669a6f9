package com.paytm.aggregatorgateway.service;

import com.paytm.aggregatorgateway.dto.QualificationCallbackRequest;
import com.paytm.aggregatorgateway.vo.ResponseUmp;

import java.util.Map;

public interface OcrService {
    ResponseUmp qualificationCallBack(QualificationCallbackRequest request);
    ResponseUmp deductionsCallBack(Map<String, Object> request);
    void callUmpRedisSetKeyAPI(String redisKey, Map<String, Object> result);
}
