package com.paytm.aggregatorgateway.service;

import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.springframework.http.ResponseEntity;

import java.util.Map;

public interface CstService {
        ResponseUmp getTrendingTopics(String language,String source,String verticalId,String tag) throws Exception;

        ResponseUmp getTicketDetails(String encryptedTicketNumber) throws Exception;

        ResponseEntity<String> getAllTicketDetails() throws Exception;

        ResponseEntity<String> getTicketTimeLine(String ticketNumber, TicketInfoDTO ticketInfoDTO) throws Exception;

        void updateTicket(String ticketNumber, Map<String, Object> requestBody) throws Exception;

        ResponseEntity<String> uploadFeedback(Map<String, Object> requestBody) throws Exception;

        ResponseEntity<String> getTickets(String mid, Map<String, String> queryParams) throws InterruptedException, UMPIntegrationException;

        ResponseEntity<String> createTicket(String mid, Map<String, Object> requestBody, Map<String, String> queryParams) throws InterruptedException, UMPIntegrationException;

        ResponseEntity<String> getCallDetailIVR(Map<String, Object> requestBody) throws UMPIntegrationException, InterruptedException;
        ResponseEntity<String> callMeIVR(Map<String, Object> requestBody) throws UMPIntegrationException, InterruptedException;
}
