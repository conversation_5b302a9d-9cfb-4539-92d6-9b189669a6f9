
package com.paytm.aggregatorgateway.service;

import com.paytm.aggregatorgateway.vo.PromoRequestVO;
import com.paytm.aggregatorgateway.vo.ResponseUmp;

import java.util.Map;

public interface PromoEngineService {

    String getGameList(PromoRequestVO request) throws Exception;
    
    String getGameListV2(PromoRequestVO request) throws Exception;

    String getTxnDetail(PromoRequestVO request) throws Exception;

    String getAllOffers(PromoRequestVO request) throws Exception;

    String activateOffer(PromoRequestVO request) throws Exception;

	String getCampaignGameV2(String id, String mid) throws Exception;

	String selectOffer(String campaignId, String body, String mid) throws Exception;
	
	String gameDetails(String id, String mid) throws Exception;

	Map<String, Object> campaignGames() throws Exception;

    Map<String, Object> fetchActiveGames(Map<String, Object> request, String pageSize, String mid) throws Exception;

    ResponseUmp fetchActiveOffers(Integer page_size,Integer page_number,String status) throws Exception;

    ResponseUmp fetchPromoPages(String sectionName, Map<String, String> requestBody) throws Exception;

    ResponseUmp fetchCBOdata(String sectionName) throws Exception;
}