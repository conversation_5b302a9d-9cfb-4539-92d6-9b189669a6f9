package com.paytm.aggregatorgateway.service;


import java.util.Map;

public interface SubscriptionService {

	Map<String, Object> autoPaySubscription(String mid,String upiMandate, String serviceName,String parentOrderId, String custId) throws Exception ;


	Map<String, Object> fetchSubscription(String mid,String usn, String serviceName,
										 String subscriptionType,String serviceType,String subscriptionStatus) throws Exception;


}
