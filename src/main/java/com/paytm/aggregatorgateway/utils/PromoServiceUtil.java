package com.paytm.aggregatorgateway.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Date;

@Component
public class PromoServiceUtil {
	
    private static final Logger LOGGER = LoggerFactory.getLogger(PromoServiceUtil.class);
    
	public static String createJwtToken(String jwtKey,String claim) {
	   // LOGGER.info("Creating JWT token for Promo Engine with claims");
	    String jwtToken=null;
	    try {
	    	Algorithm algo = Algorithm.HMAC256(jwtKey);
	         jwtToken= JWT.create()
	         .withIssuedAt(new Date())
	         .withClaim(PayTmPGConstants.ISSUER, claim)
	         .sign(algo);
	        //LOGGER.info("generated JWT token is {}",jwtToken);
	    } catch (IllegalArgumentException | JWTCreationException e) {
	        LOGGER.error("Exception occured while generating JWT Token {} :", e);
	    }
	    return jwtToken;
	}

}
