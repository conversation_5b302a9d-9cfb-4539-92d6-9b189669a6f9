package com.paytm.aggregatorgateway.utils;

import com.paytm.aggregatorgateway.enums.JwtAlgorithm;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class StorefrontUtils {
    @Autowired
    Environment environment;

    public static String STORE_FRONT_CLIENT = "store.front.client";
    public static String STORE_FRONT_ISSUER = "store.front.issuer";

    public HttpHeaders generateStoreFrontHeaders(String mid, String sso_token)
    {
        String clientSecret = AWSSecretManager.awsSecretsMap.get(AWSSecrets.STORE_FRONT_SECRET.getValue());
        String clientId =  environment.getRequiredProperty(STORE_FRONT_CLIENT);
        String issuer = environment.getRequiredProperty(STORE_FRONT_ISSUER);
        Map<String,Object> jwtClaims = new HashMap<>();
        jwtClaims.put("clientId",clientId);
        jwtClaims.put("iat",new Date());
        jwtClaims.put("iss",issuer);
        String jwtToken = JwtUtil.createJwtToken(jwtClaims,clientSecret.getBytes(),issuer,null,null, JwtAlgorithm.HS256);
        HttpHeaders headerParams = new HttpHeaders();
        headerParams.add("x-merchant-id",mid);
        headerParams.add("Authorization",jwtToken);
        headerParams.add("sso_token",sso_token);
        return headerParams;
    }
}
