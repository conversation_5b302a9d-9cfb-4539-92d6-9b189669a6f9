package com.paytm.aggregatorgateway.utils;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MappingUtils {

	private static final Logger LOGGER = LogManager.getLogger(MappingUtils.class);
	public static final ObjectMapper OBJECT_MAPPER;

	static {
		OBJECT_MAPPER = new ObjectMapper();
		OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
	}
	
	public static Map<String, Object> convertJsonToMap(String jsonString) throws JsonParseException, JsonMappingException, IOException {
		if (jsonString == null) {
			return null;
		}
		return OBJECT_MAPPER.readValue(jsonString, Map.class);
	}

	public static <T> T convertJsonToType(String jsonString, Class<T> type) throws IOException {
		if (jsonString == null) {
			return null;
		}
		return OBJECT_MAPPER.readValue(jsonString, type);
	}

	public static <T> T convertJsonToTypeSafe(String jsonString, Class<T> type) {
		if (jsonString == null) {
			return null;
		}
		try {
			return OBJECT_MAPPER.readValue(jsonString, type);
		} catch (IOException | RuntimeException e) {
			return null;
		}
	}
	
	public static <T> T convertObjectToType(Object object, Class<T> type) {
		if (object == null) {
			return null;
		}
		return OBJECT_MAPPER.convertValue(object, type);
	}
	
	public static String convertObjectToJson(Object object) throws JsonProcessingException {
		return OBJECT_MAPPER.writeValueAsString(object);
	}
	
	public static JsonNode convertJsonToNode(String json) throws IOException {
		return OBJECT_MAPPER.readTree(json);
	}

	public static <T> T extractType(Map<String, Object> parent, String key, Class<T> type) {
		T child = extractTypeSafe(parent, key, type);
		if (child == null) {
			LOGGER.info("Entity: {}, not found as Type: {}", key, type.getSimpleName());
			throw new RuntimeException("Key not present in Map");
		} else {
			return child;
		}
	}
	
	public static <T> T extractTypeSafe(Map<String, Object> parent, String key, Class<T> type) {
		Object value = parent == null ? null : parent.get(key);
		if (type != null && type.isInstance(value)) {
			return (T) value;
		} else {
			return null;
		}
	}

	public static Map<String, Object> filterKeysInclude(Map<String, Object> input, String... keys) {
		if (input == null) {
			return input;
		}

		Map<String, Object> output = new HashMap<>();
		for (String key : keys) {
			if (input.containsKey(key)) {
				output.put(key, input.get(key));
			}
		}
		return output;
	}
	
	public static Map<String, Object> extractMapOrCreateIfNull(Map<String, Object> parent, String child) {
		if (parent == null) {
			LOGGER.info("Entity 'parent' is null");
			throw new RuntimeException("Null response data");
		}
		Map<String, Object> childMap = (Map<String, Object>) parent.get(child);
		if (childMap != null) {
			try {
				return childMap;
			} catch (ClassCastException ex) {
				LOGGER.error("Existing 'child' is not a map", ex);
				throw new RuntimeException("Unexpected response data");
			}
		} else {
			childMap = new HashMap<String, Object>();
			parent.put(child, childMap);
			return (Map<String, Object>) childMap;
		}
	}

	public static <T> List<String> convertListEntriesToString(List<T> listT) throws JsonProcessingException {
		if (listT == null) {
			return null;
		}
		List<String> list = (List<String>) listT;
		for (int i = 0; i < list.size(); i++) {
			list.set(i, Objects.toString(listT.get(i), null));
		}
		return list;
	}

	public static String getBankIconUrl(String ifsc) {
		String code = StringUtils.substring(ifsc, 0, 4);
		if (code != null) {
			code = StringUtils.upperCase(code);
		} else {
			return "https://staticgw1.paytm.com/native/bank/deafultbank.png";
		}
		String logo = null;
		// paytm logo -->use PYTM
		switch (code) {
			case "SBIN": {
				logo = "SBI";
				break;
			}
			case "PYTM": {
				logo = "PPBL";
				break;
			}
			case "ICIC": {
				logo = "ICICI";
				break;
			}
			case "HDFC": {
				logo = "HDFC";
				break;
			}
			case "CNRB": {
				logo = "CANARA";
				break;
			}
			case "UTIB": {
				logo = "AXIS";
				break;
			}
			case "IBKL": {
				logo = "IDBI";
				break;
			}
			case "PUNB": {
				logo = "PNB";
				break;
			}
			case "BARB": {
				logo = "BOB";
				break;
			}
			case "BKID": {
				logo = "BOI";
				break;
			}
			case "YESB": {
				logo = "YES";
				break;
			}
			case "KKBK": {
				logo = "KOTAK";
				break;
			}
			case "HSBC": {
				logo = "HSBC";
				break;
			}
			case "IDFB": {
				logo = "IDFC";
				break;
			}
			case "SCBL": {
				logo = "SCB";
				break;
			}
			case "WALL": {
				logo = "WALL";
				break;
			}
			
			//added new banks from here
			case "ALLA": {
			    logo = "AALDCORP";
			    break;
			}
			case "AIRP": {
			    logo = "AIRTEL";
			    break;
			}
			case "ANDB": {
			    logo = "ANDB";
			    break;
			}
			case "AUBL": {
			    logo = "AUBL";
			    break;
			}
			case "BDBL": {
			    logo = "BBL";
			    break;
			}
			case "BCBM": {
			    logo = "BHARAT";
			    break;
			}
			case "MAHB": {
			    logo = "BOM";
			    break;
			}
			case "BBKM": {
			    logo = "BOR";
			    break;
			}
			case "CBIN": {
			    logo = "CBI";
			    break;
			}
			case "CITI": {
			    logo = "CITI";
			    break;
			}
			case "CIUB": {
			    logo = "CITIUB";
			    break;
			}
			case "CORP": {
			    logo = "CORP";
			    break;
			}
			case "CSBK": {
			    logo = "CSB";
			    break;
			}
			case "DBSS": {
			    logo = "DBS";
			    break;
			}
			case "DCBL": {
			    logo = "DCB";
			    break;
			}
			case "BKDN": {
			    logo = "DENA";
			    break;
			}
			case "DEUT": {
			    logo = "DEUTS";
			    break;
			}
			case "DLXB": {
			    logo = "DHAN";
			    break;
			}
			case "FDRL": {
			    logo = "FDEB";
			    break;
			}
			case "PJSB": {
			    logo = "GPPB";
			    break;
			}
			case "IDIB": {
			    logo = "INDB";
			    break;
			}
			case "INDB": {
			    logo = "INDS";
			    break;
			}
			case "VYSA": {
			    logo = "ING";
			    break;
			}
			case "IOBA": {
			    logo = "IOB";
			    break;
			}
			case "JAKA": {
			    logo = "JKB";
			    break;
			}
			case "JSBP": {
			    logo = "JSB";
			    break;
			}
			case "KARB": {
			    logo = "KTKB";
			    break;
			}
			case "KVBL": {
			    logo = "KVB";
			    break;
			}
			case "LAVB": {
			    logo = "LVB";
			    break;
			}
			case "ORBC": {
			    logo = "OBPRF";
			    break;
			}
			case "PMCB": {
			    logo = "PSB";
			    break;
			}
			case "RATN": {
			    logo = "RATN";
			    break;
			}
			case "SIBL": {
			    logo = "SIB";
			    break;
			}
			case "SURY": {
			    logo = "SSFB";
			    break;
			}
			case "SRCB": {
			    logo = "STB";
			    break;
			}
			case "SYNB": {
			    logo = "SYNBK";
			    break;
			}
			case "TMBL": {
			    logo = "TNMB";
			    break;
			}
			case "UTBI": {
			    logo = "UBI";
			    break;
			}
			case "UCBA": {
			    logo = "UCO";
			    break;
			}
			case "UBIN": {
			    logo = "UNI";
			    break;
			}
			case "VIJB": {
			    logo = "VJYA";
			    break;
			}
			default: {
				logo = "deafultbank";
				break;
			}
		}
		return "https://staticgw1.paytm.com/native/bank/" + logo + ".png";
	}

}
