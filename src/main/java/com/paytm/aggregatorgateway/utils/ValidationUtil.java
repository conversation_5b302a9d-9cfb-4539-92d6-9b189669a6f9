package com.paytm.aggregatorgateway.utils;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class ValidationUtil {


    public static void validateInteger(String integer) throws Exception{
        log.info("Validating integers");
        String integerRegex = "-?\\d+$";
        Pattern p = Pattern.compile(integerRegex);
        if(StringUtils.isBlank(integer))
            return;
        Matcher m = p.matcher(integer);
        if(!m.matches()){
            log.info("Invalid Integer");
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION,"Invalid Params");
        }
    }


    public static void validateAlphaNumeric(String str) throws Exception{
        log.info("Validating alpha-numeric string");
        String alphaNumericStringRegex = "^[a-zA-Z0-9]*$";
        if(StringUtils.isBlank(str))
            return;
        Pattern p = Pattern.compile(alphaNumericStringRegex);
        Matcher m = p.matcher(str);
        if(!m.matches()){
            log.info("Not a alphaNumeric value");
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, "Validation check for alphanumeric value failed");
        }

    }

    public static void validateDate(String date) throws Exception{
        log.info("Validating date");
        if(StringUtils.isBlank(date))
            return;
        String dateRegex = "\\\\d{4}-\\\\d{2}-\\\\d{2}";
        Pattern p = Pattern.compile(dateRegex);
        Matcher m = p.matcher(date);
        if(!m.matches())
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION,"Invalid date passed");
    }

    public static void validateAlphanumericwithhyphen(String str) throws Exception{
        log.info("Validating alpha-numeric string with hyphen");
        String alphaNumericStringRegex = "^[a-zA-Z0-9-]*$";
        if(StringUtils.isBlank(str))
            return;
        Pattern p = Pattern.compile(alphaNumericStringRegex);
        Matcher m = p.matcher(str);
        if(!m.matches()){
            log.info("Not a alphaNumeric with hyphen value");
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION, "Validation check for alphanumeric value with hyphen failed");
        }

    }


}

