package com.paytm.aggregatorgateway.utils;

import com.paytm.aggregatorgateway.constants.DomainConstants;
import com.paytm.aggregatorgateway.vo.MonitorLog;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.io.InputStream;
import java.net.URI;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
public class MonitorLogger implements DomainConstants {

	private static final Logger MONITOR_LOGGER = LogManager.getLogger("MonitorLogger");
	private static final Logger LOGGER = LogManager.getLogger(MonitorLogger.class);
	private static final String[] MASK_KEYS = {"key", "password"};
	private static final Pattern PATTERN_MASK_URLENCODED = Pattern.compile("((&|^)(" + StringUtils.join(MASK_KEYS, '|') + ")=)([^&$]*)");
	private static final Pattern PATTERN_MASK_JSON = Pattern.compile("(\"(" + StringUtils.join(MASK_KEYS, '|') + ")\":)(\"[^\"]+\")");
	private static final Set<String> PROMO_ENGINE_SUCCESS_ID = new HashSet<>(Arrays.asList(new String[] {"1"}));
	private static final String DESC_KEY_NOT_FOUND = "KEY_NOT_FOUND:";
	private static final DateTimeFormatter FORMATTER_LOG_DATETIME = DateTimeFormatter.ofPattern("dd MMM yyyy HH:mm:ss,SSS");
	private static final Set<String> SKIP_REQ_LOG_TRIMMING = new HashSet<>(Arrays.asList("SAMPLE_API"));
	private static final Set<String> SKIP_RESP_LOG_TRIMMING = new HashSet<>(Arrays.asList("SAMPLE_API"));
	private static final Pattern PATTERN_BOSS_MERCHANT_APIS_PATH = Pattern.compile("[/]{1,2}api/v(\\d)/merchant/(\\w+)/(\\w+)");
	private static final String DESC_KEY_TYPE_MISMATCH = "KEY_TYPE_MISMATCH:";


	private static final Pattern PATTERN_PROMO_SUPERCASH_TXN = Pattern.compile("/v1/mpromocard/s2s/supercash/(.+)/transactions");
	private static final Pattern PATTERN_PROMO_CAMPAIGNS = Pattern.compile("/v1/mpromocard/s2s/campaigns(/.+)?");
	private static final Pattern PATTERN_PROMO_SUPERCASH = Pattern.compile("/v1/mpromocard/s2s/supercash(/.+)?");
	private static final Pattern PATTERN_PROMO_CAMPAIGNS_V2 = Pattern.compile("/v2/mpromocard/s2s/campaigns(/.+)?");
	private static final Pattern PATTERN_PROMO_SUPERCASH_CAMPAIGN_GAMES = Pattern.compile("/v2/mpromocard/s2s/supercash/campaign-games(/.+)?");

	private static int maxBodyLogLength;

	@Autowired
	private Environment environment;

	private static enum Status {
		SUCCESS,
		FAILURE,
		ERROR,
		UNKNOWN;
	}

	
	public static class ResponseResult {
		/**
		 * String domain: The domain of the API, used to group logs
		 */
		private String domain;
		/**
		 * String apiName: The user-friendly name of the API, used to group logs
		 */
		private String apiName;
		/**
		 * Status status: Inferred status of the response from responseBody
		 */
		private Status status;
		/**
		 * String desc: Description of the reason behind the status
		 */
		private String desc;

		ResponseResult(String apiName, Status status, String desc) {
			this.apiName = apiName;
			this.status = status;
			this.desc = desc;
		}

		public String getApiName() {
			return apiName;
		}

		public Status getStatus() {
			return status;
		}

		public String getDesc() {
			return desc;
		}

		public String getDomain() {
			return domain;
		}

		public void setDomain(String domain) {
			this.domain = domain;
		}
	}

	@PostConstruct
	private void initialise() {
		maxBodyLogLength = environment.getProperty("monitor.log.body.length", Integer.class, 1500);
	}

	@Async("monitorLogExecutor")
	public void log(MonitorLog monitorLog) {
		ResponseResult responseResult = getResponseResult(monitorLog);
		log(monitorLog, responseResult);
	}

	private static void log(MonitorLog monitorLog, ResponseResult responseResult) {

		long timeTaken = -1;
		StringBuffer request = null;
		StringBuffer response = null;
		StringBuffer uri = null;
		StringBuilder exceptions = null;
		String desc = null;
		/*
		 * SKIPPING HEADER LOGGING
		 * 
		 * String requestHeaders = null;
		 * 
		 * String responseHeaders = null;
		 */

		try {
			if (monitorLog.getStartTime() != null && monitorLog.getEndTime() != null) {
				timeTaken = ChronoUnit.MILLIS.between(monitorLog.getStartTime(), monitorLog.getEndTime());
			}

			try {
				if (monitorLog.getRequest() instanceof UrlEncodedFormEntity) {
					InputStream dataStream = ((UrlEncodedFormEntity) monitorLog.getRequest()).getContent();
					request = toBuffer(IOUtils.toString(dataStream));
					request = mask(request, PATTERN_MASK_URLENCODED);
				} else if (monitorLog.getRequest() instanceof StringBuffer) {
					request = (StringBuffer) monitorLog.getRequest();
				} else if (monitorLog.getRequest() instanceof Map) {
					request = toBuffer(monitorLog.getRequest());
				} else {
					request = toBuffer(Objects.toString(monitorLog.getRequest(), null));
				}

				request = replace(request, '|', '~');
			} catch (Exception e) {
				LOGGER.error("Unable to log request", e);
				monitorLog.appendException("LOG_REQ_EX");
			}

			try {
				response = toBuffer(monitorLog.getResponse());
				response = mask(response, PATTERN_MASK_JSON);
				response = replace(response, '|', '~');
			} catch (Exception e) {
				LOGGER.error("Unable to log response", e);
				monitorLog.appendException("LOG_RESP_EX");
			}

			if(!SKIP_REQ_LOG_TRIMMING.contains(responseResult.getApiName())) {
				request = abbreviateMiddle(request, " ~~~ ", maxBodyLogLength);
			}
			if(!SKIP_RESP_LOG_TRIMMING.contains(responseResult.getApiName())) {
				response = abbreviateMiddle(response, " ~~~ ", maxBodyLogLength);
			}
			/*
			 * SKIPPING HEADER LOGGING
			 * 
			 * requestHeaders =
			 * MappingUtils.OBJECT_MAPPER.writeValueAsString(auditLog.getRequestHeaders());
			 * 
			 * responseHeaders =
			 * MappingUtils.OBJECT_MAPPER.writeValueAsString(auditLog.getResponseHeaders());
			 */


			uri = toBuffer(monitorLog.getUri());
			uri = mask(uri, PATTERN_MASK_URLENCODED);
			uri = replace(uri, '|', '~');

			exceptions = replace(monitorLog.getException(), '|', '~');

			desc = StringUtils.replaceChars(responseResult.getDesc(), '|', '~');

		} catch (Exception e) {
			LOGGER.error("Could not parse message", e);
			exceptions.append("*LOG_EX");
		}

		log(responseResult.getDomain(), responseResult.getApiName(), responseResult.getStatus(), desc, monitorLog.getHttpStatus(), timeTaken,
				monitorLog.getStartTime(), monitorLog.getCaller(), uri, monitorLog.getMethod(), request, response, monitorLog.getUid(),
				monitorLog.getEid(), exceptions, monitorLog.getTraceId());
	}

	public ResponseResult getResponseResult(MonitorLog monitorLog) {
		
		String domain = DomainUtils.DOMAIN_MAP.getOrDefault(monitorLog.getUri().getHost(), monitorLog.getUri().getHost());
		ResponseResult responseResult = null;
		switch (domain) {
			case UMP:
				responseResult =  getResponseResultUMP(monitorLog);
				break;
			case PROMO_CLM:
				responseResult = getResponseResultPromoClm(monitorLog);
				break;
			case BOSS:
				responseResult =  getResponseResultBoss(monitorLog);
				break;
			case CLEVERTAP:
				responseResult =  getResponseResultClevertap(monitorLog);
				break;
			case SUBSCRIPTION:
				responseResult =  getResponseResultSubscription(monitorLog);
				break;
			case UPS:
				responseResult =  getResponseResultUPS(monitorLog);
				break;
			case KYB:
				responseResult =  getResponseResultKyb(monitorLog);
				break;
			case REWARDS:
				responseResult =  getResponseResultRewards(monitorLog);
				break;
			case EOS:
				responseResult =  getResponseResultEOS(monitorLog);
				break;
		}
		
		//to avoid writing generateUnknownResponse() inside every case method
		if(Objects.isNull(responseResult) || StringUtils.isBlank(responseResult.getApiName())) {
			responseResult = generateUnknownResponse(monitorLog.getUri());
		}
		responseResult.setDomain(domain);
		return responseResult;
	}

	private static <K, V> Map<K, V> toMapSafe(Object mapObject) {
		try {
			if (mapObject instanceof Map) {
				return (Map<K, V>) mapObject;
			}
		} catch (Exception e) {
			//NO HANDLING
		}
		return null;
	}

	private static ResponseResult getResponseResultPromoClm(MonitorLog monitorLog) {
		Status status = Status.SUCCESS;
		String apiName;
		String desc = null;
		Object responseObject = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()),
				Object.class);
		Map<String, Object> responseMap = toMapSafe(responseObject);
		String resultSataus = MapUtils.getString(responseMap, "status");
		if (resultSataus == null) {
			status = Status.ERROR;
			desc = DESC_KEY_NOT_FOUND + "status";
		} else if (PROMO_ENGINE_SUCCESS_ID.contains(resultSataus)) {
			status = Status.SUCCESS;
		} else {
			status = Status.FAILURE;
			desc = "status:" + resultSataus;
		}
		String urlPath = monitorLog.getUri().getPath();
		Matcher supercashTxnMatcher = PATTERN_PROMO_SUPERCASH_TXN.matcher(urlPath);
		Matcher campaignsMatcher = PATTERN_PROMO_CAMPAIGNS.matcher(urlPath);
		Matcher supercashMatcher = PATTERN_PROMO_SUPERCASH.matcher(urlPath);
		Matcher supercashCampaignGamesMatcher = PATTERN_PROMO_SUPERCASH_CAMPAIGN_GAMES.matcher(urlPath);
		Matcher campaignsV2Matcher = PATTERN_PROMO_CAMPAIGNS_V2.matcher(urlPath);
		if (supercashTxnMatcher.matches()) {
			apiName = "PROMO_ENGINE_SUPERCASH_TXN";
		}
		else if(supercashMatcher.matches()) {
			if (supercashMatcher.group(1) != null) {
				apiName = "PROMO_ENGINE_FETCH_CAMPAIGN_GAMES";
			} else {
				apiName = "PROMO_ENGINE_FETCH_GAME_LIST";
			}
		}
		else if (campaignsMatcher.matches()) {
			if (campaignsMatcher.group(1) != null) {
				apiName = "PROMO_ENGINE_ACTIVATE_OFFERS";
			} else {
				apiName = "PROMO_ENGINE_FETCH_ALL_OFFERS";
			}
		} else if (supercashCampaignGamesMatcher.matches()) {
			apiName = "PROMO_ENGINE_V2_FETCH_CAMPAIGN-GAMES";
		} else if (campaignsV2Matcher.matches()) {
			apiName = "PROMO_ENGINE_V2_SELECT_OFFER";
		}
		else {
			StringBuilder apiNameBuilder = toBuilder(urlPath.substring(19));
			apiNameBuilder = toUpperCase(apiNameBuilder);
			apiNameBuilder = replace(apiNameBuilder, '/', '_');
			apiName = apiNameBuilder.insert(0, "PROMO_ENGINE_").toString();
		}
		return new ResponseResult(apiName, status, desc);
	}


	private static final void log(String domain, String apiName, Status status, String desc, int httpStatus, long timeTaken, LocalDateTime startTime,
			String caller, StringBuffer uri, HttpMethod method, StringBuffer request, StringBuffer response, String uid, Long eid,
			StringBuilder exception,String traceId) {

		String startTimeString = FORMATTER_LOG_DATETIME.format(startTime);

		Map<String,Object> loggerMap = new LinkedHashMap<>();
		loggerMap.put("domain",domain);
		loggerMap.put("apiName",apiName);
		loggerMap.put("status",status);
		loggerMap.put("desc",desc);
		loggerMap.put("httpStatus",httpStatus);
		loggerMap.put("timeTaken",timeTaken);
		loggerMap.put("startTime",startTimeString);
		loggerMap.put("caller",caller);
		loggerMap.put("uri",uri);
		loggerMap.put("method",method);
		loggerMap.put("request",request);
		loggerMap.put("response",response);
		loggerMap.put("uid",uid);
		loggerMap.put("eid",eid);
		loggerMap.put("exception",exception);
		loggerMap.put("traceId",traceId);
		MONITOR_LOGGER.info(loggerMap.toString());
	}

	/**
	 * Utility Methods
	 */

	private static StringBuffer mask(StringBuffer body, Pattern maskPattern) {
		if (body == null) {
			return null;
		}
		StringBuffer buffer = new StringBuffer();
		Matcher matcher = maskPattern.matcher(body);
		while (matcher.find()) {
			matcher.appendReplacement(buffer, "$1*****");
		}
		matcher.appendTail(buffer);
		return buffer;
	}

	private static StringBuffer abbreviateMiddle(StringBuffer str, String middle, int length) {
		if (str == null || str.length() == 0 || middle == null || middle.length() == 0) {
			return str;
		}

		if (length >= str.length() || length < (middle.length() + 2)) {
			return str;
		}

		int targetSting = length - middle.length();
		int startOffset = targetSting / 2 + targetSting % 2;
		int endOffset = str.length() - targetSting / 2;

		str.replace(startOffset, endOffset, middle);

		return str;
	}

	private static StringBuffer replace(StringBuffer str, char oldChar, char newChar) {
		if (str != null && oldChar != newChar) {
			for (int i = 0; i < str.length(); i++) {
				if (str.charAt(i) == oldChar) {
					str.setCharAt(i, newChar);
				}
			}
		}
		return str;
	}

	private static StringBuilder replace(StringBuilder str, char oldChar, char newChar) {
		if (str != null && oldChar != newChar) {
			for (int i = 0; i < str.length(); i++) {
				if (str.charAt(i) == oldChar) {
					str.setCharAt(i, newChar);
				}
			}
		}
		return str;
	}

	/**
	 * Used StringBuffer to use mask() as Matcher.appendReplacement only works with StringBuffer, if
	 * not required - use StringBuilder as StringBuffer is synchronized hence slower
	 */
	private static StringBuffer toBuffer(Object strObject) {
		if (strObject instanceof StringBuffer) {
			return (StringBuffer) strObject;
		} else if (strObject == null) {
			return null;
		} else {
			return new StringBuffer(strObject.toString());
		}
	}

	private static ResponseResult generateUnknownResponse(URI uri) {
		return new ResponseResult(uri.getPath(), Status.UNKNOWN, "UNKNOWN_API");
	}

	private static ResponseResult getResponseResultUMP(MonitorLog monitorLog) {
		Status status = Status.SUCCESS;
		String apiName;
		String desc = null;
		Map<String, Object> responseMap = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Map.class);
		switch (monitorLog.getUri().getPath()) {
			case "/api/v1/context":
				apiName = "USER_CONTEXT";
				if (monitorLog.getHttpStatus() !=200) {
					status = Status.FAILURE;
					desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
				}
				break;
			default:
				return generateUnknownResponse(monitorLog.getUri());
		}

		return new ResponseResult(apiName, status, desc);
	}
	private static ResponseResult getResponseResultEOS(MonitorLog monitorLog) {
		Status status = Status.SUCCESS;
		String apiName;
		String desc = null;
		Map<String, Object> responseMap = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Map.class);
		if (monitorLog.getUri().getPath().contains("/eos/getLastTransactionAddressDetails/deviceId/")) {
				apiName = "GET_LAST_TRANSACTION_ADDRESS";
				if (monitorLog.getHttpStatus() !=200) {
					status = Status.FAILURE;
					desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
				}}
		else
			return generateUnknownResponse(monitorLog.getUri());

		return new ResponseResult(apiName, status, desc);
	}
	private static ResponseResult getResponseResultBoss(MonitorLog monitorLog) {
		String apiName=null;
		Status status = Status.SUCCESS;
		String desc = null;

		Object responseObject = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Object.class);
		Map<String, Object> responseMap = toMapSafe(responseObject);

		Matcher bossMerchantApisMatcher = PATTERN_BOSS_MERCHANT_APIS_PATH.matcher(monitorLog.getUri().getPath());
		if (bossMerchantApisMatcher.matches()) {
			String version = bossMerchantApisMatcher.group(1);
			String preference = bossMerchantApisMatcher.group(2);
			String apiPath = bossMerchantApisMatcher.group(3);
			switch (apiPath)
			{
				case "info":
					apiName = "FETCH_MERCHANT_DETAILS";
					if (MapUtils.getString(responseMap, "mid") == null) {
						status = Status.FAILURE;
						desc = DESC_KEY_NOT_FOUND + "mid;code:" + getMapFirstEntrySafe(responseMap).getKey();
					}
					break;
				default:
					return generateUnknownResponse(monitorLog.getUri());

			}
			return new ResponseResult(apiName + "_V" + version, status, desc);
		}
		else if (monitorLog.getUri().getPath().matches("/api/v(\\d)/communication/(\\w+)/(\\w+)")) {
			apiName = "BOSS_MERCHANT_COMMUNICATION_GET";
			if (monitorLog.getHttpStatus() !=200) {
				status = Status.FAILURE;
				desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
			}

		} else if (monitorLog.getUri().getPath().matches("/api/v(\\d)/communication/(\\w+)")) {
			apiName = "BOSS_MERCHANT_COMMUNICATION_UPDATE";
			if (monitorLog.getHttpStatus() !=200) {
				status = Status.FAILURE;
				desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
			}

		} else {
			 return generateUnknownResponse(monitorLog.getUri());
		 }
			return new ResponseResult(apiName, status, desc);
	}

	private static ResponseResult getResponseResultSubscription(MonitorLog monitorLog) {
		String apiName=null;
		Status status = Status.SUCCESS;
		String desc = null;

		Object responseObject = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Object.class);
		Map<String, Object> responseMap = toMapSafe(responseObject);
		switch (monitorLog.getUri().getPath()) {
			case "/api/v1/subscription":
				if(monitorLog.getMethod().matches("GET")) {
					apiName = "FETCH_SUBSCRIPTION";
					if (monitorLog.getHttpStatus() != 200) {
						status = Status.FAILURE;
						desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
					}
				}
				else if(monitorLog.getMethod().matches("POST")) {
					apiName = "CREATE_SUBSCRIPTION";
					if (monitorLog.getHttpStatus() != 200) {
						status = Status.FAILURE;
						desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
					}
				}
				else if(monitorLog.getMethod().matches("PUT")) {
					apiName = "UPDATE_SUBSCRIPTION";
					if (monitorLog.getHttpStatus() != 200) {
						status = Status.FAILURE;
						desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
					}
				}
				break;
			default:
				return generateUnknownResponse(monitorLog.getUri());
		}
		return new ResponseResult(apiName, status, desc);
	}

	private ResponseResult getResponseResultClevertap(MonitorLog monitorLog) {
		Status status = Status.SUCCESS;
		String apiName;
		String desc = null;
		Map<String, Object> responseMap = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Map.class);
		switch (monitorLog.getUri().getPath()) {
			case "/pull":
				apiName = "FETCH_ID";
				if (monitorLog.getHttpStatus() !=200) {
					status = Status.FAILURE;
					desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
				}
				break;
			default:
				return generateUnknownResponse(monitorLog.getUri());
		}

		return new ResponseResult(apiName, status, desc);
	}

	private ResponseResult getResponseResultUPS(MonitorLog monitorLog) {
		Status status = Status.SUCCESS;
		String apiName;
		String desc = null;
		Map<String, Object> responseMap = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Map.class);
		switch (monitorLog.getUri().getPath()) {
			case "/ups/internal/v1/entity-preferences":
				if(monitorLog.getMethod().matches("PUT")) {
					apiName = "API_INTERNAL_ENTITY_PREFERENCE_PUT";
					if (monitorLog.getHttpStatus() != 200) {
						status = Status.FAILURE;
						desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
					}
				}
				else if(monitorLog.getMethod().matches("POST")) {
					apiName = "API_INTERNAL_ENTITY_PREFERENCE_POST";
					if (monitorLog.getHttpStatus() != 200) {
						status = Status.FAILURE;
						desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
					}
				}
				else {
					return generateUnknownResponse(monitorLog.getUri());
				}
				break;
			default:
				return generateUnknownResponse(monitorLog.getUri());
		}

		return new ResponseResult(apiName, status, desc);
	}

	/**
	 * Used StringBuffer to use mask() as Matcher.appendReplacement only works with StringBuffer, if
	 * not required - use StringBuilder as StringBuffer is synchronized hence slower
	 */
	private static StringBuilder toBuilder(Object strObject) {
		if (strObject instanceof StringBuilder) {
			return (StringBuilder) strObject;
		} else if (strObject == null) {
			return null;
		} else {
			return new StringBuilder(strObject.toString());
		}
	}

	private static StringBuilder toUpperCase(StringBuilder str) {
		if (str != null) {
			for (int i = 0; i < str.length(); i++) {
				str.setCharAt(i, Character.toUpperCase(str.charAt(i)));
			}
		}
		return str;
	}

	private static <K, V> Map.Entry<K, V> getMapFirstEntrySafe(Map<K, V> map) {
		try {
			if (map != null) {
				Map.Entry<K, V> firstEntry = map.entrySet().iterator().next();
				return firstEntry;
			}
		} catch (Exception e) {
			//NO HANDLING
		}
		return new AbstractMap.SimpleImmutableEntry<K, V>(null, null);
	}

	private static ResponseResult getResponseResultKyb(MonitorLog monitorLog) {
		Status status = Status.SUCCESS;
		String apiName;
		String desc = null;
		Map<String, Object> responseMap = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Map.class);
		switch (monitorLog.getUri().getPath()) {
			case "/kyb/getRootCustid":
				apiName = "GET_ROOT_CUSTID";
				if (monitorLog.getHttpStatus() !=200) {
					status = Status.FAILURE;
					desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
				}
				break;
			default:
				return generateUnknownResponse(monitorLog.getUri());
		}

		return new ResponseResult(apiName, status, desc);
	}

	private static ResponseResult getResponseResultRewards(MonitorLog monitorLog) {
		Status status = Status.SUCCESS;
		String apiName;
		String desc = null;
		Map<String, Object> responseMap = MappingUtils.convertJsonToTypeSafe(String.valueOf(monitorLog.getResponse()), Map.class);
		switch (monitorLog.getUri().getPath()) {
			case "/fund-service/fundproxy/loyaltypoints/v2/checkbalance":
				apiName = "LOYALITYPOINTS_V2_CHECKBALANCE";
				if (monitorLog.getHttpStatus() !=200) {
					status = Status.FAILURE;
					desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
				}
				break;
			case "/fund-service/fundproxy/loyaltypoints/v2/passbook":
				apiName = "LOYALITYPOINTS_V2_PASSBOOK";
				if (monitorLog.getHttpStatus() !=200) {
					status = Status.FAILURE;
					desc = "!200;code:" + getMapFirstEntrySafe(responseMap).getKey();
				}
				break;
			default:
				return generateUnknownResponse(monitorLog.getUri());
		}

		return new ResponseResult(apiName, status, desc);
	}

}
