package com.paytm.aggregatorgateway.utils;

import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.pgdashboard.commons.dto.User;
import com.paytm.dashboard.security.UserAuthentication;
import com.paytm.dashboard.security.UserDetailConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Collection;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class SecurityUtils {


	/**
	 * Get the logged-in user from security context
	 * 
	 * @return
	 */
	public static User getLoggedInUser() {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth != null && auth instanceof UserAuthentication) {
			return ((UserDetailConfig) auth.getDetails()).getUser();
		}
		return null;

	}
	
	public static Merchant getCurrentMerchant() {
		Merchant merchantExists = null;
		User user = getLoggedInUser();
		if(user != null){
			List<Merchant> merchants = user.getMerchants();
			
			if (CollectionUtils.isNotEmpty(merchants)) {
				for (Merchant merchant : merchants) {
					if (merchant.getId() == user.getCurrentMerchant()) {
						merchantExists = merchant;
						break;
					}
				}
			}
		}
		return merchantExists;
	}

	public static boolean hasPermission(String permission) {
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if (auth != null && auth instanceof UserAuthentication) {
			Collection<? extends GrantedAuthority> authorities = ((UserAuthentication) auth).getAuthorities();
			return authorities.contains(new SimpleGrantedAuthority("ROLE_" + permission));
		}
		return false;
	}
	
}
