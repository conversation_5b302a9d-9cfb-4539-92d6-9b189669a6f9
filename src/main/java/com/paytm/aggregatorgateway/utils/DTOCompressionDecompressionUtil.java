package com.paytm.aggregatorgateway.utils;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.xerial.snappy.Snappy;

import java.util.Base64;
import java.util.Collections;
import java.util.List;

public class DTOCompressionDecompressionUtil {

    private static final Logger LOGGER = LogManager.getLogger(DTOCompressionDecompressionUtil.class);
    public static final ObjectMapper jsonMapper;
    static {
        jsonMapper = new ObjectMapper();
    }
    public static String compressAndEncodeDTO(Object dto) throws Exception {
        // LOGGER.info("inside compressAndEncodeDTO ");
        if(dto != null) {
            String jsonString = jsonMapper.writeValueAsString(dto);
            byte[] compressedBytes = Snappy.compress(jsonString.getBytes());
            return Base64.getEncoder().encodeToString(compressedBytes);
        }
        else{
            LOGGER.error("Dto is null");
            return "";
        }

    }

    public static <T> T decodeAndDecompressDTO(String base64Encoded, Class<T> dtoClass) throws Exception {
        // LOGGER.info("inside decodeAndDecompressDTO");
        if(StringUtils.isBlank(base64Encoded)){
            return null;
        }
        byte[] compressedBytes = Base64.getDecoder().decode(base64Encoded);
        byte[] decompressedBytes = Snappy.uncompress(compressedBytes);
        String jsonString = new String(decompressedBytes);
        return jsonMapper.readValue(jsonString, dtoClass);
    }
    public static <T> List<T> decodeAndDecompressDTOList(String base64Encoded, Class<T> dtoClass) throws Exception {
        LOGGER.info("inside decodeAndDecompressDTOList");
        if (StringUtils.isBlank(base64Encoded)) {
            return Collections.emptyList();
        }

        byte[] compressedBytes = Base64.getDecoder().decode(base64Encoded);
        byte[] decompressedBytes = Snappy.uncompress(compressedBytes);
        String jsonString = new String(decompressedBytes);

        ObjectMapper objectMapper = new ObjectMapper();
        JavaType type = objectMapper.getTypeFactory().constructCollectionType(List.class, dtoClass);
        return objectMapper.readValue(jsonString, type);
    }

    public static String compressAndEncodeString(String str) throws Exception{
        //LOGGER.info("inside compressAndEncodeString");
        if(StringUtils.isNotBlank(str)){
            byte[] compressedBytes = Snappy.compress(str.getBytes());
            String encoded =  Base64.getEncoder().encodeToString(compressedBytes);
            //LOGGER.info("encoded String {} for str {}", encoded, str);
            return encoded;
        }
        else{
            LOGGER.error("String is blank");
            return str;
        }

    }
    public static String decodeAndDecompressString(String base64Encoded) throws Exception{
        // LOGGER.info("inside decodeAndDecompressString");
        if(StringUtils.isBlank(base64Encoded)){
            return null;
        }
        byte[] compressedBytes = Base64.getDecoder().decode(base64Encoded);
        byte[] decompressedBytes = Snappy.uncompress(compressedBytes);
        String decoded =  new String(decompressedBytes);
        //LOGGER.info("decoded string {} for string {}", decoded, base64Encoded);
        return decoded;
    }
}
