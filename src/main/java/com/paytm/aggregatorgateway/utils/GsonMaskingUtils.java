package com.paytm.aggregatorgateway.utils;

import com.google.gson.JsonObject;
import org.springframework.stereotype.Component;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Component
public class GsonMaskingUtils {
    private static final Logger LOGGER = LogManager.getLogger(GsonMaskingUtils.class);
    static Gson gson = new GsonBuilder().create();
    private static Set<String> sensitiveKeysSet;

    @Value("${logger.monitor.sensitive.key}")
    public void setSensitiveKeys(String str) {
        if (StringUtils.isNotBlank(str))
            GsonMaskingUtils.sensitiveKeysSet = new HashSet<>(Arrays.asList(str.split(",")));
        else
            GsonMaskingUtils.sensitiveKeysSet = new HashSet<>();
        LOGGER.info("sensitiveKeysSet : {}", sensitiveKeysSet);
    }

    public static Object maskResponseBody(Object body) {
        try {
            String strBody;
            if(body instanceof String) {
                strBody = (String) body;
            } else {
                strBody = MappingUtils.convertObjectToJson(body);
            }
            JsonElement jsonElement = gson.fromJson(strBody, JsonElement.class);
            maskSensitiveFields(jsonElement);
            return gson.toJson(jsonElement).toString();
        }catch (Exception ex) {
            LOGGER.error("Error occurred while masking the body: ", ex);
        }
        return body;
    }

    public static Object maskRequestBody(Object requestBody) {
        return maskResponseBody(requestBody);
    }

    private static void maskSensitiveFields(JsonElement jsonElement) {
        if (jsonElement.isJsonObject()) {
            JsonObject jsonObject = jsonElement.getAsJsonObject();
            for (String key : jsonObject.keySet()) {
                JsonElement childElement = jsonObject.get(key);
                if (isSensitiveField(key)) {
                    jsonObject.add(key, gson.toJsonTree("********"));
                } else if (childElement.isJsonObject() || childElement.isJsonArray()) {
                    maskSensitiveFields(childElement);
                }
            }
        } else if (jsonElement.isJsonArray()) {
            for (JsonElement childElement : jsonElement.getAsJsonArray()) {
                maskSensitiveFields(childElement);
            }
        }
    }

    private static boolean isSensitiveField(String field) {
        return sensitiveKeysSet.contains(field);
    }

}
