package com.paytm.aggregatorgateway.utils.metrics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MetricFilterConfig {

    @Autowired
    private MetricUtils metricUtils;

    @Bean
    public UmpMetricFilter myFilterRegistrationBean() {
        return new UmpMetricFilter(metricUtils);
    }
}
