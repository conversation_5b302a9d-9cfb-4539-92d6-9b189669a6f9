package com.paytm.aggregatorgateway.utils.metrics;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * This filter will push metrics for all incoming requests in the application.
 * 
 * <AUTHOR>
 *
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
public class UmpMetricFilter implements Filter{
	
	private MetricUtils metricUtils;
	
	private static final Logger LOGGER = LogManager.getLogger(UmpMetricFilter.class);
	
	public UmpMetricFilter(MetricUtils metricUtils) {
		this.metricUtils = metricUtils;
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		
		HttpServletRequest req = (HttpServletRequest) request;
		LocalDateTime startTime = LocalDateTime.now();
		String uri = req.getRequestURI();
		HttpMethod method =  HttpMethod.valueOf(req.getMethod());
		String clientId = req.getHeader("client-id");

		try {
			LOGGER.debug("Incrementing api {} hit count", uri);
			metricUtils.incrementIntApiHitsCounter(uri, method, clientId);
		}catch(Exception e) {
			LOGGER.error("Failed to increament counter: {}", e);
		}
			
		chain.doFilter(request, response);
		
		try {
			LocalDateTime endTime = LocalDateTime.now();
			HttpServletResponse resp = (HttpServletResponse) response;
			HttpStatus status = HttpStatus.valueOf(resp.getStatus());
			LOGGER.debug("Pushing status:{} and latency:{} ", status, ChronoUnit.MILLIS.between(startTime, endTime));
			metricUtils.incrementIntApiStatusCounter(uri, method, status, clientId);
			metricUtils.recordIntApiLatencyHistogram(uri, method, startTime, endTime, status);
		}catch(Exception e) {
			LOGGER.error("Failed to push status and latency metrics: {}", e);
		}
	}

	@Override
	public void destroy() {
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		
	}

}
