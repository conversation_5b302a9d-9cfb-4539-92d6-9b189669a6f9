package com.paytm.aggregatorgateway.utils.metrics;

import com.paytm.aggregatorgateway.utils.metrics.MetricConstants.TagName;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.core5.pool.PoolStats;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.regex.Matcher;


/**
 * 
 * <AUTHOR>
 *
 */
@Component
public class MetricUtils {

	@Value("${push.upstream.api.metrics:true}")
	private boolean pushUpstreamApiMetrics;
	
	@Autowired
	private Environment environment;
	
	private String hostNameTag;

	@Value("${spring.profiles.active}")
	private String profileActive;

	private static final Logger LOGGER = LogManager.getLogger(MetricUtils.class);
	public static final String APP_DOMAIN = "BFF_V2";
	public static final String JOIN = ":";

	/**
	 * {@link MetricClient#primaryStatsDClient()}
	 */
    @Autowired
    StatsDClient statsdClient;	
    
    
    @PostConstruct
    public void getPodId() {
    	String podId = environment.getProperty("HOSTNAME");
		this.hostNameTag = StringUtils.isBlank(podId)?null:TagName.HOST_NAME + JOIN + podId;
	}

	private String[] addPodLevelMetrics(String... metrictags) {
		if(hostNameTag != null)
			metrictags = ArrayUtils.addAll(metrictags, hostNameTag);
		if(StringUtils.isNotBlank(profileActive))
			metrictags = ArrayUtils.addAll(metrictags, TagName.PROFILE_ACTIVE + JOIN + profileActive);
		LOGGER.debug("Host name: {}", hostNameTag);
		return metrictags;
	}

	public void pushCounterMetrics(String metricName, String... counterMetrictags) {
		String upstreamDomain = StringUtils.isNotBlank(MDC.get("upstreamDomain")) ? MDC.get("upstreamDomain").toString() : "";
		if(upstreamDomain.equalsIgnoreCase("localhost"))
			metricName = metricName.replace("api", "warmup");
		counterMetrictags = addPodLevelMetrics(counterMetrictags);
        statsdClient.incrementCounter(metricName, counterMetrictags);
    }

    private void pushHistogramValues(String metricName, double elapsedTime, String... histogramTags) {
		String upstreamDomain = StringUtils.isNotBlank(MDC.get("upstreamDomain")) ? MDC.get("upstreamDomain").toString() : "";
		if(upstreamDomain.equalsIgnoreCase("localhost"))
			metricName = metricName.replace("api", "warmup");
		histogramTags = addPodLevelMetrics(histogramTags);
        statsdClient.recordHistogramValue(metricName, elapsedTime, histogramTags);
    }

	private void pushGaugeValue(String metricName, Integer value, String... gaugeMetrictags){
		String upstreamDomain = StringUtils.isNotBlank(MDC.get("upstreamDomain")) ? MDC.get("upstreamDomain").toString() : "";
		if(upstreamDomain.equalsIgnoreCase("localhost"))
			metricName = metricName.replace("api", "warmup");
		gaugeMetrictags = addPodLevelMetrics(gaugeMetrictags);
		statsdClient.recordGaugeValue(metricName, value, gaugeMetrictags);
	}

    
    /**
     * Mask Path Variables: Replaces most of the path variables like mid, pure number, uuid, ifsc code, permissions or roles in url with "xxxx"
     * 
     * @param url
     * @return
     */
    public static String maskPathVariables(String url) {
    	
    	/**NUMBER_MATCHER checks for pattern and replaces it /xxxx(group1) and starts search again from group1 only.
    	 * This will make sure it covers overlapping patterns like 21212/32323 also **/
    	Matcher m2 = MetricConstants.Matchers.NUMBER_MATCHER.matcher(url);
    	if(m2.find()) {
    		do {
    			url = url.replace(m2.group(), MetricConstants.Matchers.REPLACE_STRING+m2.group(1));
    		}while(m2.find(m2.start(1)));
    	}
    	
    	Matcher m4 = MetricConstants.Matchers.TEMP_USER_ID.matcher(url);
    	url = m4.replaceAll(MetricConstants.Matchers.REPLACE_STRING);
    	
    	Matcher m3 = MetricConstants.Matchers.UUID_MATCHER.matcher(url);
    	url = m3.replaceAll(MetricConstants.Matchers.REPLACE_STRING);
    	
    	Matcher m5 = MetricConstants.Matchers.IFSC_MATCHER.matcher(url);
    	url = m5.replaceAll(MetricConstants.Matchers.REPLACE_STRING);
    	
    	Matcher m1 = MetricConstants.Matchers.MID_MATCHER.matcher(url);
    	url = m1.replaceAll(MetricConstants.Matchers.REPLACE_STRING);
    	
//    	Matcher m4 = MetricConstants.Matchers.UNDERSCORE_MATCHER.matcher(url);
//    	url = m4.replaceAll(MetricConstants.Matchers.REPLACE_STRING);
    	
    	LOGGER.debug("after masking {}", url);
    	return url;
    }

    /**
     * Push External API Hits Counter Metric
     * 
     * @param domain
     * @param url
     * @param httpMethod
     */
    public void incrementExtApiHitsCounter(String domain, String url, HttpMethod httpMethod, String upstreamAPI) {
        try {
        	url = maskPathVariables(url);
        	upstreamAPI = maskPathVariables(upstreamAPI);
			String[] tags = pushUpstreamApiMetrics?createExtApiHitsCounterTags(domain, url, httpMethod, upstreamAPI)
													:createExtApiHitsCounterTagsWithoutUpstream(domain, url, httpMethod);
			LOGGER.debug(String.format("Metric [%s] Tags [%s]", MetricConstants.MetricsName.API_HITS_COUNT, Arrays.toString(tags)));
			pushCounterMetrics(MetricConstants.MetricsName.API_HITS_COUNT, tags);
		} catch (Exception e) {
			LOGGER.error("exception occured while pushing metrics ", e);
		}
    }

    private String[] createExtApiHitsCounterTags(String domain, String url, HttpMethod httpMethod, String upstreamAPI) {
        return new String[]{
                TagName.DOMAIN + JOIN + domain,
                TagName.API_URL + JOIN + url,
                TagName.HTTP_METHOD + JOIN + httpMethod,
                TagName.UPSTREAM_API_URL + JOIN + upstreamAPI
                };
    }
    
    private String[] createExtApiHitsCounterTagsWithoutUpstream(String domain, String url, HttpMethod httpMethod) {
        return new String[]{
                TagName.DOMAIN + JOIN + domain,
                TagName.API_URL + JOIN + url,
                TagName.HTTP_METHOD + JOIN + httpMethod
                };
    }
    
    /**
     * Push Internal API Hits Counter Metric
     * 
     * @param url
     * @param httpMethod
     */
    public void incrementIntApiHitsCounter(String url, HttpMethod httpMethod, String clientId) {
        try {
        	url = maskPathVariables(url);
			String[] tags = createIntApiHitsCounterTags(url, httpMethod, clientId);
			LOGGER.debug(String.format("Metric [%s] Tags [%s]", MetricConstants.MetricsName.API_HITS_COUNT, Arrays.toString(tags)));
			pushCounterMetrics(MetricConstants.MetricsName.API_HITS_COUNT, tags);
		} catch (Exception e) {
			LOGGER.error("exception occured while pushing metrics ", e);
		}
    }

    private String[] createIntApiHitsCounterTags(String url, HttpMethod httpMethod, String clientId) {
        return new String[]{
        		TagName.DOMAIN + JOIN + APP_DOMAIN,
                TagName.API_URL + JOIN+ url,
                TagName.HTTP_METHOD + JOIN + httpMethod,
				TagName.CLIENT_ID + JOIN + clientId
                };
    }
    
    /**
     * Push External API Status Counter Metric
     * 
     * @param domain
     * @param url
     * @param httpMethod
     * @param httpStatusCode
     */
    public void incrementExtApiStatusCounter(String domain, String url, HttpMethod httpMethod, HttpStatus httpStatus, String upstreamAPI) {
        try {
        	url = maskPathVariables(url);
        	upstreamAPI = maskPathVariables(upstreamAPI);
			String[] tags = createExtApiStatusCounterTags(domain, url, httpMethod, httpStatus, upstreamAPI);
			LOGGER.debug(String.format("Metric [%s] Tags [%s]", MetricConstants.MetricsName.API_STATUS_COUNT, Arrays.toString(tags)));
			pushCounterMetrics(MetricConstants.MetricsName.API_STATUS_COUNT, tags);
		} catch (Exception e) {
			LOGGER.error("exception occured while pushing metrics ", e);
		}
    }

    
    private String[] createExtApiStatusCounterTags(String domain, String url, HttpMethod httpMethod, HttpStatus httpStatus, String upstreamAPI) {
        return new String[]{
                TagName.DOMAIN + JOIN + domain,
                TagName.API_URL + JOIN + url,
                TagName.HTTP_METHOD + JOIN + httpMethod,
                TagName.HTTP_STATUS_CODE + JOIN + httpStatus.value()
//                TagName.UPSTREAM_API_URL + JOIN + upstreamAPI
                };
    }
    
    /**
     * Record Internal API Status Counter Metric
     * 
     * @param domain
     * @param url
     * @param httpMethod
     * @param httpStatusCode
     */
    public void incrementIntApiStatusCounter(String url, HttpMethod httpMethod, HttpStatus httpStatus, String clientId) {
        try {
        	url = maskPathVariables(url);
			String[] tags = createIntApiStatusCounterTags(url, httpMethod, httpStatus, clientId);
			LOGGER.debug(String.format("Metric [%s] Tags [%s]", MetricConstants.MetricsName.API_STATUS_COUNT, Arrays.toString(tags)));
			pushCounterMetrics(MetricConstants.MetricsName.API_STATUS_COUNT, tags);
		} catch (Exception e) {
			LOGGER.error("exception occured while pushing metrics ", e);
		}
    }

    private String[] createIntApiStatusCounterTags(String url, HttpMethod httpMethod, HttpStatus httpStatus, String clientId) {
        return new String[]{
                TagName.DOMAIN + JOIN + APP_DOMAIN,
                TagName.API_URL + JOIN + url,
                TagName.HTTP_METHOD + JOIN + httpMethod,
                TagName.HTTP_STATUS_CODE + JOIN + httpStatus.value(),
				TagName.CLIENT_ID + JOIN + clientId
                };
    }
    
    /**
     * Record External API Latency Histogram Metric
     * 
     * @param domain
     * @param url
     * @param httpMethod
     */
    public void recordExtApiLatencyHistogram(String domain, String url, HttpMethod httpMethod, LocalDateTime startTime, LocalDateTime endTime, HttpStatus httpStatus, String upstreamAPI) {
    	try {
    		url = maskPathVariables(url);
    		upstreamAPI = maskPathVariables(upstreamAPI);
			long timeTaken = ChronoUnit.MILLIS.between(startTime, endTime);
			String[] tags = createExtApiLatencyHistogramTags(domain, url, httpMethod, httpStatus, upstreamAPI);
			LOGGER.debug(String.format("Metric [%s] Tags [%s]", MetricConstants.MetricsName.API_LATENCY_HISTOGRAM, Arrays.toString(tags)));
			pushHistogramValues(MetricConstants.MetricsName.API_LATENCY_HISTOGRAM, timeTaken, tags);
		} catch (Exception e) {
			LOGGER.error("exception occured while pushing metrics ", e);
		}
        
    }

    private String[] createExtApiLatencyHistogramTags(String domain, String url, HttpMethod httpMethod, HttpStatus httpStatus, String upstreamAPI) {
        return new String[]{
                TagName.DOMAIN + JOIN + domain,
                TagName.API_URL + JOIN + url,
                TagName.HTTP_METHOD + JOIN + httpMethod,
                TagName.HTTP_STATUS_CODE + JOIN + httpStatus.value()
//                TagName.UPSTREAM_API_URL + JOIN + upstreamAPI
                };
    }

    /**
     * Record Internal API Latency Histogram Metric
     * 
     * @param domain
     * @param url
     * @param httpMethod
     */
    public void recordIntApiLatencyHistogram(String url, HttpMethod httpMethod, LocalDateTime startTime, LocalDateTime endTime, HttpStatus httpStatus) {
    	try {
    		url = maskPathVariables(url);
			long timeTaken = ChronoUnit.MILLIS.between(startTime, endTime);
			String[] tags = createIntApiLatencyHistogramTags(url, httpMethod, httpStatus);
			LOGGER.debug(String.format("Metric [%s] Tags [%s]", MetricConstants.MetricsName.API_LATENCY_HISTOGRAM, Arrays.toString(tags)));
			pushHistogramValues(MetricConstants.MetricsName.API_LATENCY_HISTOGRAM, timeTaken, tags);
		} catch (Exception e) {
			LOGGER.error("exception occured while pushing metrics ", e);
		}
        
    }

    private String[] createIntApiLatencyHistogramTags(String url, HttpMethod httpMethod, HttpStatus httpStatus) {
        return new String[]{
                TagName.DOMAIN + JOIN + APP_DOMAIN,
                TagName.API_URL + JOIN + url,
                TagName.HTTP_METHOD + JOIN + httpMethod,
                TagName.HTTP_STATUS_CODE + JOIN + httpStatus.value()
                };
    }

    public void recordHTTPConnectionPoolMetrics(String domain, PoolStats poolStats){
		try {
			String[] tags = createRecordConnectionPoolMetricsTags(domain);
			pushGaugeValue(MetricConstants.MetricsName.HTTP_AVAILABLE_CONNECTION_POOL_METRIC, poolStats.getAvailable(), tags);
			pushGaugeValue(MetricConstants.MetricsName.HTTP_LEASED_CONNECTION_POOL_METRIC, poolStats.getLeased(),tags);
			pushGaugeValue(MetricConstants.MetricsName.HTTP_MAX_CONNECTION_POOL_METRIC, poolStats.getMax(),tags);
			pushGaugeValue(MetricConstants.MetricsName.HTTP_PENDING_CONNECTION_POOL_METRIC, poolStats.getPending(),tags);
		} catch (Exception e) {
			LOGGER.error("exception occured while pushing metrics ", e);
		}
	}
    
	private String[] createRecordConnectionPoolMetricsTags(String domain) {
		 return new String[]{
	                TagName.DOMAIN + JOIN + domain,
	                };
		
	}

	public String[] createClevertapTags(String errorCode) {
		return new String[]{
				TagName.ERROR_CODE + JOIN + errorCode
		};
	}

	public String[] createSmsBugTags(String callerAPI, String httpMethod, String errorCode) {
		return new String[]{
				TagName.UPSTREAM_API_URL + MetricUtils.JOIN + callerAPI,
				TagName.HTTP_METHOD + JOIN + httpMethod,
				TagName.ERROR_CODE + JOIN + errorCode
		};
	}
    
}
