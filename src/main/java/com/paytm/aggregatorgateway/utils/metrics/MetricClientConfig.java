package com.paytm.aggregatorgateway.utils.metrics;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 
 * <AUTHOR>
 *
 */
@Configuration("MetricClientConfig")
@ConfigurationProperties("metric-client-config")
public class MetricClientConfig implements InitializingBean {
	
	private static final Logger log = LogManager.getLogger(MetricClientConfig.class);
	
	String hostName;
    String prefix;
    int port;

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info(" properties set " + this.toString());
    }

	public String getPrefix() {
		return prefix;
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public String getHostName() {
		return hostName;
	}

	public void setHostName(String hostName) {
		this.hostName = hostName;
	}

	@Override
	public String toString() {
		return "MetricClientConfig [hostName=" + hostName + ", prefix=" + prefix + ", port=" + port + "]";
	}
    
    
}
