package com.paytm.aggregatorgateway.utils.metrics;

import java.util.regex.Pattern;

//TODO: @Chandu: remove unwanted metrics and tags

/**
 * 
 * <AUTHOR>
 *
 */
public class MetricConstants {

    public static final class MetricsName {
        public static final String API_HITS_COUNT = "api_hits_count";
		public static final String API_STATUS_COUNT = "api_status_count";
		public static final String API_LATENCY_HISTOGRAM = "api_latency_hist";
		public static final String SERVICE_NODE = "ump.service.node";
		public static final String SERVICE_ID = "ump.service.id";
		public static final String SMS_BUG = "sms_bug";
		public static final String CLEVERTAP_ERROR = "clevertap_error";
        public static final String HTTP_AVAILABLE_CONNECTION_POOL_METRIC = "http_available_connections";
        public static final String HTTP_LEASED_CONNECTION_POOL_METRIC = "http_leased_connections";
        public static final String HTTP_MAX_CONNECTION_POOL_METRIC = "http_max_connections";
        public static final String HTTP_PENDING_CONNECTION_POOL_METRIC = "http_pending_connections";
    }

    public static final class TagName {
        
    	public static final String DOMAIN = "domain";
    	public static final String API_URL = "api-url";
    	public static final String UPSTREAM_API_URL = "upstream_api_url";
        public static final String HTTP_METHOD = "http-method";
        public static final String HTTP_STATUS_CODE = "http-status-code";
        public static final String TIME_TAKEN = "time-taken";
		public static final String APPLICATION_NAME_TAG = "applicationName";
		public static final String HOST_NAME = "hostName";
		public static final String ERROR_CODE = "errorCode";
        public static final String PROFILE_ACTIVE = "profileActive";
        public static final String CLIENT_ID = "clientId";
    }
    
    public static final class Matchers {
    	public static final Pattern MID_MATCHER = Pattern.compile("/[a-zA-Z0-9]{6}[0-9]{14}");
        public static final Pattern NUMBER_MATCHER = Pattern.compile("/[0-9]+[^/]*(/|$)");
        public static final Pattern UUID_MATCHER = Pattern.compile("/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}");
        public static final Pattern IFSC_MATCHER = Pattern.compile("/[A-Za-z]{4}0[A-Za-z0-9]{6}");
        public static final Pattern TEMP_USER_ID = Pattern.compile("/T_[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}");
//        public static final Pattern UNDERSCORE_MATCHER = Pattern.compile("/[^/]*_[^/]*");
        public static final String REPLACE_STRING = "/xxxx";
    }
    
}
