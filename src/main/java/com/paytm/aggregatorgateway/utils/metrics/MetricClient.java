package com.paytm.aggregatorgateway.utils.metrics;


import com.timgroup.statsd.NonBlockingStatsDClient;
import com.timgroup.statsd.StatsDClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@DependsOn("MetricClientConfig")
public class MetricClient {

    @Autowired
    MetricClientConfig metricClientConfig;

//    @Bean
//    public StatsDClient primaryStatsDClient() {
//        return new NonBlockingStatsDClient(metricClientConfig.getPrefix(),     /* prefix to any stats; may be null or empty string */
//        		metricClientConfig.getHostName(),                      		   /* common case: localhost */
//        		metricClientConfig.getPort(),                                   /* port: by default it is 8125*/
//                MetricConstants.MetricsName.SERVICE_ID + ":" + System.getProperty(MetricConstants.TagName.APPLICATION_NAME_TAG),  /* constant tags */
//                MetricConstants.MetricsName.SERVICE_NODE + ":" + System.getProperty(MetricConstants.TagName.HOST_NAME)); /* constant tags */
//    }
    
    @Bean
    public StatsDClient primaryStatsDClient() {
        return new NonBlockingStatsDClient(metricClientConfig.getPrefix(),     /* prefix to any stats; may be null or empty string */
        		metricClientConfig.getHostName(),                      		   /* common case: localhost */
        		metricClientConfig.getPort());                                 /* port: by default it is 8125*/
    }
}
