package com.paytm.aggregatorgateway.utils;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

public class HashUtils {
	public static String generateHash(String data) {

		String hashedData = null;
		if (StringUtils.isNotBlank(data)) {
			MessageDigest digest;
			try {
				digest = MessageDigest.getInstance("SHA-256");
				byte[] encodedhash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
				hashedData = bytesToHex(encodedhash);
			} catch (Throwable th) {

			}
		}
		return hashedData;
	}

	public static String bytesToHex(byte[] hash) {
		StringBuffer hexString = new StringBuffer();
		for (int i = 0; i < hash.length; i++) {
			String hex = Integer.toHexString(0xff & hash[i]);
			if (hex.length() == 1)
				hexString.append('0');
			hexString.append(hex);
		}
		return hexString.toString();
	}

}
