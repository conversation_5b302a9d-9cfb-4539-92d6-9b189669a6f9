package com.paytm.aggregatorgateway.utils;


import com.paytm.aggregatorgateway.constants.DomainConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Map;


@Component
public class RestProcessorDelegate implements DomainConstants {
	
	@Autowired
	private RestProcessor restProcessor;
	
	
	private static final Logger LOGGER = LogManager.getLogger(RestProcessorDelegate.class);

	/**
	 * Rest Calls for UMP
	 */
	public <T> ResponseEntity<T> executeUMPRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {

		String traceId = StringUtils.isNotBlank(MDC.get("traceId")) ? MDC.get("traceId").toString() : "";
		headers.add("x-client-traceId", traceId);
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.UMP);
	}

	/**
	 * Rest Calls for Promo CLM
	 */
	public <T> ResponseEntity<T> executePromoRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.PROMO_CLM);
	}

	public  <T>  ResponseEntity<T>  executeBOSSRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type)throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type,DomainConstants.BOSS);
	}

	public  <T>  ResponseEntity<T>  executeBOSSAppRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type)throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.BOSS);
	}

	public <T> ResponseEntity<T> executeClevertapRequestHystrix(String url, String method, Map<String, String> queryMap, HttpHeaders headers,
																Object payload, Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload, type, DomainConstants.CLEVERTAP);
	}

	public <T> ResponseEntity<T> executeNotificationRequestHystrix(String url, String method, Map<String,String>queryMap, HttpHeaders headers,
																   Object payload, Class<T> type) throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload, type, DomainConstants.NOTIFICATION);
	}
	public  <T>  ResponseEntity<T>  executeSubscriptionRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type)throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type,DomainConstants.SUBSCRIPTION);
	}

	public  <T>  ResponseEntity<T>  executeUPSRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type)throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type,DomainConstants.UPS);
	}

	/**
	 * Rest Calls for Rewards
	 */
	public <T> ResponseEntity<T> executeRewardsRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.REWARDS);
	}

	/**
	 * Rest Calls for KYB
	 */
	public <T> ResponseEntity<T> executeKybRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.KYB);
	}

	public <T> ResponseEntity<T> executeKybAddressRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.KYB_ADDRESS);
	}
	public  <T>  ResponseEntity<T>  executeDMSRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type)throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type,DomainConstants.DMS);
	}

	public  <T>  ResponseEntity<T>  executeVoucherRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type)throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type,DomainConstants.VOUCHER);
	}

	public <T> ResponseEntity<T> executeEosAddressRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.EOS);
	}
	public <T> ResponseEntity<T> executeCentralToolKitRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.CENTRAL_TOOL_KIT);
	}
	public <T> ResponseEntity<T> executeCstRequestHystrix(String url, String method, Map<String,String> queryMap, HttpHeaders headers, Object payload, Class<T> type) throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload, type, DomainConstants.CST);
	}

	public <T> ResponseEntity<T> executeCstSeviceCallRequestHystrix(String url, String method, Map<String,String> queryMap, HttpHeaders headers, Object payload, Class<T> type) throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload, type, DomainConstants.CST_SERVICE_CALL);
	}

	public <T> ResponseEntity<T> executeMaquetteRequestHystrix(String url, String method, Map<String,String> queryMap, HttpHeaders headers, Object payload, Class<T> type) throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload, type, DomainConstants.MAQUETTE);
	}

	public <T> ResponseEntity<T> executeFsmRequestHystrix(String url, String method, Map<String,String> queryMap, HttpHeaders headers, Object payload, Class<T> type) throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload, type, FSM);
	}

	public <T> ResponseEntity<T> executeSurveyRequestHystrix(String url, String method, Map<String,String> queryMap, HttpHeaders headers, Object payload, Class<T> type) throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload, type, DomainConstants.SURVEY);
	}

	public  <T>  ResponseEntity<T>  executeOthersRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type)throws InterruptedException{
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.OTHERS);
	}

	/**
	 * Rest Calls for Digital Proxy
	 */
	public <T> ResponseEntity<T> executeDigitalProxyRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.DIGITALPROXY);
	}
	public <T> ResponseEntity<T> executeCstMGWRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.CST_MGW);
	}

	public <T> ResponseEntity<T> executeStoreFrontRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.STORE_FRONT);
	}

	/**
	 * Rest Calls for OE
	 */
	public <T> ResponseEntity<T> executeOERequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.OE);
	}
	public <T> ResponseEntity<T> executeOMSRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.OMS);
	}
	public <T> ResponseEntity<T> executeCheckoutRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.CHECKOUT);
	}

	/**
	 * Rest Calls for OCR
	 */
	public <T> ResponseEntity<T> executeOCRRequestHystrix(String url,String method,Map<String,String> queryMap,HttpHeaders headers, Object payload,Class<T> type) throws InterruptedException {
		return executeRequestDomainWise(url, method, queryMap, headers, payload,type, DomainConstants.OCR);
	}

	private <T, S> ResponseEntity<T> executeRequestDomainWise(String url,String method,Map<String,String> queryMap,HttpHeaders headers, S payload,Class<T> type, String domain) throws InterruptedException{
		ResponseEntity<T> response =null;
		try {
		switch (method) {
			case "GET":
				response = restProcessor.exchangeRequest(url, queryMap, headers,payload, HttpMethod.GET, type, domain);
				break;
			case "POST":
				response = restProcessor.exchangeRequest(url, queryMap, headers,payload, HttpMethod.POST, type, domain);
				break;
			case "DELETE":
				response = restProcessor.exchangeRequest(url, queryMap, headers,payload, HttpMethod.DELETE, type, domain);
				break;
			case "PUT":
				response = restProcessor.exchangeRequest(url, queryMap, headers,payload, HttpMethod.PUT, type, domain);
				break;
			default:
				LOGGER.info("No valid method specified: {}", method);
			
			}
		}catch(HttpClientErrorException e) {
			LOGGER.error("4xx Exception occurred {}",e);
			return handleExceptionForHystrix(e);
		}
		if (Thread.currentThread().isInterrupted()) {
			LOGGER.error("executeRequest api interrupted:{}", Thread.currentThread().getName());
			throw new InterruptedException();
		}
		return response;
	}
	
	@SuppressWarnings("unchecked")
	private <T> ResponseEntity<T> handleExceptionForHystrix(Exception e) {
	      if (e instanceof HttpClientErrorException) {
	      	HttpClientErrorException httpException = ((HttpClientErrorException)e);
	          if(httpException.getStatusCode().is4xxClientError()) {
	         	 ResponseEntity<String> errorResp = new ResponseEntity<String>(((HttpClientErrorException) e).getResponseBodyAsString(),httpException.getStatusCode());
	         	 return (ResponseEntity<T>) errorResp;
	          }
	          throw new RuntimeException(e);
	      }
	      throw new RuntimeException(e);
	  }

	
}
