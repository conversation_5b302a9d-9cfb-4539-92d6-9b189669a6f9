/**
 * 
 */
package com.paytm.aggregatorgateway.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator.Builder;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.enums.JwtAlgorithm;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class JwtUtil {

	private JwtUtil() {}

	private static final Logger LOGGER = LoggerFactory.getLogger(JwtUtil.class);
	private static ObjectMapper objectMapper;
	static {
		JwtUtil.objectMapper = new ObjectMapper();
		JwtUtil.objectMapper.setSerializationInclusion(Include.NON_NULL);
	}
	
	public static String createJwtTokenHS256(Map<String, String> jwtClaims, String iss, String key) {
		return createJwtToken(jwtClaims, key.getBytes(), iss, null, null, JwtAlgorithm.HS256);
	}

	public static String createJwtToken(Map<String, ?> claims, byte[] key, String issuer, Date issuedAt, Date expiresAt, JwtAlgorithm jwtAlgorithm) {
		Algorithm algorithm = null;
		switch (jwtAlgorithm) {
			case HS256:
				algorithm = Algorithm.HMAC256(key);
				break;
			case HS512:
				algorithm = Algorithm.HMAC512(key);
				break;
		}

		Builder jwtBuilder = JWT.create();
		
		if(issuer != null){
			jwtBuilder.withIssuer(issuer);
		}
		if(issuedAt != null){
			jwtBuilder.withIssuedAt(issuedAt);
		}
		if(expiresAt != null){
			jwtBuilder.withExpiresAt(expiresAt);
		}
		setClaimsFromMap(jwtBuilder, claims);
		
		return jwtBuilder.sign(algorithm);
	}
	
	private static void setClaimsFromMap(Builder builder, Map<String, ?> claims) {
		if (MapUtils.isNotEmpty(claims)) {
			claims.forEach((k, v) -> {
				if (v instanceof Long) {
					builder.withClaim(k, (Long) v);
				} else if (v instanceof Boolean) {
					builder.withClaim(k, (Boolean) v);
				} else if (v instanceof Date) {
					builder.withClaim(k, (Date) v);
				} else if (v instanceof Double) {
					builder.withClaim(k, (Double) v);
				} else if (v instanceof Integer) {
					builder.withClaim(k, (Integer) v);
				} else {
					builder.withClaim(k, Objects.toString(v, null));
				}
			});

		}

	}


}
