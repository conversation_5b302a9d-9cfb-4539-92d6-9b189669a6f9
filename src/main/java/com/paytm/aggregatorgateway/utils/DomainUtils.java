package com.paytm.aggregatorgateway.utils;

import com.paytm.aggregatorgateway.constants.DomainConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.net.URI;
import java.util.HashMap;

@Component
public class DomainUtils implements DomainConstants {
	
	@Autowired
	private Environment environment;

	public final static HashMap<String, String> DOMAIN_MAP = new HashMap<>();

	@PostConstruct
	private void initialise() {

		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.UMP_BASE_URL)).getHost(), DomainConstants.UMP);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.BOSS_BASE_URL)).getHost(), DomainConstants.BOSS);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.CLEVERTAP_BASE_URL)).getHost(), DomainConstants.CLEVERTAP);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.SUBSCRIPTION_BASE_URL)).getHost(), DomainConstants.SUBSCRIPTION);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.UPS_BASE_URL)).getHost(), DomainConstants.UPS);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.PROMO_BASE_URL)).getHost(), DomainConstants.PROMO_CLM);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.KYB_BASE_URL)).getHost(), KYB);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.REWARDS_URL)).getHost(), REWARDS);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.CST_URL)).getHost(),CST);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.CST_SERVICE_CALL_URL)).getHost(),CST_SERVICE_CALL);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.CST_MGW_URL)).getHost(),CST_MGW);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.NOTIFICATIONS_BASE_URL)).getHost(), NOTIFICATION);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.MAQUETTE_BASE_URL)).getHost(), MAQUETTE);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.FSM_BASE_URL)).getHost(),FSM);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.KYB_ADDRESS_BASE_URL)).getHost(),KYB_ADDRESS);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.EOS_BASE_URL)).getHost(),EOS);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.CENTRAL_TOOL_KIT_BASE_URL)).getHost(),CENTRAL_TOOL_KIT);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.STORE_FRONT_BASE_URL)).getHost(),STORE_FRONT);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.SURVEY_URL)).getHost(),SURVEY);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.GOLDENGATE_BASE_URL)).getHost(),OE);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.DMS_URL)).getHost(),DMS);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.CHECKOUT_BASE_URL)).getHost(),CHECKOUT);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.OMS_BASE_URL)).getHost(),OMS);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.VOUCHER_BASE_URL)).getHost(),VOUCHER);
		DOMAIN_MAP.put(URI.create(environment.getRequiredProperty(DomainConstants.OCR_BASE_URL)).getHost(),OCR);
	}
	
	
}
