package com.paytm.aggregatorgateway.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.aggregatorgateway.config.core.HttpClientConfig;
import com.paytm.aggregatorgateway.constants.WarmUpConstants;
import com.paytm.pgdashboard.commons.dto.User;
import com.paytm.aggregatorgateway.utils.metrics.MetricUtils;
import com.paytm.aggregatorgateway.vo.MonitorLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.core5.pool.PoolStats;
import org.apache.http.client.utils.URIBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;

@Component
public class RestProcessor implements InitializingBean {

	private static final Logger LOGGER = LogManager.getLogger(RestProcessor.class);
	
	@Qualifier("restTemplateUMP")
	@Autowired
	private RestTemplate restTemplateUMP;

	@Qualifier("restTemplatePromoCLM")
	@Autowired
	private RestTemplate restTemplatePromoCLM;

	@Qualifier("restTemplateCleverTap")
	@Autowired
	private RestTemplate restTemplateCleverTap;

	@Qualifier("restTemplateSubscription")
	@Autowired
	private RestTemplate restTemplateSubscription;

	@Qualifier("restTemplateBOSS")
	@Autowired
	private RestTemplate restTemplateBOSS;

	@Qualifier("restTemplateUPS")
	@Autowired
	private RestTemplate restTemplateUPS;

	@Qualifier("restTemplateRewards")
	@Autowired
	private RestTemplate restTemplateRewards;

	@Qualifier("restTemplateKYB")
	@Autowired
	private RestTemplate restTemplateKYB;

	@Qualifier("restTemplateCST")
	@Autowired
	private RestTemplate restTemplateCST;

	@Qualifier("restTemplateCSTServiceCall")
	@Autowired
	private RestTemplate restTemplateCSTServiceCall;

	@Qualifier("restTemplateMaquette")
	@Autowired
	private RestTemplate restTemplateMaquette;

	@Qualifier("restTemplateFsm")
	@Autowired
	private RestTemplate restTemplateFsm;

	@Qualifier("restTemplateCstMGW")
	@Autowired
	private RestTemplate restTemplateCstMGW;

	@Qualifier("restTemplateOthers")
	@Autowired
	private RestTemplate restTemplateOthers;

	@Qualifier("restTemplateDigitalProxy")
	@Autowired
	private RestTemplate restTemplateDigitalProxy;

	@Qualifier("restTemplateNotification")
	@Autowired
	private RestTemplate restTemplateNotification;

	@Qualifier("restTemplateKYBAddress")
	@Autowired
	private RestTemplate restTemplateKYBAddress;

	@Qualifier("restTemplateEos")
	@Autowired
	private RestTemplate restTemplateEos;

	@Qualifier("restTemplateCentralToolKit")
	@Autowired
	private RestTemplate restTemplateCentralToolKit;

	@Qualifier("restTemplateStoreFront")
	@Autowired
	private RestTemplate restTemplateStoreFront;

	@Qualifier("restTemplateSurvey")
	@Autowired
	private RestTemplate restTemplateSurvey;

	@Qualifier("restTemplateOE")
	@Autowired
	private RestTemplate restTemplateOE;

	@Qualifier("restTemplateDMS")
	@Autowired
	private RestTemplate restTemplateDMS;

	@Qualifier("restTemplateOMS")
	@Autowired
	private RestTemplate restTemplateOMS;

	@Qualifier("restTemplateCheckout")
	@Autowired
	private RestTemplate restTemplateCheckout;

	@Qualifier("restTemplateVoucher")
	@Autowired
	private RestTemplate restTemplateVoucher;

	@Autowired
	private MonitorLogger monitorLogger;
	
	@Autowired
	MetricUtils metricUtils;

	private HashMap<String,RestTemplate> restTemplateMap = new HashMap<>();

	@Override
	public void afterPropertiesSet() throws Exception {
		restTemplateMap.put("UMP", restTemplateUMP);
		restTemplateMap.put("PROMO_CLM", restTemplatePromoCLM);
		restTemplateMap.put("BOSS",restTemplateBOSS);
		restTemplateMap.put("CLEVERTAP",restTemplateCleverTap);
		restTemplateMap.put("SUBSCRIPTION",restTemplateSubscription);
		restTemplateMap.put("UPS",restTemplateUPS);
		restTemplateMap.put("REWARDS", restTemplateRewards);
		restTemplateMap.put("KYB", restTemplateKYB);
		restTemplateMap.put("OTHERS", restTemplateOthers);
		restTemplateMap.put("CST", restTemplateCST);
		restTemplateMap.put("CST_SERVICE_CALL", restTemplateCSTServiceCall);
		restTemplateMap.put("DIGITALPROXY", restTemplateDigitalProxy);
		restTemplateMap.put("NOTIFICATION", restTemplateNotification);
		restTemplateMap.put("MAQUETTE", restTemplateMaquette);
		restTemplateMap.put("FSM", restTemplateFsm);
		restTemplateMap.put("CST_MGW", restTemplateCstMGW);
		restTemplateMap.put("KYB_ADDRESS", restTemplateKYBAddress);
		restTemplateMap.put("EOS", restTemplateEos);
		restTemplateMap.put("CENTRAL_TOOL_KIT", restTemplateCentralToolKit);
		restTemplateMap.put("STORE_FRONT",restTemplateStoreFront);
		restTemplateMap.put("SURVEY",restTemplateSurvey);
		restTemplateMap.put("OE",restTemplateOE);
		restTemplateMap.put("DMS",restTemplateDMS);
		restTemplateMap.put("CHECKOUT",restTemplateCheckout);
		restTemplateMap.put("OMS",restTemplateOMS);
		restTemplateMap.put("VOUCHER",restTemplateVoucher);
	}
	
	private static HttpStatus getHttpStatus(int status) {
		if(status == 0)
			return HttpStatus.valueOf(504);
		HttpStatus httpStatus = null;
		try {
			httpStatus = HttpStatus.valueOf(status);
		}catch(Exception e) {
			LOGGER.error("invalid status code");
		}
		return httpStatus;
	}
	
	private static URI prepareURL(String url, Map<String, String> queryParameters) {
		URIBuilder builder;
		try {
			builder = new URIBuilder(url);
			if (queryParameters != null) {
				for (Entry<String, String> entry : queryParameters.entrySet()) {
					builder.addParameter(entry.getKey(), entry.getValue());
				}
			}
			return builder.build();
		} catch (URISyntaxException e) {
			try {
				LOGGER.error("Could not build URI, url: {}, queryParams: {}", url, MappingUtils.convertObjectToJson(queryParameters));
			} catch (JsonProcessingException e1) {
				LOGGER.error("Unable to print queryParams as json", e1);
			}
			throw new RuntimeException(e);
		}
	}
	
	public <T, S> ResponseEntity<T> exchangeRequest(String url, Map<String, String> queryParams, HttpHeaders headers, S body,
			HttpMethod httpMethod, Class<T> type, String... domains) {

		URI uri = queryParams == null ? URI.create(url) : prepareURL(url, queryParams);
		LOGGER.info("Entering exchange with method: {}, uri: {}", httpMethod, uri);
		MonitorLog monitorLog = new MonitorLog();
		String domain = DomainUtils.DOMAIN_MAP.getOrDefault(uri.getHost(), uri.getHost());
		String upstreamDomain = StringUtils.isNotBlank(MDC.get("upstreamDomain")) ? MDC.get("upstreamDomain").toString() : "";
		
		ResponseEntity<T> response = null;
		try {
			RequestEntity<S> request = new RequestEntity<S>(body, headers, httpMethod, uri);

			monitorLog.setMethod(httpMethod);
			monitorLog.setCaller(Thread.currentThread().getName());
			monitorLog.setRequest(GsonMaskingUtils.maskRequestBody(body));
			monitorLog.setUri(uri);
			monitorLog.setStartTime(LocalDateTime.now());
			
			
			try {
				if(upstreamDomain.equalsIgnoreCase("localhost")) {
					response = (ResponseEntity<T>)(new ResponseEntity<String>(WarmUpConstants.WARMUP_RESPONSE_BODY, null, HttpStatus.OK));
				} else
					response = restTemplateMap.get(domains[0]).exchange(request, type);
			}catch (HttpStatusCodeException e) {
				response = (ResponseEntity<T>)(new ResponseEntity<String>(e.getResponseBodyAsString(), e.getResponseHeaders(), e.getStatusCode()));
			}

			monitorLog.setEndTime(LocalDateTime.now());
			monitorLog.setHttpStatus(response.getStatusCodeValue());
			try {
				long time1 = System.currentTimeMillis();
				monitorLog.setResponse(GsonMaskingUtils.maskResponseBody(response.getBody()));
				long time2 = System.currentTimeMillis();
				long gap = time2-time1;
				LOGGER.info("Logger time : "+gap);
			} catch (Exception e) {
				LOGGER.error("error : " + e.getMessage());
				LOGGER.error("error getCause : " + e.getCause());
				monitorLog.setResponse(response.getBody());
			}

			User user = SecurityUtils.getLoggedInUser();
			if (user != null) {
				String uid = user.getId();
				Long eid = user.getCurrentMerchant();
				monitorLog.setUid(uid);
				monitorLog.setEid(eid);
			}
			return response;

		} catch (RestClientResponseException e) {
			monitorLog.setEndTime(LocalDateTime.now());
			monitorLog.setHttpStatus(e.getRawStatusCode());
			monitorLog.setResponse(e.getResponseBodyAsString());
			throw e;
		} catch (Exception e) {
			monitorLog.setEndTime(LocalDateTime.now());
			monitorLog.appendException(e.getMessage());
			throw e;
		} finally {
			String traceId = StringUtils.isNotBlank(MDC.get("traceId")) ? MDC.get("traceId").toString() : "";
			String upstreamAPI = StringUtils.isNotBlank(MDC.get("upstreamAPI")) ? MDC.get("upstreamAPI").toString() : "";
			monitorLog.setTraceId(traceId);
			if(monitorLog.getHttpStatus()==0){
				monitorLog.setHttpStatus(504);
			}
			monitorLogger.log(monitorLog);
			
			MonitorLogger.ResponseResult responseResult = monitorLogger.getResponseResult(monitorLog);
			LOGGER.debug("pushing {}, {}, {}", domain, uri.getPath(), httpMethod);
			
			//Push metrics
			metricUtils.incrementExtApiHitsCounter(domain, responseResult.getApiName(), httpMethod, upstreamAPI);
			metricUtils.incrementExtApiStatusCounter(domain, responseResult.getApiName(), httpMethod, getHttpStatus(monitorLog.getHttpStatus()), upstreamAPI);
			metricUtils.recordExtApiLatencyHistogram(domain, responseResult.getApiName(), httpMethod, monitorLog.getStartTime(), monitorLog.getEndTime(), getHttpStatus(monitorLog.getHttpStatus()), upstreamAPI);			

			
			if(!Objects.isNull(HttpClientConfig.connectionMangerMap.get(domain))) {
				PoolStats poolStats = HttpClientConfig.connectionMangerMap.get(domain).getTotalStats();
				metricUtils.recordHTTPConnectionPoolMetrics(domain, poolStats);
			}
			
			LOGGER.debug("Exiting");
		}
	}

}
