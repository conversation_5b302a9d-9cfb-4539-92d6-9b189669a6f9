package com.paytm.aggregatorgateway.dto;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

public class SmsSubscriptionDTO {
	
    @NotNull(message = "client cannot be null")
	private String client;    
    
    @NotNull(message = "platformVersion cannot be null")
	private String platformVersion;
	private String siteId;
	private String childSiteId;
	private String appVersion;
	private String locale;
	private String langId;
	
	private String custId;
	private String mid;
    @NotNull(message = "usn cannot be null")
	private String usn;
    
    @NotNull(message = "subscriptionType cannot be null")
	private String subscriptionType;
    
	private String phoneNumber;
	
	private String securityDeposit;
	
    @NotNull(message = "serviceType cannot be null")
	private String serviceType;
    
    @NotNull(message = "serviceName cannot be null")
	private String serviceName;
    
    @NotNull(message = "planPrice cannot be null")
	private String planPrice;
    
    @NotNull(message = "frequency cannot be null")
	private String frequency;
    
    @NotNull(message = "onboardingDate cannot be null")
	private String onboardingDate;
    
	private String deductionStartDate;
	
	private String endDate;
	
	private Map<String,String> userSubscriptionMetadata;
	
	private List<Map<String,String>> otherCharges;
	
	public String getClient() {
		return client;
	}
	public void setClient(String client) {
		this.client = client;
	}
	public String getPlatformVersion() {
		return platformVersion;
	}
	public void setPlatformVersion(String platformVersion) {
		this.platformVersion = platformVersion;
	}
	public String getSiteId() {
		return siteId;
	}
	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}
	public String getChildSiteId() {
		return childSiteId;
	}
	public void setChildSiteId(String childSiteId) {
		this.childSiteId = childSiteId;
	}
	public String getAppVersion() {
		return appVersion;
	}
	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}
	public String getLocale() {
		return locale;
	}
	public void setLocale(String locale) {
		this.locale = locale;
	}
	public String getLangId() {
		return langId;
	}
	public void setLangId(String langId) {
		this.langId = langId;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getMid() {
		return mid;
	}
	public void setMid(String mid) {
		this.mid = mid;
	}
	public String getUsn() {
		return usn;
	}
	public void setUsn(String usn) {
		this.usn = usn;
	}
	public String getSubscriptionType() {
		return subscriptionType;
	}
	public void setSubscriptionType(String subscriptionType) {
		this.subscriptionType = subscriptionType;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public String getSecurityDeposit() {
		return securityDeposit;
	}
	public void setSecurityDeposit(String securityDeposit) {
		this.securityDeposit = securityDeposit;
	}
	public String getServiceType() {
		return serviceType;
	}
	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}
	public String getServiceName() {
		return serviceName;
	}
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}
	public String getPlanPrice() {
		return planPrice;
	}
	public void setPlanPrice(String planPrice) {
		this.planPrice = planPrice;
	}
	public String getFrequency() {
		return frequency;
	}
	public void setFrequency(String frequency) {
		this.frequency = frequency;
	}
	public String getOnboardingDate() {
		return onboardingDate;
	}
	public void setOnboardingDate(String onboardingDate) {
		this.onboardingDate = onboardingDate;
	}
	public String getDeductionStartDate() {
		return deductionStartDate;
	}
	public void setDeductionStartDate(String deductionStartDate) {
		this.deductionStartDate = deductionStartDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Map<String, String> getUserSubscriptionMetadata() {
		return userSubscriptionMetadata;
	}
	public void setUserSubscriptionMetadata(Map<String, String> userSubscriptionMetadata) {
		this.userSubscriptionMetadata = userSubscriptionMetadata;
	}
	
	public List<Map<String, String>> getOtherCharges() {
		return otherCharges;
	}
	public void setOtherCharges(List<Map<String, String>> otherCharges) {
		this.otherCharges = otherCharges;
	}
	@Override
	public String toString() {
		return "SmsSubscriptionDTO [usn=" + usn + ", subscriptionType=" + subscriptionType + ", phoneNumber="
				+ phoneNumber + ", securityDeposit=" + securityDeposit + ", serviceType=" + serviceType
				+ ", serviceName=" + serviceName + ", planPrice=" + planPrice + ", frequency=" + frequency
				+ ", onboardingDate=" + onboardingDate + ", deductionStartDate=" + deductionStartDate + ", endDate="
				+ endDate + ", userSubscriptionMetadata=" + userSubscriptionMetadata + ", otherCharges=" + otherCharges
				+ ", client=" + client + ", platformVersion=" + platformVersion + "]";
	}
	
	

}
