package com.paytm.aggregatorgateway.dto;

public class SurveyDTO {

    private String _id;
    private String eventCategory;
    private String eventAction;
    private String eventLabel;
    private String deeplink;
    private String surveyId;
    private String active;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getEventCategory() {
        return eventCategory;
    }

    public void setEventCategory(String eventCategory) {
        this.eventCategory = eventCategory;
    }

    public String getEventAction() {
        return eventAction;
    }

    public void setEventAction(String eventAction) {
        this.eventAction = eventAction;
    }

    public String getEventLabel() {
        return eventLabel;
    }

    public void setEventLabel(String eventLabel) {
        this.eventLabel = eventLabel;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(String surveyId) {
        this.surveyId = surveyId;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    @Override
    public String toString() {
        return "SurveyDTO{" +
                "_id='" + _id + '\'' +
                ", eventCategory='" + eventCategory + '\'' +
                ", eventAction='" + eventAction + '\'' +
                ", eventLabel='" + eventLabel + '\'' +
                ", deeplink='" + deeplink + '\'' +
                ", surveyId='" + surveyId + '\'' +
                ", active='" + active + '\'' +
                '}';
    }
}
