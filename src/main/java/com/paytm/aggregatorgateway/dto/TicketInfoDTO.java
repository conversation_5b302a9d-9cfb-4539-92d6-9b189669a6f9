package com.paytm.aggregatorgateway.dto;

import lombok.*;

import java.util.List;
import java.util.Map;

public class TicketInfoDTO
{
    private String source;
    private String createdAt;
    private String updatedAt;
    private String cstentity;
    private Object botParams;
    private Long requesterId;
    private String ticketIcon;
    private String itemName;
    private String caseCreationDate;
    private String ticketNumber;
    private String status;
    private String formattedCreatedDate;
    private String umpMid;
    private String viewType;
    private String freshDeskUrl;
    private String transactionId;
    private String l1IssueCategory;
    private String verticalLabel;
    private Map<String,Object> custom_fields;
    private List<String> tags;
    private String customerIssueCategoryL1;
    private int ticketStatus;
    private int origin;
    private String responderId;
    private String l2IssueCategory;
    private String l3IssueCategory;
    private String rawL1IssueCategory;

    private String riskComment;

    private String subStatus;

    public String getRiskComment() {
        return riskComment;
    }

    public void setRiskComment(String riskComment) {
        this.riskComment = riskComment;
    }

    public String getSubStatus() {
        return subStatus;
    }

    public void setSubStatus(String subStatus) {
        this.subStatus = subStatus;
    }

    public int getOrigin()
    {
        return origin;
    }

    public void setOrigin(int origin)
    {
        this.origin = origin;
    }

    public String getSource()
    {
        return source;
    }

    public void setSource(String source)
    {
        this.source = source;
    }

    public String getCreatedAt()
    {
        return createdAt;
    }

    public void setCreatedAt(String createdAt)
    {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt()
    {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt)
    {
        this.updatedAt = updatedAt;
    }

    public String getCstentity()
    {
        return cstentity;
    }

    public void setCstentity(String cstentity)
    {
        this.cstentity = cstentity;
    }

    public Object getBotParams()
    {
        return botParams;
    }

    public void setBotParams(Object botParams)
    {
        this.botParams = botParams;
    }

    public Long getRequesterId()
    {
        return requesterId;
    }

    public void setRequesterId(Long requesterId)
    {
        this.requesterId = requesterId;
    }

    public String getTicketIcon()
    {
        return ticketIcon;
    }

    public void setTicketIcon(String ticketIcon)
    {
        this.ticketIcon = ticketIcon;
    }

    public String getItemName()
    {
        return itemName;
    }

    public void setItemName(String itemName)
    {
        this.itemName = itemName;
    }

    public String getCaseCreationDate()
    {
        return caseCreationDate;
    }

    public void setCaseCreationDate(String caseCreationDate)
    {
        this.caseCreationDate = caseCreationDate;
    }

    public String getTicketNumber()
    {
        return ticketNumber;
    }

    public void setTicketNumber(String ticketNumber)
    {
        this.ticketNumber = ticketNumber;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getFormattedCreatedDate()
    {
        return formattedCreatedDate;
    }

    public void setFormattedCreatedDate(String formattedCreatedDate)
    {
        this.formattedCreatedDate = formattedCreatedDate;
    }

    public String getUmpMid()
    {
        return umpMid;
    }

    public void setUmpMid(String umpMid)
    {
        this.umpMid = umpMid;
    }

    public String getViewType()
    {
        return viewType;
    }

    public void setViewType(String viewType)
    {
        this.viewType = viewType;
    }

    public String getFreshDeskUrl()
    {
        return freshDeskUrl;
    }

    public void setFreshDeskUrl(String freshDeskUrl)
    {
        this.freshDeskUrl = freshDeskUrl;
    }

    public String getTransactionId()
    {
        return transactionId;
    }

    public void setTransactionId(String transactionId)
    {
        this.transactionId = transactionId;
    }

    public String getL1IssueCategory()
    {
        return l1IssueCategory;
    }

    public void setL1IssueCategory(String l1IssueCategory)
    {
        this.l1IssueCategory = l1IssueCategory;
    }

    public String getVerticalLabel()
    {
        return verticalLabel;
    }

    public void setVerticalLabel(String verticalLabel)
    {
        this.verticalLabel = verticalLabel;
    }

    public Map<String, Object> getCustom_fields()
    {
        return custom_fields;
    }

    public void setCustom_fields(Map<String, Object> custom_fields)
    {
        this.custom_fields = custom_fields;
    }

    public List<String> getTags()
    {
        return tags;
    }

    public void setTags(List<String> tags)
    {
        this.tags = tags;
    }

    public String getCustomerIssueCategoryL1()
    {
        return customerIssueCategoryL1;
    }

    public void setCustomerIssueCategoryL1(String customerIssueCategoryL1)
    {
        this.customerIssueCategoryL1 = customerIssueCategoryL1;
    }

    public int getTicketStatus()
    {
        return ticketStatus;
    }

    public void setTicketStatus(int ticketStatus)
    {
        this.ticketStatus = ticketStatus;
    }

    public String getResponderId() {
        return responderId;
    }

    public void setResponderId(String responderId) {
        this.responderId = responderId;
    }

    public String getL2IssueCategory() {
        return l2IssueCategory;
    }

    public void setL2IssueCategory(String l2IssueCategory) {
        this.l2IssueCategory = l2IssueCategory;
    }

    public String getL3IssueCategory() {
        return l3IssueCategory;
    }

    public void setL3IssueCategory(String l3IssueCategory) {
        this.l3IssueCategory = l3IssueCategory;
    }

    public String getRawL1IssueCategory() {
        return rawL1IssueCategory;
    }

    public void setRawL1IssueCategory(String rawL1IssueCategory) {
        this.rawL1IssueCategory = rawL1IssueCategory;
    }

    public TicketInfoDTO()
    {
    }

    public TicketInfoDTO(String source, String createdAt, String updatedAt, String cstentity, Object botParams, Long requesterId, String ticketIcon, String itemName, String caseCreationDate, String ticketNumber, String status, String formattedCreatedDate, String umpMid, String viewType, String freshDeskUrl, String transactionId, String l1IssueCategory, String verticalLabel, Map<String, Object> custom_fields, List<String> tags, String customerIssueCategoryL1, int ticketStatus, int origin, String responderId, String l2IssueCategory, String l3IssueCategory, String rawL1IssueCategory) {
        this.source = source;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.cstentity = cstentity;
        this.botParams = botParams;
        this.requesterId = requesterId;
        this.ticketIcon = ticketIcon;
        this.itemName = itemName;
        this.caseCreationDate = caseCreationDate;
        this.ticketNumber = ticketNumber;
        this.status = status;
        this.formattedCreatedDate = formattedCreatedDate;
        this.umpMid = umpMid;
        this.viewType = viewType;
        this.freshDeskUrl = freshDeskUrl;
        this.transactionId = transactionId;
        this.l1IssueCategory = l1IssueCategory;
        this.verticalLabel = verticalLabel;
        this.custom_fields = custom_fields;
        this.tags = tags;
        this.customerIssueCategoryL1 = customerIssueCategoryL1;
        this.ticketStatus = ticketStatus;
        this.origin = origin;
        this.responderId=responderId;
        this.l2IssueCategory=l2IssueCategory;
        this.l3IssueCategory=l3IssueCategory;
        this.rawL1IssueCategory=rawL1IssueCategory;
    }

    @Override
    public String toString() {
        return "TicketInfoDTO{" +
                "source='" + source + '\'' +
                ", createdAt='" + createdAt + '\'' +
                ", updatedAt='" + updatedAt + '\'' +
                ", cstentity='" + cstentity + '\'' +
                ", botParams=" + botParams +
                ", requesterId=" + requesterId +
                ", ticketIcon='" + ticketIcon + '\'' +
                ", itemName='" + itemName + '\'' +
                ", caseCreationDate='" + caseCreationDate + '\'' +
                ", ticketNumber='" + ticketNumber + '\'' +
                ", status='" + status + '\'' +
                ", formattedCreatedDate='" + formattedCreatedDate + '\'' +
                ", umpMid='" + umpMid + '\'' +
                ", viewType='" + viewType + '\'' +
                ", freshDeskUrl='" + freshDeskUrl + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", l1IssueCategory='" + l1IssueCategory + '\'' +
                ", verticalLabel='" + verticalLabel + '\'' +
                ", custom_fields=" + custom_fields +
                ", tags=" + tags +
                ", customerIssueCategoryL1='" + customerIssueCategoryL1 + '\'' +
                ", ticketStatus=" + ticketStatus +
                ", origin=" + origin +
                ", responderId='" + responderId + '\'' +
                ", l2IssueCategory='" + l2IssueCategory + '\'' +
                ", l3IssueCategory='" + l3IssueCategory + '\'' +
                ", rawL1IssueCategory='" + rawL1IssueCategory + '\'' +
                '}';
    }
}
