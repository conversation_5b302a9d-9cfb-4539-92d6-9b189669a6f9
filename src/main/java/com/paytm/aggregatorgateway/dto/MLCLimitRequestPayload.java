package com.paytm.aggregatorgateway.dto;

public class MLCLimitRequestPayload {
    private String mid;
    private Double triggerValueMonthly;
    private String triggerTypeMonthly;
    private String triggerInstrumentMonthly;
    private String merchantType;
    private Double currentCumulativeLimitMonthly;
    private Double currentInstrumentLimitMonthly;
    private Boolean dailyLimitFlag;
    private Double currentCumulativeLimitDaily;
    private Double currentInstrumentLimitDaily;
    private Double triggerValueDaily;
    private String triggerTypeDaily;
    private String triggerInstrumentDaily;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public Double getTriggerValueMonthly() {
        return triggerValueMonthly;
    }

    public void setTriggerValueMonthly(Double triggerValueMonthly) {
        this.triggerValueMonthly = triggerValueMonthly;
    }

    public String getTriggerTypeMonthly() {
        return triggerTypeMonthly;
    }

    public void setTriggerTypeMonthly(String triggerTypeMonthly) {
        this.triggerTypeMonthly = triggerTypeMonthly;
    }

    public String getTriggerInstrumentMonthly() {
        return triggerInstrumentMonthly;
    }

    public void setTriggerInstrumentMonthly(String triggerInstrumentMonthly) {
        this.triggerInstrumentMonthly = triggerInstrumentMonthly;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public Double getCurrentCumulativeLimitMonthly() {
        return currentCumulativeLimitMonthly;
    }

    public void setCurrentCumulativeLimitMonthly(Double currentCumulativeLimitMonthly) {
        this.currentCumulativeLimitMonthly = currentCumulativeLimitMonthly;
    }

    public Double getCurrentInstrumentLimitMonthly() {
        return currentInstrumentLimitMonthly;
    }

    public void setCurrentInstrumentLimitMonthly(Double currentInstrumentLimitMonthly) {
        this.currentInstrumentLimitMonthly = currentInstrumentLimitMonthly;
    }

    public Boolean getDailyLimitFlag() {
        return dailyLimitFlag;
    }

    public void setDailyLimitFlag(Boolean dailyLimitFlag) {
        this.dailyLimitFlag = dailyLimitFlag;
    }

    public Double getCurrentCumulativeLimitDaily() {
        return currentCumulativeLimitDaily;
    }

    public void setCurrentCumulativeLimitDaily(Double currentCumulativeLimitDaily) {
        this.currentCumulativeLimitDaily = currentCumulativeLimitDaily;
    }

    public Double getCurrentInstrumentLimitDaily() {
        return currentInstrumentLimitDaily;
    }

    public void setCurrentInstrumentLimitDaily(Double currentInstrumentLimitDaily) {
        this.currentInstrumentLimitDaily = currentInstrumentLimitDaily;
    }

    public Double getTriggerValueDaily() {
        return triggerValueDaily;
    }

    public void setTriggerValueDaily(Double triggerValueDaily) {
        this.triggerValueDaily = triggerValueDaily;
    }

    public String getTriggerTypeDaily() {
        return triggerTypeDaily;
    }

    public void setTriggerTypeDaily(String triggerTypeDaily) {
        this.triggerTypeDaily = triggerTypeDaily;
    }

    public String getTriggerInstrumentDaily() {
        return triggerInstrumentDaily;
    }

    public void setTriggerInstrumentDaily(String triggerInstrumentDaily) {
        this.triggerInstrumentDaily = triggerInstrumentDaily;
    }
}
