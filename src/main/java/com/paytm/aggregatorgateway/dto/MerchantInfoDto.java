
package com.paytm.aggregatorgateway.dto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
@JsonIgnoreProperties(ignoreUnknown = true)
public class MerchantInfoDto
{
    private String mid;
    private String ppiLimit;
    private String merchantName;
    private String status;
    private String aggregator;
    private String firstName;
    private String lastName;
    private String primaryMailId;
    private String van;
    private String kybId;
    private String businessType;
    private String primaryMobileNumber;
    public String getMid()
    {
        return mid;
    }
    public void setMid(String mid)
    {
        this.mid = mid;
    }
    public String getPpiLimit()
    {
        return ppiLimit;
    }
    public void setPpiLimit(String ppiLimit)
    {
        this.ppiLimit = ppiLimit;
    }
    public String getMerchantName()
    {
        return merchantName;
    }
    public void setMerchantName(String merchantName)
    {
        this.merchantName = merchantName;
    }
    public String getStatus()
    {
        return status;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }
    public String getAggregator()
    {
        return aggregator;
    }
    public void setAggregator(String aggregator)
    {
        this.aggregator = aggregator;
    }
    public String getFirstName()
    {
        return firstName;
    }
    public void setFirstName(String firstName)
    {
        this.firstName = firstName;
    }
    public String getLastName()
    {
        return lastName;
    }
    public void setLastName(String lastName)
    {
        this.lastName = lastName;
    }
    public String getPrimaryMailId()
    {
        return primaryMailId;
    }
    public void setPrimaryMailId(String primaryMailId)
    {
        this.primaryMailId = primaryMailId;
    }
    public String getVan()
    {
        return van;
    }
    public void setVan(String van)
    {
        this.van = van;
    }
    public String getKybId()
    {
        return kybId;
    }
    public void setKybId(String kybId)
    {
        this.kybId = kybId;
    }
    public String getBusinessType()
    {
        return businessType;
    }
    public void setBusinessType(String businessType)
    {
        this.businessType = businessType;
    }
    public String getPrimaryMobileNumber()
    {
        return primaryMobileNumber;
    }
    public void setPrimaryMobileNumber(String primaryMobileNumber)
    {
        this.primaryMobileNumber = primaryMobileNumber;
    }
    @Override
    public String toString() {
        return "MerchantInfoDto{" +
                "mid='" + mid + '\'' +
                ", ppiLimit='" + ppiLimit + '\'' +
                ", merchantName='" + merchantName + '\'' +
                ", status='" + status + '\'' +
                ", aggregator='" + aggregator + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", primaryMailId='" + primaryMailId + '\'' +
                ", van='" + van + '\'' +
                ", kybId='" + kybId + '\'' +
                ", businessType='" + businessType + '\'' +
                ", primaryMobileNumber='" + primaryMobileNumber + '\'' +
                '}';
    }
}