package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class QualificationCallbackResponse {
    private String requestStatus;
    private Object result;

    public QualificationCallbackResponse(String requestStatus, Object result) {
        this.requestStatus = requestStatus;
        this.result = result;
    }
}
