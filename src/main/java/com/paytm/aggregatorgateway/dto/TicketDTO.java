package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;

public class TicketDTO {
    private String issue_category_l1;
    private String issue_category_l2;
    private String issue_category_l3;
    private String status;
    private String sub_status;
    private String ticket_id;

    // Constructors, getters, and setters

    public TicketDTO() {
    }

    public TicketDTO(String issue_category_l1, String issue_category_l2, String issue_category_l3, String status, String sub_status, String ticket_id) {
        this.issue_category_l1 = issue_category_l1;
        this.issue_category_l2 = issue_category_l2;
        this.issue_category_l3 = issue_category_l3;
        this.status = status;
        this.sub_status = sub_status;
        this.ticket_id = ticket_id;
    }

    public String getIssue_category_l1() {
        return issue_category_l1;
    }

    public void setIssue_category_l1(String issue_category_l1) {
        this.issue_category_l1 = issue_category_l1;
    }

    public String getIssue_category_l2() {
        return issue_category_l2;
    }

    public void setIssue_category_l2(String issue_category_l2) {
        this.issue_category_l2 = issue_category_l2;
    }

    public String getIssue_category_l3() {
        return issue_category_l3;
    }

    public void setIssue_category_l3(String issue_category_l3) {
        this.issue_category_l3 = issue_category_l3;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSub_status() {
        return sub_status;
    }

    public void setSub_status(String sub_status) {
        this.sub_status = sub_status;
    }

    public String getTicket_id() {
        return ticket_id;
    }

    public void setTicket_id(String ticket_id) {
        this.ticket_id = ticket_id;
    }
}





