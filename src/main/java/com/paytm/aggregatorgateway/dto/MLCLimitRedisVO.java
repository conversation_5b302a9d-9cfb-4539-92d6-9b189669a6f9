package com.paytm.aggregatorgateway.dto;

import java.time.LocalDateTime;

public class MLCLimitRedisVO {
    private MLCLimitVo notification;
    private LocalDateTime receivedAt;

    public MLCLimitRedisVO(MLCLimitVo notification, LocalDateTime receivedAt) {
        this.notification = notification;
        this.receivedAt = receivedAt;
    }

    public MLCLimitVo getNotification() {
        return notification;
    }

    public void setNotification(MLCLimitVo notification) {
        this.notification = notification;
    }

    public LocalDateTime getReceivedAt() {
        return receivedAt;
    }

    public void setReceivedAt(LocalDateTime receivedAt) {
        this.receivedAt = receivedAt;
    }
}
