package com.paytm.aggregatorgateway.dto;

import java.util.List;

public class MerchantDetailsVO {

	private String merchantName;
	private boolean isReseller;
	private List<ResellerDetail> resellerDetails;
	private boolean onlineMerchant;
	private String inactiveState;
	private String inactiveReason;
	private String inactiveDate;
	private String van;
	private KycDetailsVO kycDetails;
	private String kybId;
	private String primaryMobileNumber;
	private String secondaryMobileNumber;
	private String primaryMailId;
	private String secondaryMailId;
	private String firstName;
	private String lastName;
	private String ppiLimit;
	private String businessType;
	private String communicationContact;
	private List<String> reqTypeName;
	private List<String> bankTransferPaymodeVANPrefix;

	public String getMerchantName() {
		return merchantName;
	}

	public void setMerchantName(String merchantName) {
		this.merchantName = merchantName;
	}

	public boolean getIsReseller() {
		return isReseller;
	}

	public void setIsReseller(boolean isReseller) {
		this.isReseller = isReseller;
	}

	public boolean isReseller() {
		return isReseller;
	}

	public void setReseller(boolean reseller) {
		isReseller = reseller;
	}

	public List<ResellerDetail> getResellerDetails() {
		return resellerDetails;
	}

	public void setResellerDetails(List<ResellerDetail> resellerDetails) {
		this.resellerDetails = resellerDetails;
	}

	public boolean getOnlineMerchant() {
		return onlineMerchant;
	}

	public void setOnlineMerchant(boolean onlineMerchant) {
		this.onlineMerchant = onlineMerchant;
	}

	public String getInactiveState() {
		return inactiveState;
	}

	public void setInactiveState(String inactiveState) {
		this.inactiveState = inactiveState;
	}

	public String getInactiveReason() {
		return inactiveReason;
	}

	public void setInactiveReason(String inactiveReason) {
		this.inactiveReason = inactiveReason;
	}

	public String getInactiveDate() {
		return inactiveDate;
	}

	public void setInactiveDate(String inactiveDate) {
		this.inactiveDate = inactiveDate;
	}

	public String getSecondaryMobileNumber() {
		return secondaryMobileNumber;
	}

	public void setSecondaryMobileNumber(String secondaryMobileNumber) {
		this.secondaryMobileNumber = secondaryMobileNumber;
	}

	public String getSecondaryMailId() {
		return secondaryMailId;
	}

	public void setSecondaryMailId(String secondaryMailId) {
		this.secondaryMailId = secondaryMailId;
	}

	public String getCommunicationContact() {
		return communicationContact;
	}

	public void setCommunicationContact(String communicationContact) {
		this.communicationContact = communicationContact;
	}

	public String getPpiLimit() {
		return ppiLimit;
	}

	public void setPpiLimit(String ppiLimit) {
		this.ppiLimit = ppiLimit;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getVan() {
		return van;
	}

	public void setVan(String van) {
		this.van = van;
	}

	public KycDetailsVO getKycDetails() {
		return kycDetails;
	}

	public void setKycDetails(KycDetailsVO kycDetails) {
		this.kycDetails = kycDetails;
	}

	public String getKybId() {
		return kybId;
	}

	public void setKybId(String kybId) {
		this.kybId = kybId;
	}

	public String getPrimaryMobileNumber() {
		return primaryMobileNumber;
	}

	public void setPrimaryMobileNumber(String primaryMobileNumber) {
		this.primaryMobileNumber = primaryMobileNumber;
	}

	public String getPrimaryMailId() {
		return primaryMailId;
	}

	public void setPrimaryMailId(String primaryMailId) {
		this.primaryMailId = primaryMailId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public List<String> getReqTypeName() {
		return reqTypeName;
	}

	public void setReqTypeName(List<String> reqTypeName) {
		this.reqTypeName = reqTypeName;
	}

	public List<String> getBankTransferPaymodeVANPrefix() {
		return bankTransferPaymodeVANPrefix;
	}

	public void setBankTransferPaymodeVANPrefix(List<String> bankTransferPaymodeVANPrefix) {
		this.bankTransferPaymodeVANPrefix = bankTransferPaymodeVANPrefix;
	}

	@Override
	public String toString() {
		return "MerchantDetailsVO{" +
				"van='" + van + '\'' +
				", merchantName=" + merchantName +
				", isReseller=" + isReseller +
				", kycDetails=" + kycDetails +
				", kybId='" + kybId + '\'' +
				", primaryMobileNumber='" + primaryMobileNumber + '\'' +
				", secondaryMobileNumber='" + secondaryMobileNumber + '\'' +
				", primaryMailId='" + primaryMailId + '\'' +
				", secondaryMailId='" + secondaryMailId + '\'' +
				", firstName='" + firstName + '\'' +
				", lastName='" + lastName + '\'' +
				", ppiLimit='" + ppiLimit + '\'' +
				", businessType='" + businessType + '\'' +
				", communicationContact='" + communicationContact + '\'' +
				'}';
	}
}
