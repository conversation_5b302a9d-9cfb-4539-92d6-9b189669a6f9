package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TicketTimeLineDTO {
    private int displayOrder;
    private String statusFlag;
    private String headerText;
    private String descriptionText;
    private Map<String,Object> metadata;
    private long timestamp;
    private String deepLink;
}
