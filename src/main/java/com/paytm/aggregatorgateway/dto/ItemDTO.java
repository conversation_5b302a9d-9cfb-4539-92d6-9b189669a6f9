package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@ToString
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ItemDTO {
    private int id;
    private String name;
    private String image_url;
    private String url_type;
    private String url;
    private Map<String,Object>cta;
    private String ga_category;
    private int item_id;
    private Map<String,Object>layout;
    private int priority;
    private String seourl;
    private String source;
    private String alt_image_url;
}
