package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@ToString
public class ReqParamsToUpdateStatus {
    private String entityId;
    private String entityType;
    private String preferenceKey;
    private List<PreferenceValue> preferenceValue;
    private Long version;
    @Setter
    @Getter
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    @ToString
    public static class PreferenceValue {
        private String custId;
        private String isSubuser;
        private String deviceId;
        private String nfcEnabled;
        private String isSecurityShieldEnabled;
    }
}