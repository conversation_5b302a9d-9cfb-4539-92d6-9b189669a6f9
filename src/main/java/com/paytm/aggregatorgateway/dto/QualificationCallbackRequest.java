package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class QualificationCallbackRequest {
    private String status;
    private String statusCode;
    private String statusMessage;
    private String requestId;
    private QualificationResponse response;

    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QualificationResponse {
        private List<String> processedImageFileIds;
        private Map<String, ImageParameters> imageWiseParameters;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageParameters {
        private ParameterValue document_type;
        private ParameterValue liveliness;
        private ParameterValue completeness;
        private ParameterValue valid;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ParameterValue {
        private String value;
        private Double confidence;
    }
}
