package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@NoArgsConstructor
public class UPSUpdateDto {
    private UPSResponseStatusInfo statusInfo;
    private List<MidDeviceDetailDto> response;
    private String requestId;
    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    @ToString
    public static class UPSResponseStatusInfo {
        private String status;
        private String statusCode;
        private String statusMessage;
    }
    @Setter
    @Getter
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    @ToString
    public static class MidDeviceDetailDto {
        private String entityId;
        private String entityType;
        private List<PreferenceDto> preferences;
    }
    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    @ToString
    public static class PreferenceDto {
        private String key;
        private List<PreferenceValueDto> value;
        private long version;
    }
    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    @ToString
    public static class PreferenceValueDto {
        private String custId;
        private String isSubuser;
        private String deviceId;
        private String nfcEnabled;
        private String isSecurityShieldEnabled;
    }
}

