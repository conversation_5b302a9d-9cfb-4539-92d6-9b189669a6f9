package com.paytm.aggregatorgateway.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@ToString
public class ReqParamsToFetchUPSTagUntag {
    private String entityId; //mid
    private String entityType; // type: mid
    private List<String> preferenceKeys; // provided by ups
}
