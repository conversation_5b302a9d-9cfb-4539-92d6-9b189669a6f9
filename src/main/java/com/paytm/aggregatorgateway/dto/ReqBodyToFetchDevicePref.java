package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


public class ReqBodyToFetchDevicePref
{
    private String entityId;

    private String entityType;

    private List<String> preferenceKeys;

    public String getEntityId()
    {
        return entityId;
    }

    public void setEntityId(String entityId)
    {
        this.entityId = entityId;
    }

    public String getEntityType()
    {
        return entityType;
    }

    public void setEntityType(String entityType)
    {
        this.entityType = entityType;
    }

    public List<String> getPreferenceKeys()
    {
        return preferenceKeys;
    }

    public void setPreferenceKeys(List<String> preferenceKeys)
    {
        this.preferenceKeys = preferenceKeys;
    }

    @Override
    public String toString() {
        return "ReqBodyToFetchDevicePref{" +
                "entityId='" + entityId + '\'' +
                ", entityType='" + entityType + '\'' +
                ", preferenceKeys=" + preferenceKeys +
                '}';
    }
}