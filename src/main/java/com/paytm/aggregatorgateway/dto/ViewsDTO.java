package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ViewsDTO {
    private String type;
    private int id;
    private String title;
    private String image_url;
    private List<ItemDTO> items;
    private String seourl;
    private String auto_scroll;
    private boolean is_bg_active;
    private Map<String,Object> meta_layout;

}
