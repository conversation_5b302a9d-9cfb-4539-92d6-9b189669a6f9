package com.paytm.aggregatorgateway.dto;

import java.time.LocalDateTime;

public class MLCLimitVo {
    private String merchantId;
    private String identifier;
    private String consultId;
    private String status;
    private String comment;
    private String consultDate;
    private String clientId;
    private String timestamp;
    private MLCLimitRequestPayload requestPayload;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getConsultId() {
        return consultId;
    }

    public void setConsultId(String consultId) {
        this.consultId = consultId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getConsultDate() {
        return consultDate;
    }

    public void setConsultDate(String consultDate) {
        this.consultDate = consultDate;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public MLCLimitRequestPayload getRequestPayload() {
        return requestPayload;
    }

    public void setRequestPayload(MLCLimitRequestPayload requestPayload) {
        this.requestPayload = requestPayload;
    }
}
