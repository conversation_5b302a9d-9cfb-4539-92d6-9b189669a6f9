package com.paytm.aggregatorgateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeductionInitiateRequest {
    private String clientRequestId;
    private String customerId;
    private List<ImageInfo> images;
    private String callbackUrl;

    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageInfo {
        private String dms_id;
        private String document_type;

        public ImageInfo(String dms_id, String document_type) {
            this.dms_id = dms_id;
            this.document_type = document_type;
        }
    }
}
