package com.paytm.aggregatorgateway.dto;

import lombok.*;
import org.springframework.security.access.annotation.Secured;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CategoryDTO {
    private int id;
    private int priority;
    private String name;
    private String image_url;
    private String url_type;
    private String url;
    private String source;
    private String seourl;
    private Map<String,Object> layout;
    private int item_id;
    private Map<String,Object>cta;
    private String ga_category;

    public CategoryDTO(ItemDTO itemDTO) {
        this.setId(itemDTO.getId());
        this.setPriority(itemDTO.getPriority());
        this.setName(itemDTO.getName());
        this.setImage_url(itemDTO.getImage_url());
        this.setUrl_type(itemDTO.getUrl_type());
        this.setUrl(itemDTO.getUrl());
        this.setSeourl(itemDTO.getSource());
        this.setSeourl(itemDTO.getSeourl());
        this.setLayout(itemDTO.getLayout());
        this.setItem_id(itemDTO.getItem_id());
        this.setCta(itemDTO.getCta());
        this.setGa_category(itemDTO.getGa_category());
    }
}
