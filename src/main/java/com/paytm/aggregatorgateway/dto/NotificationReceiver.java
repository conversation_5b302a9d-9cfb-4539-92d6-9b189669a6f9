package com.paytm.aggregatorgateway.dto;
import java.util.List;
public class NotificationReceiver
{
    private String notificationReceiverType;
    private List<String> notificationReceiverIdentifier;
    public NotificationReceiver(List<String> notificationReceiverIdentifier, String notificationReceiverType)
    {
        super();
        this.notificationReceiverType=notificationReceiverType;
        this.notificationReceiverIdentifier = notificationReceiverIdentifier;
    }
    public String getNotificationReceiverType()
    {
        return notificationReceiverType;
    }
    public List<String> getNotificationReceiverIdentifier()
    {
        return notificationReceiverIdentifier;
    }
    public void setNotificationReceiverIdentifier(List<String> notificationReceiverIdentifier)
    {
        this.notificationReceiverIdentifier = notificationReceiverIdentifier;
    }
    @Override
    public String toString()
    {
        return "NotificationReceiver [notificationReceiverType=" + notificationReceiverType
                + ", notificationReceiverIdentifier=" + notificationReceiverIdentifier + "]";
    }
}