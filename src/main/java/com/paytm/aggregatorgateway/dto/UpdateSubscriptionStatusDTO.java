package com.paytm.aggregatorgateway.dto;

import jakarta.validation.constraints.NotNull;

public class UpdateSubscriptionStatusDTO {
	
   private String mid;
   private String custId;
   
   @NotNull(message = "usn cannot be null")
   private String usn;
   
   @NotNull(message = "serviceName cannot be null")
   private String serviceName;
   
   @NotNull(message = "subscriptionType cannot be null")
   private String subscriptionType;
   
   @NotNull(message = "status cannot be null")
   private String status;
   
   private String phoneNo;
   private String planPrice;
   private String frequency;
   private String endDate;
   private Boolean tempSuspend;
   private Boolean instantSmsDisable;
   private Boolean instantSmsEnable;
   
   
public Boolean getInstantSmsEnable() {
	return instantSmsEnable;
}
public void setInstantSmsEnable(Boolean instantSmsEnable) {
	this.instantSmsEnable = instantSmsEnable;
}
public String getMid() {
	return mid;
}
public void setMid(String mid) {
	this.mid = mid;
}
public String getUsn() {
	return usn;
}
public void setUsn(String usn) {
	this.usn = usn;
}
public String getServiceName() {
	return serviceName;
}
public void setServiceName(String serviceName) {
	this.serviceName = serviceName;
}
public String getSubscriptionType() {
	return subscriptionType;
}
public void setSubscriptionType(String subscriptionType) {
	this.subscriptionType = subscriptionType;
}
public String getStatus() {
	return status;
}
public void setStatus(String status) {
	this.status = status;
}
public String getCustId() {
	return custId;
}
public void setCustId(String custId) {
	this.custId = custId;
}
public String getPhoneNo() {
	return phoneNo;
}
public void setPhoneNo(String phoneNo) {
	this.phoneNo = phoneNo;
}
public String getPlanPrice() {
	return planPrice;
}
public void setPlanPrice(String planPrice) {
	this.planPrice = planPrice;
}
public String getFrequency() {
	return frequency;
}
public void setFrequency(String frequency) {
	this.frequency = frequency;
}
public String getEndDate() {
	return endDate;
}
public void setEndDate(String endDate) {
	this.endDate = endDate;
}

public Boolean getTempSuspend() {
	return tempSuspend;
}
public void setTempSuspend(Boolean tempSuspend) {
	this.tempSuspend = tempSuspend;
}
public Boolean getInstantSmsDisable() {
	return instantSmsDisable;
}
public void setInstantSmsDisable(Boolean instantSmsDisable) {
	this.instantSmsDisable = instantSmsDisable;
}
@Override
public String toString() {
	return "UpdateSubscriptionStatusDTO [usn=" + usn + ", serviceName=" + serviceName + ", subscriptionType="
			+ subscriptionType + ", status=" + status + ", phoneNo=" + phoneNo + ", planPrice=" + planPrice
			+ ", frequency=" + frequency + ", endDate=" + endDate + ", tempSuspend=" + tempSuspend + ", instantSmsDisable=" + instantSmsDisable + ", instantSmsEnable=" + instantSmsEnable + "]";
}

}


