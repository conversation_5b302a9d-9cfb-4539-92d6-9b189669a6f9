package com.paytm.aggregatorgateway.dto;
import java.util.Map;
public class NotificationsVO
{
    private NotificationReceiver notificationReceiver;
    private String templateName;
    private Map<String, String> dynamicParams;
    public NotificationReceiver getNotificationReceiver()
    {
        return notificationReceiver;
    }
    public void setNotificationReceiver(NotificationReceiver notificationReceiver)
    {
        this.notificationReceiver = notificationReceiver;
    }
    public String getTemplateName()
    {
        return templateName;
    }
    public void setTemplateName(String templateName)
    {
        this.templateName = templateName;
    }
    public Map<String, String> getDynamicParams()
    {
        return dynamicParams;
    }
    public void setDynamicParams(Map<String, String> dynamicParams)
    {
        this.dynamicParams = dynamicParams;
    }
    @Override
    public String toString()
    {
        return "NotificationsVO [notificationReceiver=" + notificationReceiver + ", templateName="
                + templateName + ", dynamicParams=" + dynamicParams + ", scheduleNotification=" + "]";
    }
}