package com.paytm.aggregatorgateway.dto;

import java.time.LocalDateTime;
import java.util.Date;

public class WidgetInfoDTO {

    private String mid;
    private String status;

    private String featureType;

    private String identifierKey;

    private String identifierValue;

    private Date expiryTime;

    private String type;

    private String metadata;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFeatureType() {
        return featureType;
    }

    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }

    public String getIdentifierKey() {
        return identifierKey;
    }

    public void setIdentifierKey(String identifierKey) {
        this.identifierKey = identifierKey;
    }

    public String getIdentifierValue() {
        return identifierValue;
    }

    public void setIdentifierValue(String identifierValue) {
        this.identifierValue = identifierValue;
    }

    public Date getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

    public String getType(){return this.type;}

    public void setType(String type){this.type = type;}

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    @Override
    public String toString() {
        return "WidgetInfoDTO{" +
                "mid='" + mid + '\'' +
                ", status='" + status + '\'' +
                ", featureType='" + featureType + '\'' +
                ", identifierKey='" + identifierKey + '\'' +
                ", identifierValue='" + identifierValue + '\'' +
                ", expiryTime=" + expiryTime +
                ", type='" + type + '\'' +
                ", metadata='" + metadata + '\'' +
                '}';
    }
}
