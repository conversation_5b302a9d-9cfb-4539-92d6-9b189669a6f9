package com.paytm.aggregatorgateway.controller.healthcheck;

import com.paytm.aggregatorgateway.app.WarmUpRequests;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping
public class HealthCheckController {
	
    Logger logger = LoggerFactory.getLogger(HealthCheckController.class);

    public static boolean warmUpCompleted = true;

    HealthCheckController(){
        logger.info("HealthCheckController object created");
    }
    
    @GetMapping("/healthcheck")
    public void healthCheck(){
        if (!warmUpCompleted) {
            if(!WarmUpRequests.completableFutureList.isEmpty() && WarmUpRequests.completableFutureList.parallelStream().anyMatch(f -> !f.isDone())) {
                throw new RuntimeException("warm up is not completed");
            }
            warmUpCompleted = true;
            WarmUpRequests.completableFutureList.clear();
        }
        logger.info("Aggregator Gateway V2 service is up and running.");
    }

    @GetMapping("/status/live")
    public void livenessCheck() {
        logger.info("Aggregator Gateway V2 service is live.");
    }

    @GetMapping("/status/ready")
    public void readinessCheck() {
        if (!warmUpCompleted) {
            if(!WarmUpRequests.completableFutureList.isEmpty() && WarmUpRequests.completableFutureList.parallelStream().anyMatch(f -> !f.isDone())) {
                throw new RuntimeException("warm up is not completed");
            }
            warmUpCompleted = true;
            WarmUpRequests.completableFutureList.clear();
        }
        if(AWSSecretManager.awsSecretsMap.isEmpty()) {
            throw new RuntimeException("Secret keys are not successfully fetched from AWS.");
        }
        logger.info("Aggregator Gateway V2 service is ready.");
    }
}
