package com.paytm.aggregatorgateway.controller.advice;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.HashMap;
import java.util.Map;

import static com.paytm.aggregatorgateway.constants.ApplicationConstants.ERROR_MESSAGE;

@ControllerAdvice(basePackages = "com.paytm")
@Order(Ordered.HIGHEST_PRECEDENCE)
public class BFFExceptionHandler {
	private static final Logger logger = LogManager.getLogger(BFFExceptionHandler.class);

	@ExceptionHandler(AccessDeniedException.class)
	@ResponseStatus(HttpStatus.FORBIDDEN)
	@ResponseBody
	public Map<String, String> accessDenied(AccessDeniedException ex) {
		logger.warn("ACCESS DENIED");
		Map<String, String> error = new HashMap<>(1);
		error.put("access_denied", "Access Denied");
		return error;
	}

	@ExceptionHandler(ValidationException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ResponseBody
	public Map<String, String> validationFailed(ValidationException ex) {
		logger.info("Returning ValidationException: {}", ex.getErrors());
		return ex.getErrors();
	}

	@ExceptionHandler({ServletRequestBindingException.class, MethodArgumentTypeMismatchException.class,
			MethodArgumentNotValidException.class, jakarta.validation.ValidationException.class})
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ResponseBody
	public ResponseUmp handleMissingAndMalformedParametersValues(Exception ex) {
		logger.error("Wrong parameters passed:{}", ex);
		ResponseUmp error = new ResponseUmp();
		error.setStatus(HttpStatus.BAD_REQUEST.name());
		error.setStatusCode(String.valueOf(HttpStatus.BAD_REQUEST.value()));
		error.setStatusMessage(ex.getMessage());
		return error;
	}

	@ExceptionHandler(HttpRequestMethodNotSupportedException.class)
	@ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
	@ResponseBody
	public Map<String, String> methodHandler(HttpRequestMethodNotSupportedException ex) {
		logger.error("Method Not Supported", ex);
		Map<String, String> error = new HashMap<>(1);
		error.put("method_not_supported", ExceptionUtils.getRootCauseMessage(ex));
		return error;
	}

	@ExceptionHandler(HttpMediaTypeNotSupportedException.class)
	@ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
	@ResponseBody
	public Map<String, String> mediaTypeHandler(HttpMediaTypeNotSupportedException ex) {
		logger.error("Media Type Not Supported", ex);
		Map<String, String> error = new HashMap<>(1);
		error.put("unsupported_media_type", ExceptionUtils.getRootCauseMessage(ex));
		return error;
	}

	@org.springframework.web.bind.annotation.ExceptionHandler(Exception.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ResponseBody
	public Map<String, String> internalError(Exception ex) {
		logger.error("Internal Server Error", ex);
		Map<String, String> error = new HashMap<>(1);
		error.put("internal_error", "Exception Occured");
		String rootCauseMessage = ExceptionUtils.getRootCauseMessage(ex);
		logger.error("Error occur: {}", rootCauseMessage);
		return error;
	}

	@org.springframework.web.bind.annotation.ExceptionHandler(UMPIntegrationException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ResponseBody
	public Map<String, String> integrationException(UMPIntegrationException ex) {
		logger.error("Integration exception", ex);
		Map<String, String> error = new HashMap<>();
		error.put("message", ex.getMessage());
		if(StringUtils.isNotBlank(ex.getErrorCode().getErrorCode()))
			error.put("errorCode", ex.getErrorCode().getErrorCode());
		error.put("internal_error", ExceptionUtils.getRootCauseMessage(ex));
		return error;
	}
	@org.springframework.web.bind.annotation.ExceptionHandler(ResponseUmpException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ResponseBody
	public ResponseUmp responseUmpException(ResponseUmpException e) {
		logger.info("Returning ResponseUmpException: {}", e.getResponse());
		return e.getResponse();
	}

	@org.springframework.web.bind.annotation.ExceptionHandler(IllegalArgumentException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	@ResponseBody
	public Map<String, String> illegalArgument(IllegalArgumentException ex) {
		logger.error("illegalArgument exception", ex);
		Map<String, String> error = new HashMap<>(1);
		error.put(ERROR_MESSAGE,"IllegalArgumentException occured");
		String rootCauseMessage = ExceptionUtils.getRootCauseMessage(ex);
		logger.error("Returning illegalArgument exception: {}", rootCauseMessage);
		return error;
	}

	@org.springframework.web.bind.annotation.ExceptionHandler(HttpMessageNotReadableException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ResponseBody
	public Map<String, String> httpMessageNotReadableException(HttpMessageNotReadableException ex) {
		logger.error("HttpMessageNotReadableException exception", ex);
		Map<String, String> error = new HashMap<>(1);
		error.put(ERROR_MESSAGE, "HttpMessageNotReadableException occured");
		String rootCauseMessage = ExceptionUtils.getRootCauseMessage(ex);
		logger.error("Returning HttpMessageNotReadableException exception: {}", rootCauseMessage);
		return error;
	}

	@org.springframework.web.bind.annotation.ExceptionHandler(BindException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ResponseBody
	public Map<String, String> bindException(BindException ex) {
		logger.error("BindException exception", ex);
		Map<String, String> error = new HashMap<>(1);
		error.put(ERROR_MESSAGE, "BindException occured");
		String rootCauseMessage = ExceptionUtils.getRootCauseMessage(ex);
		logger.error("Returning BindException exception: {}", rootCauseMessage);
		return error;
	}

	@org.springframework.web.bind.annotation.ExceptionHandler(InvalidFormatException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ResponseBody
	public Map<String, String> invalidFormatException(InvalidFormatException ex) {
		logger.error("InvalidFormatException exception", ex);
		Map<String, String> error = new HashMap<>(1);
		error.put(ERROR_MESSAGE, "InvalidFormatException occured");
		String rootCauseMessage = ExceptionUtils.getRootCauseMessage(ex);
		logger.error("Returning InvalidFormatException exception: {}", rootCauseMessage);
		return error;
	}

}
