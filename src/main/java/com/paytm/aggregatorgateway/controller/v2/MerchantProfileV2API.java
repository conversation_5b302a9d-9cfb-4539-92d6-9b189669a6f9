/**
 * 
 */
package com.paytm.aggregatorgateway.controller.v2;


import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import jakarta.servlet.http.HttpServletRequest;

import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.dto.OutletAndBusinessPreference;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/v2/merchantprofile")
public class MerchantProfileV2API implements PayTmPGConstants {
	private final Logger LOGGER = LogManager.getLogger(MerchantProfileV2API.class);

	@Autowired
	private MerchantProfileService merchantProfileService;


	@GetMapping("/notification")
	public Map<String, Object> getNotificationSettings(@RequestParam String type, HttpServletRequest httpRequest) throws Exception {
		// refer-->https://wiki.mypaytm.com/display/PPD/Notification+Alert+apis
		// refer boss
		// wiki-->https://wiki.mypaytm.com/display/PPD/Communication+Api+%7C+Transaction+%7C+Refund

		if (StringUtils.isBlank(type)) {
			throw new ValidationException("Invalid_Params", "Invalid type field passed");
		}
		Set<String> typeSet = new HashSet<>();
		typeSet.add("ALL");// Both transaction and refund communication configuration will be returned.
		typeSet.add("TRANSACTION");// Transaction communication configuration will be returned.
		typeSet.add("REFUND");// Refund communication configuration will be returned
		if (!typeSet.contains(type)) {
			throw new ValidationException("Invalid_Params", "Invalid type field passed");
		}
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		String client = httpRequest.getHeader("client");
		Map<String, Object> response = merchantProfileService.getCommunicationConfiguration(mid, type, client);
		return response;
	}

	//@Secured("ROLE_MERCHANTPROFILE_SUBUSEREDITNOTIFICATION")
	@PostMapping("/notification")
	public Map<String, Object> setNotificationSettings(@RequestBody Map<String, Object> requestBody, HttpServletRequest httpRequest) throws Exception {
		// refer-->https://wiki.mypaytm.com/display/PPD/Notification+Alert+apis
		// refer boss
		// wiki-->https://wiki.mypaytm.com/display/PPD/Communication+Api+%7C+Transaction+%7C+Refund


		if (Objects.isNull(requestBody) || requestBody.isEmpty()) {
			throw new ValidationException("Invalid_Params", "No Configuration setting passed");
		}

		if(!requestBody.containsKey("transaction") && !requestBody.containsKey("refund"))
		{
			throw new ValidationException("Invalid_Params", "Invalid params");
		}

		String mid = SecurityUtils.getCurrentMerchant().getMid();
		if(requestBody.containsKey("transaction")) {
			Map<String,Object> txnMap = MappingUtils.convertObjectToType(requestBody.get("transaction"), Map.class);
			if(txnMap!=null && txnMap.containsKey("event")&& !ObjectUtils.isEmpty(txnMap.containsKey("event"))) {
					txnMap.put("event","SUCCESS");
					requestBody.put("transaction", txnMap);
			}
		}
		Map<String, Object> response = merchantProfileService.setCommunicationConfiguration(requestBody, mid, httpRequest);
		return response;
	}

	@RequestMapping(value = "/elig/check", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public OutletAndBusinessPreference getOutletAndBusinessPreference(){
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		return merchantProfileService.getMerchantOutletAndBusinessPreference(mid);
	}
	@RequestMapping(value = "/elig/agg", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseUmp checkAggEligibility() throws InterruptedException, JsonProcessingException {
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		return merchantProfileService.checkAggEligibility(mid);
	}
	


}
