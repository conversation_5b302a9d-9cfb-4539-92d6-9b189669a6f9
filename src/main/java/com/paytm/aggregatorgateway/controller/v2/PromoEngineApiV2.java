package com.paytm.aggregatorgateway.controller.v2;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.PromoEngineService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.utils.ValidationUtil;
import com.paytm.aggregatorgateway.vo.PromoRequestVO;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 *<AUTHOR> 
 **/
@RestController
@RequestMapping("/api/v2/promo")
public class PromoEngineApiV2 {

	private static final Logger LOGGER = LogManager.getLogger(PromoEngineApiV2.class);

	@Autowired
	private PromoEngineService promoEngineService;

	@GetMapping(value = "/games", produces = MediaType.APPLICATION_JSON_VALUE)
	public String gameList(PromoRequestVO request) throws Exception {
		ValidationUtil.validateInteger(String.valueOf(request.getPage_number()));
		ValidationUtil.validateInteger(String.valueOf(request.getPage_size()));
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		request.setMerchant_id(mid);
		return promoEngineService.getGameListV2(request);
	}
	
	@GetMapping(value = "/games/campaign-games/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public String campaignGame(@PathVariable("id") String id) throws Exception {
        ValidationUtil.validateInteger(id);
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		return promoEngineService.getCampaignGameV2(id, mid);
	}

	@PostMapping(value = "/offer/{campaign_id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public String selectOffer(@PathVariable("campaign_id") String campaignId, @RequestBody String body) throws Exception {
        ValidationUtil.validateInteger(campaignId);
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		return promoEngineService.selectOffer(campaignId, body, mid);
	}

	@GetMapping(value = "/games/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public String gameDetails(@PathVariable("id") String id) throws Exception {
        ValidationUtil.validateInteger(id);
		LOGGER.info("Request received at game details ");
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		return promoEngineService.gameDetails(id, mid);
	}
	
	@GetMapping(value = "/cashback", produces = MediaType.APPLICATION_JSON_VALUE)
	public Map<String,Object> campaignGames() throws Exception {
		LOGGER.info("Request received at game details ");
		return promoEngineService.campaignGames();
	}


}
