package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.utils.ValidationUtil;
import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.LoyaltyPointsService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

import static com.paytm.aggregatorgateway.constants.ApplicationConstants.*;

@RestController
@RequestMapping("/api/v1/loyaltypoints")
public class LoyaltyPointsAPI {
	private static final Logger LOGGER = LogManager.getLogger(LoyaltyPointsAPI.class);

	@Autowired
	private LoyaltyPointsService service;

	@GetMapping("/summary")
	public ResponseUmp getSummary() throws Exception {

		//LOGGER.info("In getSummary");
		Merchant merchant = SecurityUtils.getCurrentMerchant();

		if(Objects.isNull(merchant))
			throw new ValidationException(MERCHANT_NOT_FOUND, FAILED_TO_GET_MERCHANT);

		String mid = merchant.getMid();
		String uid = service.fetchUid(mid);
		return service.getSummary(uid);
	}

	@GetMapping("/list")
	public ResponseUmp getList(@RequestParam Map<String, Object> map) throws Exception {

		if(map.containsKey("pageNum") )
			ValidationUtil.validateInteger(MapUtils.getString(map, "pageNum"));
		if(map.containsKey("pageSize") )
			ValidationUtil.validateInteger(MapUtils.getString(map, "pageSize"));

		LOGGER.info("In getList ");
		Merchant merchant = SecurityUtils.getCurrentMerchant();

		if(Objects.isNull(merchant))
			throw new ValidationException(MERCHANT_NOT_FOUND, FAILED_TO_GET_MERCHANT);

		String mid = merchant.getMid();
		String uid = service.fetchUid(mid);
		return service.getList(map, uid);
	}
	
	@PostMapping("/checkBalance")
	public String checkBalance() throws Exception {

		//LOGGER.info("In getSummary");
		Merchant merchant = SecurityUtils.getCurrentMerchant();

		if(Objects.isNull(merchant))
			throw new ValidationException(MERCHANT_NOT_FOUND, FAILED_TO_GET_MERCHANT);

		String mid = merchant.getMid();
		String uid = service.fetchUid(mid);
		return service.checkBalance(uid);
	}

}