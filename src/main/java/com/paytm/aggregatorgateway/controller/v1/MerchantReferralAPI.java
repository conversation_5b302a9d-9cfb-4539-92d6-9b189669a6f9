package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.MerchantReferralService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.utils.ValidationUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/v1/merchant/referral")
public class MerchantReferralAPI {

	private static final Logger LOGGER = LogManager.getLogger(MerchantReferralAPI.class);

	@Autowired
	private MerchantReferralService merchantReferralService;
	
	@GetMapping
	public String getReferral(@RequestParam String tag, @RequestParam(required = false) String deviceIdentifier, @RequestParam(required = false) String locale) throws Exception {

		ValidationUtil.validateAlphanumericwithhyphen(locale);
		ValidationUtil.validateAlphanumericwithhyphen(deviceIdentifier);
		LOGGER.info("Get Referral API called");
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		return merchantReferralService.getReferral(mid, tag, deviceIdentifier, locale);
	}
}
