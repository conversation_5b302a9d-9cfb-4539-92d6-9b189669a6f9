package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.ConsentService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@RestController
@Slf4j
@RequestMapping("/api/v1/consent")
public class ConsentAPI {

    private static final Set<String> VALID_PREFERENCES = new HashSet<>(Arrays.asList("SMS", "LOCATION"));
    private static final Set<String> VALID_TYPES = new HashSet<>(Arrays.asList("ALWAYS", "WHILE_USING"));

    @Autowired
    private ConsentService consentService;

    /**
     * Stores user consent for SMS or location permissions
     *
     * @param requestBody Contains preference and type parameters
     * @param headers Contains device_id and appversion
     * @return ResponseUmp with success or failure status
     */
    @PostMapping
    public ResponseUmp storeConsent(@RequestBody Map<String, String> requestBody, @RequestHeader Map<String, String> headers) {

        String deviceId = headers.get("deviceidentifier");
        String appversion = headers.getOrDefault("appversion", null);
        String preference = requestBody.get("preference");
        String type = requestBody.get("type");
        String mid = SecurityUtils.getCurrentMerchant().getMid();
        String custId = SecurityUtils.getCurrentMerchant().getAdminUserId();

        ResponseUmp validationResponse = validateParameters(deviceId, appversion, mid, custId, preference, type);
        if (validationResponse != null) {
            return validationResponse;
        }

        return consentService.storeConsentTimeStamp(mid, deviceId, custId, appversion, preference, type);
    }

    private ResponseUmp validateParameters(String deviceId, String appversion, String mid, String custId, String preference, String type) {

        if (StringUtils.isBlank(deviceId)) {
            return new ResponseUmp("FAILURE", "400", "Missing required field: deviceId", null);
        }

        if (StringUtils.isBlank(appversion)) {
            return new ResponseUmp("FAILURE", "400", "Missing required field: appversion", null);
        }

        if (StringUtils.isBlank(mid)) {
            return new ResponseUmp("FAILURE", "400", "Missing required field: mid", null);
        }

        if (StringUtils.isBlank(custId)) {
            return new ResponseUmp("FAILURE", "400", "Missing required field: custId", null);
        }

        if (StringUtils.isBlank(preference)) {
            return new ResponseUmp("FAILURE", "400", "Missing required field: preference", null);
        }

        if (StringUtils.isBlank(type)) {
            return new ResponseUmp("FAILURE", "400", "Missing required field: type", null);
        }

        String preferenceUpper = preference.toUpperCase();
        String typeUpper = type.toUpperCase();

        if (!VALID_PREFERENCES.contains(preferenceUpper)) {
            log.warn("Invalid preference value: {}", preference);
            return new ResponseUmp("FAILURE", "400", "Invalid preference value. Must be one of: " + VALID_PREFERENCES, null);
        }

        if (!VALID_TYPES.contains(typeUpper)) {
            log.warn("Invalid type value: {}", type);
            return new ResponseUmp("FAILURE", "400", "Invalid type value. Must be one of: " + VALID_TYPES, null);
        }

        return null;
    }
}
