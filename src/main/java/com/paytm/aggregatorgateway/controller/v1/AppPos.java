package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.AppPosService;
import com.paytm.aggregatorgateway.utils.ValidationUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/mpos")
public class AppPos {
    private static final Logger LOGGER = LogManager.getLogger(AppPos.class);

    @Autowired
    AppPosService appPosService;

    @GetMapping(value = "/terminal")
    public String checkStatus(@RequestParam Map<String, String> queryParamMap)
            throws Exception {

        if(queryParamMap.containsKey("vendorName") && !"MOSAMBEE".equalsIgnoreCase(queryParamMap.get("vendorName")))
        {
            throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION,"invalid vendorName");
        }

         if(queryParamMap.containsKey("deviceId") )
             ValidationUtil.validateAlphanumericwithhyphen(queryParamMap.get("deviceId"));

        //LOGGER.info("Enter to fetch terminal status");

        return appPosService.checkStatus(queryParamMap);

    }
}


