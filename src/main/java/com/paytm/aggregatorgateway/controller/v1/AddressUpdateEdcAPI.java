package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.MaquetteService;
import com.paytm.aggregatorgateway.service.AddressUpdateEdcService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/api/v1/address/edc")
public class AddressUpdateEdcAPI {

    @Autowired
    AddressUpdateEdcService addressUpdateEdcService;

    @Autowired
    MaquetteService maquetteService;

    @PostMapping(value = "/update")
    public ResponseUmp updateEdcAddress(@RequestBody Map<String, Object> requestBody) throws Exception {
        //log.info("Entering into /api/v1/address/update/edc API for mid: {}", SecurityUtils.getCurrentMerchant().getMid());

        if(!requestBody.containsKey("deviceId"))
            throw new ValidationException(UMPErrorCodeEnums.MISSING_PARAM, " deviceId missing");

        if(!requestBody.containsKey("addressLine1") || !requestBody.containsKey("addressLine2") || !requestBody.containsKey("city") ||
                !requestBody.containsKey("state") || !requestBody.containsKey("postalCode"))
            throw new ValidationException(UMPErrorCodeEnums.MISSING_PARAM, " Incomplete address");

        return addressUpdateEdcService.updateEdcAddress(requestBody, SecurityUtils.getCurrentMerchant().getMid());
    }

    @PostMapping(value = "/validate/location")
    public ResponseUmp verifyLatLong(@RequestBody Map<String, Object> requestBody) throws Exception {
        log.info("Entering into /api/v1/maquette/latlong for mid {}", SecurityUtils.getCurrentMerchant().getMid());
        return maquetteService.verifyLatLong(requestBody);
    }

    @GetMapping(value = "/fetch")
    public ResponseUmp getEdcAddress(@RequestParam(required=true) String deviceId, @RequestParam(required=false) Boolean deploymentAddressStatus, @RequestParam(required=false) Boolean lastTransactionAddress) throws Exception {
        log.info("Entering into api/v1/address/edc/fetch API for mid: {}, deviceId {}", SecurityUtils.getCurrentMerchant().getMid(),deviceId);
        return addressUpdateEdcService.getEdcAddress(deviceId, deploymentAddressStatus, lastTransactionAddress);
    }

}
