package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.MerchantProfileService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;

@RestController
public class AppSupportApi {

	private static final Logger LOGGER = LogManager.getLogger(AppSupportApi.class);

	@Autowired
	private MerchantProfileService merchantProfileService;

	@GetMapping("/api/v1/app/forceupdate")
	public ResponseUmp forceUpdateMerchants(@RequestParam String version, @RequestParam String source, @RequestParam String merchantType,
											HttpServletResponse httpServlrtResponse) throws Exception {
		String splitVersion = version;
		if(StringUtils.isNotBlank(version)) {
			String[] versionSplit = version.split("-");
			splitVersion = versionSplit[0];
		}
			
		return merchantProfileService.forceUpdateMerchants(splitVersion, source, merchantType, httpServlrtResponse);
	}

	@GetMapping("/name")
	public String getThreadName() {
		return Thread.currentThread().toString();
	}


}
