package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.MandateCallbackService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/upi/mandate")
public class MandateCallbackApi {

    private static final Logger LOGGER = LogManager.getLogger(MandateCallbackApi.class);

    @Autowired
    private MandateCallbackService mandateCallbackService;

    @PostMapping("/callback")
    public ResponseUmp handleMandateCallback(@RequestBody Map<String, Object> requestBody) throws Exception {
        LOGGER.info("Entering into handleMandateCallback");
        return mandateCallbackService.processMandateCallback(requestBody);
    }
} 