package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.NudgeApiService;


import com.paytm.aggregatorgateway.service.SettleApiService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
@RestController
@RequestMapping("/api/v1/merchant")
public class SettleApi {
    private static final Logger LOGGER = LogManager.getLogger(NudgeApi.class);

    @Autowired
    private SettleApiService settleApiService;

    @PostMapping("/settle")
    public ResponseUmp getSettleApi(@RequestBody Map<String, Object> requestBody) throws Exception {
        //LOGGER.info("Entering into getSettleApi");
        return settleApiService.getSettleApi(requestBody);
    }
}
