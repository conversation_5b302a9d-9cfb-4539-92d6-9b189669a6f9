package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.dto.Address;
import com.paytm.aggregatorgateway.dto.AddressUpdateDTO;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ResponseUmpException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.AddressUpdateService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/address")
public class AddressUpdateAPI {

    private final Logger logger = LogManager.getLogger(AddressUpdateAPI.class);

    @Autowired
    private AddressUpdateService addressUpdateService;

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseUmp addressUpdate(@RequestBody AddressUpdateDTO addressUpdateDTO) throws Exception
    {
        logger.info("Entered addressUpdate API");
        String ticketNo=addressUpdateDTO.getTicketNumber();
        if(StringUtils.isBlank(ticketNo))
        {
            throw new ResponseUmpException(UMPErrorCodeEnums.EMPTY_TICKET_NO);
        }
        Address address=addressUpdateDTO.getAddress();
        if(address==null || StringUtils.isBlank(address.getAddress_line_2()) || StringUtils.isBlank(address.getState())
                || StringUtils.isBlank(address.getCity()) || StringUtils.isBlank(address.getPostalCode()) || StringUtils.isBlank(address.getLatitude()) || StringUtils.isBlank(address.getLongitude()))
        {
            throw new ResponseUmpException(UMPErrorCodeEnums.EMPTY_ADDRESS);
        }
        return addressUpdateService.updateAddress(addressUpdateDTO);

    }


}
