package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.DevicesAPIService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v1/device")
public class DevicesAPI {

    @Autowired
    private DevicesAPIService devicesAPIService;

    @GetMapping(value = "/health/details")
    public Map<String, Object> getDeviceHealthDetails() throws Exception {
        String mid = SecurityUtils.getCurrentMerchant().getMid();
        return devicesAPIService.getDeviceHealthDetails(mid);
    }
}
