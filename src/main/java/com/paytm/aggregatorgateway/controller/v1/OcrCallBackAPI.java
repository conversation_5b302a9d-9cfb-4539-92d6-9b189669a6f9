package com.paytm.aggregatorgateway.controller.v1;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.dto.QualificationCallbackRequest;
import com.paytm.aggregatorgateway.service.OcrService;
import com.paytm.aggregatorgateway.utils.RestProcessorDelegate;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/ocr")
public class OcrCallBackAPI {

    @Value("${ump.base.url}")
    private String umpBaseUrl;

    private static final Logger LOGGER = LogManager.getLogger(OcrCallBackAPI.class);

    @Autowired
    private OcrService ocrService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RestProcessorDelegate restProcessorDelegate;

    @RequestMapping(value = "/bankProof/qualification/callBack", method = RequestMethod.POST)
    public ResponseUmp qualificationCallBack(@RequestBody Map<String, Object> request) {
        validateRequest();
        LOGGER.info("Received qualification callback request: {}", request);

        try {
            QualificationCallbackRequest qualificationRequest = objectMapper.convertValue(request, QualificationCallbackRequest.class);
            return ocrService.qualificationCallBack(qualificationRequest);
        } catch (Exception e) {
            LOGGER.error("Error processing qualification callback: ", e);
            return new ResponseUmp("FAILED", "500", "Internal server error", null);
        }
    }

    @RequestMapping(value = "/bankProof/deductions/callBack", method = RequestMethod.POST)
    public ResponseUmp deductionsCallBack(@RequestBody Map<String, Object> request) {
        validateRequest();
        LOGGER.info("Received deductions callback request: {}", request);

        try {
            return ocrService.deductionsCallBack(request);
        } catch (Exception e) {
            LOGGER.error("Error processing deductions callback: ", e);
            return new ResponseUmp("FAILED", "500", "Internal server error", null);
        }
    }

    @RequestMapping(value = "/test/umpRedis", method = RequestMethod.POST)
    public ResponseUmp testRedis(@RequestBody Map<String, Object> data) {
        String mid = SecurityUtils.getCurrentMerchant().getMid();
        String redisKey = mid + "BANK_PROOF_UPLOAD";
        try {
            String url = umpBaseUrl + "/api/v1/redis/setKey";

            Map<String, Object> request = new HashMap<>();
            // Create the request payload using Map for flexibility
            request.put("key", redisKey);
            request.put("value", data);

            HttpHeaders headers = generateUMPHeaders();

            // Make the API call using UMP method
            ResponseEntity<String> response = restProcessorDelegate.executeUMPRequestHystrix(url, "POST", null, headers, request, String.class);

            LOGGER.info("UMP Redis SetKey API response: {}", response.getBody());

            // Parse and log the response
            if (response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> responseMap = objectMapper.readValue(response.getBody(), Map.class);
                String status = (String) responseMap.get("status");
                String message = (String) responseMap.get("message");

                if ("SUCCESS".equals(status)) {
                    LOGGER.info("Successfully set bank OCR data in UMP Redis: {}", message);
                    return new ResponseUmp("SUCCESS", "200", "saved to redis", response);
                } else {
                    LOGGER.error("Failed to set bank OCR data in UMP Redis: {}", message);
                    return new ResponseUmp("FAILURE", "500", "NOT saved to redis", response);
                }
            }

        } catch (Exception e) {
            LOGGER.error("Error calling UMP Redis SetKey API: ", e);
        }
        return new ResponseUmp("FAILURE", "500", "NOT saved to redis", null);
    }

    private HttpHeaders generateUMPHeaders() { //TODO check headers
        HttpHeaders headers = new HttpHeaders();
        headers.add("x-auth-ump", "umpapp-3754-36d-aqr-cn7");
        return headers;
    }

    void validateRequest() {
        if(!SecurityUtils.hasPermission("OCR_CALLBACK")) {
            throw new AccessDeniedException("access denied");
        }
    }
}
