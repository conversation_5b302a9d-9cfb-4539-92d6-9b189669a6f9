package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.WidgetApiService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/widget")
public class WidgetApi {

	private static final Logger LOGGER = LogManager.getLogger(WidgetApi.class);

	@Autowired
	private WidgetApiService widgetApiService;

	@PostMapping("/update")
	public ResponseUmp updateWidget(@RequestBody Map<String, Object> requestBody) throws Exception {
		return widgetApiService.updateWidget(requestBody);
	}

}
