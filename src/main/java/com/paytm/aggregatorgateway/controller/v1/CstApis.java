package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.CstService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/api/v1/cst")
public class CstApis {
    @Autowired
    private CstService cstService;

    @GetMapping(value = "/trending/topics")
    public ResponseUmp getTrendingTopics(@RequestParam String language, @RequestParam String source, @RequestParam String verticalId, @RequestParam(required = false) String tag )throws Exception{
       // log.info("Entering into /trending/topics for mid {}", SecurityUtils.getCurrentMerchant().getMid());
        return cstService.getTrendingTopics(language,source,verticalId,tag);
    }
    @GetMapping(value="/viewticket")
    public ResponseUmp getTicketDetails(@RequestParam String ticketNumber)throws Exception{
       // log.info("Entering into /viewticket for mid {}", SecurityUtils.getCurrentMerchant().getMid());
        return cstService.getTicketDetails(ticketNumber);
    }
}
