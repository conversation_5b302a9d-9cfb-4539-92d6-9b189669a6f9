package com.paytm.aggregatorgateway.controller.v1;

import com.google.common.base.Preconditions;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import com.paytm.aggregatorgateway.utils.AesEncryption;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/")
public class EncryptDecryptAPI {

    @PostMapping("encrypt")
    public String encrypt(@RequestBody Map<String, String> request) throws Exception {
        validateRequest(request);
        return AesEncryption.encrypt(request.get("message"), AWSSecretManager.awsSecretsMap.get(AWSSecrets.AES_ENCRYPTION_KEY.getValue()));
    }

    @PostMapping("decrypt")
    public String decrypt(@RequestBody Map<String, String> request) throws Exception {
        validateRequest(request);
        return AesEncryption.decrypt(request.get("message"), AWSSecretManager.awsSecretsMap.get(AWSSecrets.AES_ENCRYPTION_KEY.getValue()));
    }

    void validateRequest(Map<String, String> request) {
        if(!SecurityUtils.hasPermission("ENCRYPT_DECRYPT")) {
            throw new AccessDeniedException("access denied");
        }
        Preconditions.checkNotNull(request.get("message"), "message cannot be null");
    }

}
