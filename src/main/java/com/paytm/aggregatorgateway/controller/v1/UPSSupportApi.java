package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateNFCStatus;
import com.paytm.aggregatorgateway.dto.ReqParamsToUpdateStatus;
import com.paytm.aggregatorgateway.service.UPSService;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/v1/app/ups")
public class UPSSupportApi {

	private static final Logger LOGGER = LogManager.getLogger(UPSSupportApi.class);

	@Autowired
	private UPSService upsService;

	@PostMapping("/update/nfc/status")
	public ResponseUmp updateNFCStatus(@RequestBody ReqParamsToUpdateNFCStatus reqParamsToUpdateNFCStatus)
			throws Exception {
		return upsService.updateNFCStatus(reqParamsToUpdateNFCStatus);
	}

	@PostMapping("/update/status")
	public ResponseUmp updateStatus(@RequestBody ReqParamsToUpdateStatus reqParamsToUpdateStatus)
			throws Exception {
		return upsService.updateStatus(reqParamsToUpdateStatus);
	}
}
