package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.PromoEngineService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.utils.ValidationUtil;
import com.paytm.aggregatorgateway.vo.PromoRequestVO;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import java.util.regex.Pattern;

import java.util.Map;

@RestController
@RequestMapping("/api/v1/promo")
public class PromoEngineApi {

	private static final Logger LOGGER = LogManager.getLogger(PromoEngineApi.class);

	@Autowired
	PromoEngineService promoEngineService;

	@RequestMapping(value = "/list/game", method = RequestMethod.GET)
	public String gameList(PromoRequestVO request) throws Exception {

		ValidationUtil.validateInteger(String.valueOf(request.getPage_number()));
		ValidationUtil.validateInteger(String.valueOf(request.getPage_size()));

		request.setMerchant_id(SecurityUtils.getCurrentMerchant().getMid());
		//LOGGER.info("Request received at GameList API for params :- request= {} ", request);
		return promoEngineService.getGameList(request);
	}

	@RequestMapping(value = "/detail/txn", method = RequestMethod.GET)
	public String txnDetail(PromoRequestVO request) throws Exception {
		ValidationUtil.validateInteger(String.valueOf(request.getPage_number()));
		ValidationUtil.validateInteger(String.valueOf(request.getPage_size()));
		ValidationUtil.validateInteger(String.valueOf(request.getGame_id()));

		request.setMerchant_id(SecurityUtils.getCurrentMerchant().getMid());
		//LOGGER.info("Request received at txnDetail API for params :- request= {} ", request);
		return promoEngineService.getTxnDetail(request);
	}

	@RequestMapping(value = "/offer", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public String allOffers(PromoRequestVO request) throws Exception {
		ValidationUtil.validateInteger(String.valueOf(request.getPage_number()));
		ValidationUtil.validateInteger(String.valueOf(request.getPage_size()));
		request.setMerchant_id(SecurityUtils.getCurrentMerchant().getMid());
		//LOGGER.info("Request received at AllOffers API for params :-" + " request {}", request);
		return promoEngineService.getAllOffers(request);
	}

	@RequestMapping(value = "/offer", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public String activateOffer(@RequestBody PromoRequestVO requestBody) throws Exception {
		ValidationUtil.validateInteger(String.valueOf(requestBody.getPage_number()));
		ValidationUtil.validateInteger(String.valueOf(requestBody.getPage_size()));
        ValidationUtil.validateInteger(String.valueOf(requestBody.getCampaign_id()));
		requestBody.setMerchant_id(SecurityUtils.getCurrentMerchant().getMid());
	//	LOGGER.info("Request received at ActiveOffer API for params :-" + " request= {}", requestBody);
		return promoEngineService.activateOffer(requestBody);
	}

	@RequestMapping(value = "/active/games", method = RequestMethod.POST)
	public Map<String, Object> activeGames(@RequestBody Map<String, Object> requestBody, @RequestParam(value = "page_size", required = false) String pageSize) throws Exception {
		LOGGER.info("Request received at ActiveGames API for mid = {}", SecurityUtils.getCurrentMerchant().getMid());
		return promoEngineService.fetchActiveGames(requestBody, pageSize, SecurityUtils.getCurrentMerchant().getMid());
	}
	@RequestMapping(value = "/active/offers", method = RequestMethod.GET)
	public ResponseUmp fetchActiveOffers(@RequestParam(value = "page_size", required = false) Integer page_size, @RequestParam(value = "page_number", required = false) Integer page_number, @RequestParam(value = "status", required = false) String status) throws Exception{
		LOGGER.info("fetching the active offers");
		return promoEngineService.fetchActiveOffers(page_size,page_number,status);
	}

	@PostMapping("/sf/pages")
	public ResponseUmp fetchPromoPages(
			@RequestParam(value = "sectionName", required = true) String sectionName,
			@RequestBody Map<String, String> requestBody) throws Exception {
		return promoEngineService.fetchPromoPages(sectionName, requestBody);
	}
	@GetMapping("/fetch/data")
	public ResponseUmp fetchCBOdata(
			@RequestParam(value = "sectionName", required = true) String sectionName) throws Exception {
		return promoEngineService.fetchCBOdata(sectionName);
	}
	
}
