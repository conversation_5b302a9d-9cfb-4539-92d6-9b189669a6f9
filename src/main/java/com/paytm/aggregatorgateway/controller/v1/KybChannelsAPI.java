package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.service.KybChannelsService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/kyb")
public class KybChannelsAPI {
	
    private final Logger LOGGER = LogManager.getLogger(KybChannelsAPI.class);
    
    @Autowired
    private KybChannelsService kybChannelsService;

	@GetMapping(value = "/fetch/channel")
    public ResponseUmp fetchChannel() throws Exception
    {
		LOGGER.info("Inside Fetch channel Api");
		String kybId = SecurityUtils.getCurrentMerchant().getKybid();
		return kybChannelsService.fetchChannels(kybId);
    }
}
