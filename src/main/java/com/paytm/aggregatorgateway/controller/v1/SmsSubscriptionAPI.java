package com.paytm.aggregatorgateway.controller.v1;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paytm.aggregatorgateway.constants.ErrorCodeConstants;
import com.paytm.aggregatorgateway.controller.v2.MerchantProfileV2API;
import com.paytm.aggregatorgateway.dto.SmsSubscriptionDTO;
import com.paytm.aggregatorgateway.dto.UpdateSubscriptionStatusDTO;
import com.paytm.aggregatorgateway.enums.UMPErrorCodeEnums;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.SmsSubscriptionService;
import com.paytm.aggregatorgateway.service.SubscriptionService;
import com.paytm.aggregatorgateway.utils.MappingUtils;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.utils.ValidationUtil;
import com.paytm.aggregatorgateway.utils.metrics.MetricConstants;
import com.paytm.aggregatorgateway.utils.metrics.MetricUtils;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import com.timgroup.statsd.StatsDClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/sms/subscription")
public class SmsSubscriptionAPI {
    private static final Logger LOGGER = LogManager.getLogger(SmsSubscriptionAPI.class);

    @Autowired
	MetricUtils metricUtils;
    
    @Autowired
    StatsDClient statsdClient;		
    
    @Value("${sms.subscription.segment.id}")
    private String segmentId;
    
    @Value("${sms.subscription.commission.value}")
    private String commissionValue;

    @Autowired
    private MerchantProfileV2API merchantProfileV2API;
    
    @Autowired
	ObjectMapper objectMapper;

    @Autowired
    private SmsSubscriptionService smsSubscriptionService;
    
    @Autowired
	private SubscriptionService subscriptionService;
    
    @GetMapping("/fetch")
    public String fetchSegmentId(@RequestParam String client, @RequestParam String platformVersion,
                                      @RequestParam(required = false) String siteId, @RequestParam(required = false) String childSiteId,
                                      @RequestParam(required = false) String appVersion, @RequestParam(required = false) String locale,
                                      @RequestParam(required = false) String langId) throws InterruptedException, IOException, Exception {

		if(StringUtils.isNotBlank(client) && ((!"ios".equalsIgnoreCase(client)) && (!"android".equalsIgnoreCase(client)))){
			LOGGER.info("client {} ",client);
			throw new ValidationException(UMPErrorCodeEnums.CUSTOM_API_EXCEPTION,"Invalid value passed for client key");
		}

        //LOGGER.info("Entering into fetchSegmentApi with client: {}, platformVersion: {}", client, platformVersion);
        String custId = SecurityUtils.getLoggedInUser().getId();
        String mid = SecurityUtils.getCurrentMerchant().getMid();
        // migration code from storefront to cleverTap
        String id = smsSubscriptionService.fetchIdFromCleverTap(client, custId, segmentId);
        
//        String id = smsSubscriptionService.fetchSegmentId(custId, client, platformVersion, siteId, childSiteId, appVersion, locale, langId);
        if ((StringUtils.isBlank(id) || !(id.equals(segmentId)))
        		&& !checkIfSubscriptionExists(mid)) {
            ResponseUmp responseUmp = new ResponseUmp();
            responseUmp.setStatus("FAILURE");
            responseUmp.setStatusMessage("Segment Id not found/invalid");
            return MappingUtils.convertObjectToJson(responseUmp);
        }
        // Note : we are not getting flat commission data from mdr right now so hardcoding response here for commission details
   //     String categoryResponse = merchantProfileV2API.scratchedMDRDetails("SMS_CHARGES_UO_DIY");
        //     LOGGER.info("Response from MDR: {}", categoryResponse);
        //  return categoryResponse;
        String response = "[{\"category_name\":\"SMS_CHARGES_UO_DIY\",\"realised_cat\":\"SMS_CHARGES_UO_DIY\",\"realised_cat_id\":235126,\"commission_type\":\"FLAT\",\"commission_value\":" + commissionValue + "}]";

        return response;
    }
    
	private boolean checkIfSubscriptionExists(String mid) throws Exception {
		boolean subscriptionExists = false;
		String subscriptionStatus = "ACTIVE,SUSPEND";
		Map<String, Object> fetchResponse = subscriptionService.fetchSubscription(mid, mid, "SMS_CHARGE", null,
				null, subscriptionStatus);
		if ((fetchResponse.get("statusCode").equals("200"))) {
			subscriptionExists = true;
		}
		LOGGER.info("returning from checkIfSubscriptionExists : " + subscriptionExists + " for mid: " + mid);
		return subscriptionExists;
	}

	@RequestMapping(value = "/subscribe", method = RequestMethod.POST)
	public String createSubscription(@RequestBody @Valid SmsSubscriptionDTO smsSubscriptionDTO) throws Exception
	{
		ValidationUtil.validateAlphaNumeric(smsSubscriptionDTO.getUsn());
		ValidationUtil.validateAlphanumericwithhyphen(smsSubscriptionDTO.getServiceName());
		ValidationUtil.validateAlphanumericwithhyphen(smsSubscriptionDTO.getSubscriptionType());

		LOGGER.info("Enter into createSubscriptionApi");
		smsSubscriptionDTO.setCustId(SecurityUtils.getLoggedInUser().getId());
		smsSubscriptionDTO.setMid(SecurityUtils.getCurrentMerchant().getMid());
		String mid = SecurityUtils.getCurrentMerchant().getMid();
		String custId = SecurityUtils.getLoggedInUser().getId();
		Boolean subscriptionUpdated = checkAndUpdateSubscription(mid,smsSubscriptionDTO.getServiceName(),smsSubscriptionDTO.getServiceType(),smsSubscriptionDTO.getUsn(),smsSubscriptionDTO.getSubscriptionType(),custId,smsSubscriptionDTO.getClient());
		LOGGER.info("subscriptionUpdated value {}", subscriptionUpdated);
		if(subscriptionUpdated)
		{
			String response = "{\n"
					+ "\"status\": \"Success\",\n"
					+ "\"statusCode\": \"201\"\n"
					+ "}";
			return response;
		}
		
		
   //     String id = smsSubscriptionService.fetchSegmentId(smsSubscriptionDTO.getCustId(),smsSubscriptionDTO.getClient() , smsSubscriptionDTO.getPlatformVersion(),smsSubscriptionDTO.getSiteId(), smsSubscriptionDTO.getChildSiteId(),smsSubscriptionDTO.getAppVersion(), smsSubscriptionDTO.getLocale(), smsSubscriptionDTO.getLangId());
		String clientName = smsSubscriptionDTO.getClient();
		  String id = smsSubscriptionService.fetchIdFromCleverTap(clientName, custId, segmentId);
        if (StringUtils.isBlank(id) || !(id.equals(segmentId))) {
        	LOGGER.info("pushing metrics for errorcode {} clientname {} mid {} custId {} segmentid {}" ,
        			ErrorCodeConstants.SMS_BUG_COMMISSION_NOT_PRESENT, clientName, mid, custId, segmentId);
        	statsdClient.incrementCounter(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/subscribe", "POST", ErrorCodeConstants.SMS_BUG_COMMISSION_NOT_PRESENT));
            ResponseUmp responseUmp = new ResponseUmp();
            responseUmp.setStatus("FAILURE");
            responseUmp.setStatusMessage("Segment Id not found/invalid");
            return MappingUtils.convertObjectToJson(responseUmp);
        }
  //      String categoryResponse = merchantProfileV2API.scratchedMDRDetails("SMS_CHARGES_UO_DIY");
  //      List<Map<String,Object>> categoryList = objectMapper.readValue(categoryResponse,new TypeReference<List<Map<String,Object>>>(){});
//		String commissionValue = null;
//        if(categoryList.get(0).containsKey("commission_value"))
//		     commissionValue = categoryList.get(0).get("commission_value").toString();
        LOGGER.info("commission value is {}",commissionValue);
        if(StringUtils.isBlank(commissionValue) || !(commissionValue.equals(smsSubscriptionDTO.getPlanPrice())))
        {
        	ResponseUmp responseUmp = new ResponseUmp();
            responseUmp.setStatus("FAILURE");
            responseUmp.setStatusMessage("Commission Value not found/invalid");
            return MappingUtils.convertObjectToJson(responseUmp);
        }
		String response = smsSubscriptionService.createSubscription(smsSubscriptionDTO);
		if(StringUtils.isBlank(response) 
				|| response.toLowerCase().contains("failure")) {
			LOGGER.info("sms subscription creation failed: response: " + response);
			metricUtils.pushCounterMetrics(MetricConstants.MetricsName.SMS_BUG, metricUtils.createSmsBugTags("/subscribe", "POST", ErrorCodeConstants.SMS_BUG_SUP_CREATE_FAILED));
		}
		return response;
	}
	
	
	
	private Boolean checkAndUpdateSubscription(String mid, String serviceName, String serviceType, String usn, String subscriptionType, String custId, String client) throws Exception {
		LOGGER.info("into checkAndUpdateSubscription method");
		String subscriptionStatus = "SUSPEND";
		Boolean updatedSubscription = false;
		Map<String,Object> fetchResponse = subscriptionService.fetchSubscription(mid,usn,serviceName,subscriptionType,serviceType,subscriptionStatus);
		if(fetchResponse.get("statusCode").equals("200"))
		{
			LOGGER.info("Incorrect api call, update subscription should've been called. Mid {} . Check for possible descrepancy that boss alerts are OFF" , mid);
			statsdClient.incrementCounter(MetricConstants.MetricsName.SMS_BUG,
										metricUtils.createSmsBugTags("/subscribe", "POST", 
										ErrorCodeConstants.SMS_BUG_INCORRECT_API_CALL));
			
			UpdateSubscriptionStatusDTO request = createUpdateData(mid,custId,usn,serviceName,subscriptionType,"RESUME");
			request.setInstantSmsEnable(true);
			String updateResponse = smsSubscriptionService.updateSubscriptionStatus(request,false,client);
			Map<String,Object> response = MappingUtils.convertJsonToMap(updateResponse);
			if((response.get("status")).equals("Success"))
				updatedSubscription = true;
		}
		return updatedSubscription;
	}
	
	private UpdateSubscriptionStatusDTO createUpdateData(String mid,String custId,String usn, String serviceName, String subscriptionType, String status)
	{
		UpdateSubscriptionStatusDTO updateSubscriptionStatusDTO = new UpdateSubscriptionStatusDTO();
		updateSubscriptionStatusDTO.setMid(mid);
		updateSubscriptionStatusDTO.setCustId(custId);
		updateSubscriptionStatusDTO.setUsn(usn);
		updateSubscriptionStatusDTO.setServiceName(serviceName);
		updateSubscriptionStatusDTO.setSubscriptionType(subscriptionType);
		updateSubscriptionStatusDTO.setStatus(status);
		return updateSubscriptionStatusDTO;
	}

	@RequestMapping(value = "/update/status", method = RequestMethod.POST)
	public String updateSubscriptionStatus(@RequestBody @Valid UpdateSubscriptionStatusDTO updateStatusDTO, HttpServletRequest request) throws Exception
	{
		ValidationUtil.validateAlphaNumeric(updateStatusDTO.getUsn());
        ValidationUtil.validateAlphanumericwithhyphen(updateStatusDTO.getServiceName());
		ValidationUtil.validateAlphanumericwithhyphen(updateStatusDTO.getSubscriptionType());
		LOGGER.info("Enter into updateSubscriptionStatus");
		updateStatusDTO.setMid(SecurityUtils.getCurrentMerchant().getMid());
		updateStatusDTO.setCustId(SecurityUtils.getLoggedInUser().getId());
		String appClient = request.getHeader("client");
		return smsSubscriptionService.updateSubscriptionStatus(updateStatusDTO,true,appClient);
	}
	

}
