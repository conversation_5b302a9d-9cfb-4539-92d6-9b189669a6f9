package com.paytm.aggregatorgateway.controller.v1;

import com.paytm.aggregatorgateway.dto.TicketInfoDTO;
import com.paytm.aggregatorgateway.exceptions.UMPIntegrationException;
import com.paytm.aggregatorgateway.exceptions.ValidationException;
import com.paytm.aggregatorgateway.service.SupportHomePageService;
import com.paytm.aggregatorgateway.utils.SecurityUtils;
import com.paytm.aggregatorgateway.utils.ValidationUtil;
import com.paytm.aggregatorgateway.vo.ResponseUmp;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

import static com.paytm.aggregatorgateway.service.impl.SupportHomePageServiceImpl.CAMPAIGN_NAME_CALLBACK_EDC;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/v1/support/homepage")
public class SupportHomePageApi
{

    @Autowired
    private SupportHomePageService supportHomePageService;

    /*
    * Api to fetch issue categories list
    * */
    @PostMapping(value = "/fetch/issue/category")
    public String fetchIssueCategory(@RequestBody Map<String,String> requestBody) throws Exception
    {
            return supportHomePageService.fetchIssueCategory(requestBody);
    }

    /*
    * Api to fetch list of all created tickets in below mentioned order.
    * 1.open ticket 2.open for feedback ticket 3.closed ticket
    * */
    @GetMapping(value = "/viewall/tickets")
    public Map<String,Object> getAllTickets() throws Exception
    {
        return supportHomePageService.getAllTickets(false);
    }

    /**
     * Api to fetch timeline of single most recent raised ticket.
     * In this only recent open ticket timeline if any otherwise recent valid for feedback ticket timeline will be returned.
     */
    @GetMapping(value = "/recent/ticket")
    Map<String,Object> getRecentTicket() throws Exception
    {
        return supportHomePageService.getRecentTicket();
    }

    /*
    * Api to fetch timeline of any ticket
    * */
    @PostMapping(value = "/ticket/timeline")
    String getTicketTimeLine(@RequestBody TicketInfoDTO ticketInfoDTO) throws Exception
    {
        return supportHomePageService.getTicketTimeLine(ticketInfoDTO);
    }

    /*
    * Api to reopen ticket on negative user feedback
    * */
    @RequestMapping(value = "/reopen/ticket",method = RequestMethod.POST)
    public String reopenClosedTicket(@RequestParam String ticketNumber, @RequestBody String description, @RequestParam(required = false) Boolean closeTicket) throws Exception
    {
        ValidationUtil.validateInteger(ticketNumber);
        if(StringUtils.isBlank(ticketNumber) || SecurityUtils.getCurrentMerchant()==null)
        {
            throw new ValidationException("500","Invalid Merchant/Ticket Number");
        }

        return supportHomePageService.reopenTicket(ticketNumber,description,SecurityUtils.getCurrentMerchant().getMid(),closeTicket);
    }

    /*
    * Api to fetch survey for negative feedback case
    * */
    @RequestMapping(value = "/survey",method = RequestMethod.GET)
    public String fetchSurvey() throws Exception
    {
        return supportHomePageService.getSurvey();
    }

    /*
     * Api to push feedback to hive
     * */
    @RequestMapping(value = "/feedback/upload",method = RequestMethod.POST)
    public Object uploadFeedback(@RequestBody Map<String,Object> request) throws Exception {

        return supportHomePageService.feedbackUpload(request);

    }
    /**
     * API to get call back details
     */
    @GetMapping(value = "/call/details")
    public ResponseUmp getCallDetails() throws IOException, InterruptedException, UMPIntegrationException {
        return supportHomePageService.getCallDetails();
    }

    /**
     * API to request call back
     */
    @PostMapping(value = "/request/callback")
    public ResponseUmp requestCallBack(@RequestParam(defaultValue = "false") Boolean edcRented,
                                       @RequestParam(defaultValue = "false") Boolean sbRented) throws Exception {
        return supportHomePageService.requestCallBack(edcRented, sbRented);
    }
}
