package com.paytm.aggregatorgateway.constants;

/**
 * The Interface PayTmPGConstants.
 */
public interface PayTmPGConstants {

	String X_CLIENT_ID = "x-client-id";
	String UID_EID_LOGGER = "UID_EID_LOGGER";

	String PROMO_BASE_PATH = "promo.engine.base.path";
	String PROMO_SECRET_KEY = "promo.engine.secret.key";
	String X_JWT_TOKEN = "x-jwt-token";
	String PROMO_CLIENT_ID = "ump";
	String ISSUER = "iss";
	String PROMO_ISSUER = "promo.engine.issuer";
	String BOSS_CLIENT_ID = "boss.client.id";
	String BOSS_CLIENT_KEY = "boss.client.key";
	String X_CLIENT_TOKEN = "x-client-token";
	String CLIENT_ID = "client-id";
	String SUBSCRIPTION_RENTAL_JWT_SECRET = "subscription.rental.jwt.secret";
	String CST_CLIENT = "P4B";
	String FAILURE = "FAILURE";
	String SSO_TOKEN = "sso_token";
	String CST_SERVICE_CLIENT_ID="cst.service.client.id";
	String JDBC_PASSWORD="jdbc.password";
    String JWT_CLIENT_ID = "jwt.client.id";
	String JWT_SECRET = "jwt.secret";
	String ADDRESS_UPDATE_PUSH_NOTIFICATION_TEMPLATE_NAME = "address.update.push.notification.template.name";
	String DEEPLINK_BASE_URL = "deeplink.base.url";
	String NOTIFICATION_PUSH_DEEPLINK_URL = "p4b/address-capture?ticket_number=";
	String ADDRESS_UPDATE_WIDGET_IDENTIFIER_KEY = "TICKET_ID";
	String ADDRESS_UPDATE_WIDGET_CARD_TYPE = "UPDATE_TICKET_ADDRESS";

	String GEOFENCING_WIDGET_CARD_TYPE = "CM_TRANSACTION_BLOCKED";

	String EDC_JWT_SECRET = "edc.jwt.secret";
	String CENTRAL_TOOL_KIT_CLIENT_ID = "central.toolkit.client.id";
	String CENTRAL_TOOL_KIT_SECRET = "central.toolkit.secret";
	String ActiveStatus="ACTIVE";
	String InActiveStatus="INACTIVE";

	String BottomSheet="BOTTOM_SHEET";
	String Card="CARD";
	String PNS_top="PNS_TOP";
	String UPDATE_TICKET_ADDRESS="UPDATE_TICKET_ADDRESS";
	String CST_CALL_SERVICE_CLIENT_ID = "cst.call.service.client.id";
    String CST_CLIENT_ID_V2 = "cst.client.id.v2";
    String FSM_CUST_ID = "fsm.cust.id";
	String FSM_CLIENT_ID = "fsm.client.id";
	String TRANSACTION_WISE_SETTLEMENT="Transaction Wise Settlement";
	String ALERT_MESSAGE = "alertMessage";
	String CM_OUT_OF_PRINTING_PAPER="CM_OUT_OF_PRINTING_PAPER";
	String CM_OUT_OF_BATTERY="CM_OUT_OF_BATTERY";
	String CM_OUT_OF_NETWORK="CM_OUT_OF_NETWORK";
	String CHARGE_LEVEL = "chargeLevel";
	String NEW_VERSION = "newVersion";

	String SB_OUT_OF_BATTERY_5 = "SB_OUT_OF_BATTERY_5";
	String SB_OUT_OF_BATTERY_10 = "SB_OUT_OF_BATTERY_10";
	String CM_BATTERY_10TO20 = "CM_BATTERY_10TO20";
	String CM_SIM_NETWORK_25 = "CM_SIM_NETWORK_25";
	String CM_WIFI_NETWORK_25 = "CM_WIFI_NETWORK_25";
	String CM_BATTERY_10TO15 = "CM_BATTERY_10TO15";
	String EDC_RESUME_JOURNEY = "EDC_RESUME_JOURNEY";
	String SB_BATTERY_10_AND_CHARGING = "SB_BATTERY_10_AND_CHARGING";
	String SB_BATTERY_5_AND_CHARGING = "SB_BATTERY_5_AND_CHARGING";
	String SB_CHARGER_CONNECTED_AND_CHARGING = "SB_CHARGER_CONNECTED_AND_CHARGING";
	String SB_CHARGER_CONNECTED_AND_NOT_CHARGING = "SB_CHARGER_CONNECTED_AND_NOT_CHARGING";
	String SB_CHARGER_DISCONNECTED = "SB_CHARGER_DISCONNECTED";
	String SB_CHARGER_DISCONNECTED_MULTIPLE = "SB_CHARGER_DISCONNECTED_MULTIPLE";
	String SB_MANUAL_SWITCH_OFF = "SB_MANUAL_SWITCH_OFF";
	String SB_MANUAL_SWITCH_ON = "SB_MANUAL_SWITCH_ON";
	String SB_LOW_BATTERY_20 = "SB_LOW_BATTERY_20";
	String SB_BATTERY_20_AND_CHARGING = "SB_BATTERY_20_AND_CHARGING";
	String ANDROID_CLIENT = "androidApp";
	String IOS_CLIENT = "iosapp";
	String PPSL_MID_FOUND = "PPSL-MID-FOUND";
}