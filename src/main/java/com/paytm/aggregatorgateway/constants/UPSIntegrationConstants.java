package com.paytm.aggregatorgateway.constants;

public class UPSIntegrationConstants {
    public static final String BASE_URL = "ups.base.url";
    public static final String CLIENT_ID = "ups.client.id";
    public static final String SECRET_KEY = "ups.secret.key";
    public static final String JWT_TOKEN = "jwt-token";
    public static final String CONTENT_TYPE = "content-Type";
    public static final String APPLICATION_JSON = "application/json";
    public static final String API_INTERNAL_ENTITY_PREFERENCE = "/ups/internal/v1/entity-preferences";
    public static final String SUCCESS = "SUCCESS";
    public static final String TIME_STAMP = "ts";
    public static final String TOKEN_CLIENT_ID = "clientId";
    public static final String REQUEST_ID = "requestId";
    public static final String UPS_REQUEST_HASH = "requestHash";
}
