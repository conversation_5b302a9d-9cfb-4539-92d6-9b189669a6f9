package com.paytm.aggregatorgateway.constants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public interface WarmUpConstants {

	public static final Set<String> WARMUP_APIS_GET = new HashSet<>(Arrays.asList("/api/v1/promo/offer",
			"/api/v1/promo/detail/txn?game_id=1",
			"/api/v2/promo/games",
			"/api/v2/promo/games/campaign-games/2",
			"/api/v2/promo/cashback",
			"/api/v2/merchantprofile/notification?type=ALL",
			"/api/v1/app/forceupdate?source=CAPP_ANDROID&merchantType=50K&version=10.20.0",
			"/api/v1/kyb/fetch/channel",
			"/api/v1/sms/subscription/fetch?platformVersion=2.2&client=android"));
	public static final Set<String> WARMUP_APIS_POST = new HashSet<>(Arrays.asList("/api/v2/promo/offer/5",
			"/api/v2/merchantprofile/notification",
			"/api/v1/app/ups/update/status",
			"/api/v1/app/ups/update/nfc/status"));
	public static final String LOCALHOST = "http://localhost:8080";
	public static final String DUMMY_CONTEXT = "{\"id\":\"1021587591\",\"username\":\"customercare\",\"firstName\":\"\",\"lastName\":\"\",\"acl\":{\"groups\":[]},\"status\":\"1\",\"type\":\"5\",\"email\":\"<EMAIL>\",\"isMerchant\":1,\"guid\":\"55ae4226-e023-4dc7-93c8-760dc96eb24f\",\"currentMerchant\":********,\"isEditable\":0,\"paytmSSOToken\":\"ff7dc7e2-6b72-4e3c-b38b-15a14d840400\",\"walletSSOToken\":\"ff7dc7e2-6b72-4e3c-b38b-15a14d840400\",\"updateTimestamp\":\"Aug12,201911:20:44AM\",\"currentLocale\":\"en-us\",\"isDemoUser\":false,\"merchants\":[{\"id\":********,\"name\":\"PVRCoimbatore\",\"guid\":\"89b18f1d-42ca-4b39-aaa2-bcb7e10203df\",\"roles\":[],\"aggregator\":false,\"mid\":\"PVRCoi95171299020475\",\"type\":\"OTHER\",\"isMerchant\":0,\"email\":\"<EMAIL>\",\"mobile\":\"**********\",\"migrated\":true,\"pgonly\":true,\"merchantType\":\"NonSD\",\"accountPrimary\":\"1\",\"walletOnly\":true,\"isActive\":true,\"pgpOnly\":false,\"betaAccess\":true,\"betaViewOnly\":true,\"createdOn\":\"2020-06-0317:40:59.0\",\"isSdMerchant\":false,\"adminUserId\":\"********\",\"isChild\":true,\"kybid\":\"A01p3zflevm2q515\",\"forceEnabled\":true,\"customSettlemntEnabled\":false,\"isReseller\":false,\"solutionType\":\"OFFLINE\",\"categoryLabel\":\"DashboardfortransactionsonQR\",\"bankEditAllowed\":true,\"isPosProvider\":false,\"isDelayedSettlement\":false,\"settlementType\":\"AutoIMPS\",\"inactiveState\":\"\",\"inactiveDate\":\"\",\"entityType\":\"PUBLIC_LIMITED\",\"nLevelHierarchyEnabled\":false,\"dummyAggregator\":false,\"isBwReconEnabled\":false,\"eRupiEnabled\":false,\"createdOnConverted\":{\"present\":true}}],\"countryCode\":\"\",\"primaryMerchant\":{\"present\":true}}";
	public static final String WARMUP_HEADER = "x-warm-up";
	public static final String WARMUP_REQUEST_BODY = "{\"transaction\":{\"smsAllowed\":false}}";
	public static final String WARMUP_RESPONSE_BODY = "{\"transaction\":{\"smsAllowed\":true},\"merchantName\":\"warmup\",\"adUnit_notifs\" : { \"override|paytm|315642\" : [ { \"type\" : \"custom-key-value\" , \"bg\" : \"#ffffff\" , \"custom_kv\" : { \"bannerId\" : \"889812\" , \"slotId\" : \"1\" , \"priority\" : \"100\"} , \"wzrk_info\" : { \"j\" : ********* , \"wzrk_id\" : \"1671791709_20230119\" , \"wzrk_pivot\" : \"Variant A\" , \"subContext\" : \"default\"} , \"subContext\" : \"default\"}]}, \"statusCode\":\"200\"}";
}
