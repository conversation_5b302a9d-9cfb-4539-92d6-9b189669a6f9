package com.paytm.aggregatorgateway.constants;

public class Queries {
    public static final String INSERT_WIDGET = "INSERT INTO  P4B_NUDGES(MID,CUSTID,EXPIRY_TIME,IDENTIFIER_KEY,IDENTIFIER_VALUE,TTL,STATUS,FEATURE_TYPE,TYPE,METADATA ) VALUES (?,?,?,?,?,?,?,?,?,?)";
    public static final String FETCH_WIDGET_DETAIL_BY_CUSTID = "SELECT * from P4B_NUDGES where MID=? and CUSTID=? and FEATURE_TYPE=? and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?) and STATUS=? and TYPE=?";
    public static final String FETCH_WIDGET_DETAIL = "SELECT * from P4B_NUDGES where MID=? and FEATURE_TYPE=? and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?) and STATUS=? and TYPE=?";
    public static final String UPDATE_WIDGET_FLAG_BY_CUSTID ="UPDATE P4B_NUDGES SET STATUS=? , EXPIRY_TIME=?, METADATA=? where MID=? and CUSTID=? and FEATURE_TYPE=? and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?) and STATUS=? and TYPE=?";
    public static final String UPDATE_WIDGET_FLAG = "UPDATE P4B_NUDGES SET STATUS=? , EXPIRY_TIME=?, METADATA=? where MID=? and FEATURE_TYPE=? and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?) and STATUS=? and TYPE=?";
    public static final String FETCH_CLIENT_SECRET="SELECT * FROM P4B_CLIENT_INFO WHERE CLIENT_ID=?";
    public static final String UPDATE_WIDGET_BY_IDENTIFIER_CUSTID = "UPDATE P4B_NUDGES SET STATUS=?, EXPIRY_TIME=? WHERE MID=? AND CUSTID=? AND IDENTIFIER_VALUE=? AND STATUS=?";
    public static final String UPDATE_WIDGET_BY_IDENTIFIER = "UPDATE P4B_NUDGES SET STATUS=?, EXPIRY_TIME=? WHERE MID=? AND IDENTIFIER_VALUE=? AND STATUS=?";
    public static final String CHECK_MID_WHITELISTED_FOR_CALL_BACK = "SELECT COUNT(*) FROM P4B_Campaigns WHERE MID = ? AND CAMPAIGN_ID = ?";
    public static final String FETCH_WIDGET_DETAIL_PAYMENT_HOLD= "SELECT * from P4B_NUDGES where MID=? and FEATURE_TYPE LIKE '%PAYMENT_HOLD%' and EXPIRY_TIME>CURRENT_TIMESTAMP";// and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?)
    public static final String UPDATE_PAYMENT_HOLD = "UPDATE P4B_NUDGES SET FEATURE_TYPE=?, METADATA=?  where MID=?  and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?)  and TYPE=? and EXPIRY_TIME>CURRENT_TIMESTAMP";

    public static final String UPDATE_PAYMENT_HOLD_EXPIRYTIME = "UPDATE P4B_NUDGES SET FEATURE_TYPE=? , EXPIRY_TIME=?, METADATA=? where MID=?  and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?)  and TYPE=? and EXPIRY_TIME>CURRENT_TIMESTAMP";
    public static final String UPDATE_MULTIPLE_CARD_TYPE = "UPDATE P4B_NUDGES SET STATUS=? , EXPIRY_TIME=? where MID=? and (IDENTIFIER_VALUE is null or IDENTIFIER_VALUE=?) and STATUS=? and TYPE=? and FEATURE_TYPE in (%s)";
    public static final String INSERT_CONSENT = "INSERT INTO P4B_CONSENT (CUSTID, MID, DEVICE_ID, preference, type, APP_VERSION) VALUES (?, ?, ?, ?, ?, ?)";
}
