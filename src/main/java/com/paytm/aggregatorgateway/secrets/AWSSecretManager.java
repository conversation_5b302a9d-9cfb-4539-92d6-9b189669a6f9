package com.paytm.aggregatorgateway.secrets;

import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import com.amazonaws.services.secretsmanager.model.*;
import com.google.gson.Gson;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import java.util.Map;

@Component("awsSecretManager")
public class AWSSecretManager {

    private static final Logger LOGGER = LogManager.getLogger(AWSSecretManager.class);

    @Value("${aws.secret.manager.path}")
    private String awsSecretManagerPath;

    @Value("${aws.region}")
    private String awsRegion;

    Gson gson = new Gson();

    public static Map<String, String> awsSecretsMap;

    @PostConstruct
    public void getSecret() {

        LOGGER.info("Fetching secret keys from AWS...");

        AWSSecretsManager client  = AWSSecretsManagerClientBuilder.standard()
                .withRegion(awsRegion)
                .build();

        String secretVal = "";
        GetSecretValueRequest getSecretValueRequest = new GetSecretValueRequest()
                .withSecretId(awsSecretManagerPath);
        GetSecretValueResult getSecretValueResult = null;

        try {
            getSecretValueResult = client.getSecretValue(getSecretValueRequest);
        } catch (DecryptionFailureException | InternalServiceErrorException | InvalidParameterException | InvalidRequestException | ResourceNotFoundException e) {
            LOGGER.error("Exception occurred while fetching secret keys from AWS: {}", e.getMessage());
            throw e;
        }

        if (getSecretValueResult.getSecretString() != null) {
            secretVal = getSecretValueResult.getSecretString();
        }

        awsSecretsMap = gson.fromJson(secretVal, Map.class);
        LOGGER.info("Successfully fetched {} secret keys from AWS.", awsSecretsMap.size());

    }

}
