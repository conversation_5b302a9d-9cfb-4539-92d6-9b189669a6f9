package com.paytm.aggregatorgateway.secrets;

public enum AWSSecrets {

    PROMO_ENGINE_SECRET_KEY("promo.engine.secret.key"),
    CLEVERTAP_ACCOUNTID("cleverTap.accountId"),
    CLEVERTAP_PASSCODE("cleverTap.passCode"),
    BOSS_CLIENT_KEY("boss.client.key"),
    BOSS_CLIENT_ID("boss.client.id"),
    SUBSCRIPTION_RENTAL_JWT_SECRET("subscription.rental.jwt.secret"),
    UPS_SECRET_KEY("ups.secret.key"),
    KYB_CLIENT_SECRET("kyb.client.secret"),
    PG_REWARD_SECRET_KEY("pg.reward.secret.key"),
    MSUPERCASH_JWT_SECRET("msupercash.jwt.secret"),
    UPS_CLIENT_ID("ups.client.id"), JDBC_PASSWORD("jdbc.password"), JWT_SECRET("jwt.secret"),
    NOTIFICATIONS_CLIENT_ID("notification.client.id"),
    NOTIFICATIONS_SECRET_KEY("notification.client.secret"),
    CST_SECRET_KEY("cst.secret.key"),
    CST_MGW_TOKEN("cst.mgw.token"),
    CST_CALL_SERVICE_MGW_TOKEN("cst.call.service.mgw.token"),
    CST_CALL_SERVICE_SECRET_KEY("cst.call.service.secret.key"),
    STORE_FRONT_SECRET("store.front.secret"),
    CENTRAL_TOOL_KIT_SECRET_KEY("central.toolkit.secret"),
    CST_SECRET_KEY_V2("cst.secret.key.v2"),
    FSM_CLIENT_SECRET("fsm.client.secret"),
    AES_ENCRYPTION_KEY("aes.encryption.key"),
    CHECKOUT_SECRET_KEY("checkout.secret.key"),
    CHECKOUT_CALLBACK_SECRET_KEY("checkout.callback.secret.key"),
    OCR_CLIENT_SECRET("ocr.client.secret");




    private String value;

    AWSSecrets(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
