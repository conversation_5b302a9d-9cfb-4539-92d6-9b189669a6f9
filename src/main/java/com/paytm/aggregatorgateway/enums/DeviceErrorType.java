package com.paytm.aggregatorgateway.enums;

import lombok.Getter;
import lombok.ToString;

@ToString
@Getter
public enum DeviceErrorType {
 //   SB_BATTERY_BRACKET_1("DEVICE_SB", "Soundbox", "SB_BATTERY_BRACKET_1_SUB_TITLE","Low Battery","https://staging-merchant.paytm.com/logo/Group%20133.png", "#FFEBEF", "#EB5757", "SB_BATTERY_BRACKET_1_CTA_TITLE", "View More","https://staging-merchant.paytm.com/logo/battery-red-new.png","SB_BATTERY_BRACKET_1_DESC_1", "Charge", "SB_BATTERY_BRACKET_1_DESC_2", "Soundbox Now","SB_BATTERY_BRACKET_1_SUB_DESC_1","Few Minutes","SB_BATTERY_BRACKET_1_SUB_DESC_2","Remaining"),
 //   SB_BATTERY_BRACKET_2("DEVICE_SB", "Soundbox", "SB_BATTERY_BRACKET_2_SUB_TITLE","Low Battery","https://staging-merchant.paytm.com/logo/Group%20133.png", "#FFF8E1", "#FF9D00", "SB_BATTERY_BRACKET_2_CTA_TITLE", "View More","https://staging-merchant.paytm.com/logo/battery-yellow-new.png","SB_BATTERY_BRACKET_2_DESC_1", "Charge", "SB_BATTERY_BRACKET_2_DESC_2", "Soundbox Now","SB_BATTERY_BRACKET_2_SUB_DESC_1","","SB_BATTERY_BRACKET_2_SUB_DESC_2","Remaining"),
 //   SB_NOT_REACHABLE("DEVICE_SB", "Soundbox", null,null,"https://staging-merchant.paytm.com/logo/Group%20133.png", "#FFEBEF", "#EB5757", "SB_NOT_REACHABLE_CTA_TITLE", "Check Now",  "https://staging-merchant.paytm.com/logo/sb-error.png", "SB_NOT_REACHABLE_DESC_1","Charge", "SB_NOT_REACHABLE_DESC_2", "Soundbox Now","SB_NOT_REACHABLE_SUB_DESC_1","Not","SB_NOT_REACHABLE_SUB_DESC_2","Reachable"),
    SB_DEEP_DISCHARGE("DEVICE_SB", "Soundbox", null,null,"https://staging-merchant.paytm.com/logo/Group%20133.png", "#FFEBEF", "#EB5757", "SB_DEEP_DISCHARGE_CTA_TITLE", "View More", "https://staging-merchant.paytm.com/logo/sb-error.png", "SB_DEEP_DISCHARGE_DESC_1", "Charge", "SB_DEEP_DISCHARGE_DESC_2", "Soundbox Now","SB_DEEP_DISCHARGE_SUB_DESC_1","Charge for","SB_DEEP_DISCHARGE_SUB_DESC_2","30 Minutes"),
    SB_MANUAL_OFF("DEVICE_SB", "Soundbox", null,null,"https://staging-merchant.paytm.com/logo/Group%20133.png", "#FFEBEF", "#EB5757", "SB_MANUAL_OFF_CTA_TITLE", "View More","https://staging-merchant.paytm.com/logo/sb-error.png","SB_MANUAL_OFF_DESC_1", "Charge", "SB_MANUAL_OFF_DESC_2", "Soundbox","SB_MANUAL_OFF_SUB_DESC_1","Listen to","SB_MANUAL_OFF_SUB_DESC_2","Payment Alerts"),
    SB_MANUAL_OFF_OPEN_TROUBLESHOOT("DEVICE_SB","Soundbox", null,null,"https://staging-merchant.paytm.com/logo/Group%20133.png","#FFEBEF","#EB5757", "SB_MANUAL_OFF_OPEN_TROUBLESHOOT_CTA_TITLE","Check Now","https://staging-merchant.paytm.com/logo/sb-error.png","SB_MANUAL_OFF_OPEN_TROUBLESHOOT_DESC_1", "Charge","SB_MANUAL_OFF_OPEN_TROUBLESHOOT_DESC_2","Issues ?","SB_MANUAL_OFF_OPEN_TROUBLESHOOT_SUB_DESC_1","Check","SB_MANUAL_OFF_OPEN_TROUBLESHOOT_SUB_DESC_2","Health");
 //   SB_CHARGER_CONNECTED("DEVICE_SB","Soundbox","SB_CHARGER_CONNECTED_SUB_TITLE","Low Battery","https://staging-merchant.paytm.com/logo/Group%20133.png", "#FFF8E1", "#FF9D00", "SB_CHARGER_CONNECTED_CTA_TITLE", "View More","https://staging-merchant.paytm.com/logo/battery-yellow-new.png", "SB_CHARGER_CONNECTED_DESC_1", "Keep Charging","SB_CHARGER_CONNECTED_DESC_2", "Soundbox","SB_CHARGER_CONNECTED_SUB_DESC_1","Battery","SB_CHARGER_CONNECTED_SUB_DESC_2","Below 10%"),

//    EDC_BATTERY_BRACKET_1("DEVICE_EDC","Card Machine","EDC_BATTERY_BRACKET_1_SUB_TITLE","Low Battery","https://staging-merchant.paytm.com/logo/Paytm%20Card%20machine.png","#FFEBEF","#EB5757","EDC_BATTERY_BRACKET_1_CTA_TITLE","View More","https://staging-merchant.paytm.com/logo/battery-red-new.png","EDC_BATTERY_BRACKET_1_DESC_1","Charge","EDC_BATTERY_BRACKET_1_DESC_2","Card Machine","EDC_BATTERY_BRACKET_1_SUB_DESC_1","Few Minutes","EDC_BATTERY_BRACKET_1_SUB_DESC_2","Remaining"),
//    EDC_BATTERY_BRACKET_2("DEVICE_EDC","Card Machine","EDC_BATTERY_BRACKET_2_SUB_TITLE","Low Battery","https://staging-merchant.paytm.com/logo/Paytm%20Card%20machine.png","#FFF8E1","#FF9D00","EDC_BATTERY_BRACKET_2_CTA_TITLE","View More","https://staging-merchant.paytm.com/logo/battery-yellow-new.png","EDC_BATTERY_BRACKET_2_DESC_1","Charge","EDC_BATTERY_BRACKET_2_DESC_2","Card Machine","EDC_BATTERY_BRACKET_2_SUB_DESC_1","Few Minutes","EDC_BATTERY_BRACKET_2_SUB_DESC_2","Remaining"),
//    EDC_NETWORK_SIM("DEVICE_EDC","Card Machine", null,null,"https://staging-merchant.paytm.com/logo/Paytm%20Card%20machine.png","#FFEBEF","#EB5757","EDC_NETWORK_SIM_CTA_TITLE","View More","https://staging-merchant.paytm.com/logo/edc-error.png","EDC_NETWORK_SIM_DESC_1","Charge","EDC_NETWORK_SIM_DESC_2","Issues","EDC_NETWORK_SIM_SUB_DESC_1","Check","EDC_NETWORK_SIM_SUB_DESC_2","Health"),
//    EDC_NETWORK_WIFI("DEVICE_EDC","Card Machine", null,null,"https://staging-merchant.paytm.com/logo/Paytm%20Card%20machine.png","#FFEBEF","#EB5757","EDC_NETWORK_WIFI_CTA_TITLE","View More","https://staging-merchant.paytm.com/logo/edc-error-signal.png","EDC_NETWORK_WIFI_DESC_1","Keep Charging","EDC_NETWORK_WIFI_DESC_2", "Soundbox","EDC_NETWORK_WIFI_SUB_DESC_1","Battery","EDC_NETWORK_WIFI_SUB_DESC_2","Below 10%");

    public final String deviceName;
    public final String deviceNameDefaultValue;
    public final String subtitle;
    public final String subtitleDefaultValue;
    public final String deviceIcon;
    public final String cardBackgroundColor;
    public final String cardBorderColor;
    public final String deviceCTATitle;
    public final String deviceCTATitleDefaultValue;
    public final String deviceCTADeeplink;
    public final String deviceErrorIcon;
    public final String deviceErrorDesc1;
    public final String deviceErrorDesc1DefaultValue;
    public final String deviceErrorDesc2;
    public final String deviceErrorDesc2DefaultValue;
    public final String deviceErrorSubDesc1;
    public final String deviceErrorSubDesc1DefaultValue;
    public final String deviceErrorSubDesc2;
    public final String deviceErrorSubDesc2DefaultValue;

    DeviceErrorType(
            String deviceName, String deviceNameDefaultValue, String subtitle, String subtitleDefaultValue, String deviceIcon,
            String cardBackgroundColor, String cardBorderColor, String deviceCTATitle,
            String deviceCTATitleDefaultValue,
            String deviceErrorIcon, String deviceErrorDesc1, String deviceErrorDesc1DefaultValue,
            String deviceErrorDesc2, String deviceErrorDesc2DefaultValue, String deviceErrorSubDesc1,
            String deviceErrorSubDesc1DefaultValue, String deviceErrorSubDesc2, String deviceErrorSubDesc2DefaultValue) {
        this.deviceName = deviceName;
        this.deviceNameDefaultValue = deviceNameDefaultValue;
        this.subtitle = subtitle;
        this.subtitleDefaultValue = subtitleDefaultValue;
        this.deviceIcon = deviceIcon;
        this.cardBackgroundColor = cardBackgroundColor;
        this.cardBorderColor = cardBorderColor;
        this.deviceCTATitle = deviceCTATitle;
        this.deviceCTATitleDefaultValue = deviceCTATitleDefaultValue;
        this.deviceCTADeeplink = "";
        this.deviceErrorIcon = deviceErrorIcon;
        this.deviceErrorDesc1 = deviceErrorDesc1;
        this.deviceErrorDesc1DefaultValue = deviceErrorDesc1DefaultValue;
        this.deviceErrorDesc2 = deviceErrorDesc2;
        this.deviceErrorDesc2DefaultValue = deviceErrorDesc2DefaultValue;
        this.deviceErrorSubDesc1 = deviceErrorSubDesc1;
        this.deviceErrorSubDesc1DefaultValue = deviceErrorSubDesc1DefaultValue;
        this.deviceErrorSubDesc2 = deviceErrorSubDesc2;
        this.deviceErrorSubDesc2DefaultValue = deviceErrorSubDesc2DefaultValue;
    }
}

