package com.paytm.aggregatorgateway.enums;

/**
 * ModuleEnum used for loading. 
 * oauth properties where we can decide to load oauth properties corresponding to module name
 */
public enum ModuleEnum {
	UMP_2("ump_2"), DEVELOPER("developer"), ICICI("icici"), MWEB("mweb");;

	private String moduleName;

	ModuleEnum(String moduleName) {
		this.moduleName = moduleName;
	}

	public String getModuleName() {
		return moduleName;
	}
}
