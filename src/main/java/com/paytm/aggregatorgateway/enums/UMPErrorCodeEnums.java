package com.paytm.aggregatorgateway.enums;

public enum UMPErrorCodeEnums {

	CUSTOM_API_EXCEPTION("UMP-604",""),
	PARSING_EXCEPTION("UMP-606","Parsing Error Occurred"),
	MISSING_PARAM("UMP-601","Request parameter is missing."),
	REWARD_FAILED_RESPONSE("UMP-907", " Rewards Faild  Response"),
	REWARD_INVALID_RESPONSE("UMP-908", " Rewards Invalid  Response"),
	KYB_INVALID_RESPONSE("UMP-908", " Kyb Failed  Response"),
	EMPTY_TICKET_NO("UMP-909","Empty Ticket Number"),
	EMPTY_ADDRESS("UMP-910","Empty Address"),
    ADDRESS_ALREADY_UPDATED("UMP-911","Address already updated"),
	CST_API_FAILED("UMP-912","Cst Api Failed"),
	INVALID_JWT_TOKEN("BFF-401", "Authentication Failed"),
	INVALID_PARAMS("BFF-402", "Mandatory params not passed");

	private String errorCode;
	private String errorMsg;

	UMPErrorCodeEnums(String errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}
}
