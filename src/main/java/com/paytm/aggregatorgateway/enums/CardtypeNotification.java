package com.paytm.aggregatorgateway.enums;

public enum CardtypeNotification {
    UPDATE_TICKET_ADDRESS("Address-Capture-P4B","p4b/address-capture?ticket_number=%s&src=p4b&channel=p4b"),
    CM_TRANSACTION_BLOCKED("Push Notification for DIY address update","p4b/update-address/%s?src=p4b&referrer=push_notification"),
    CM_OUT_OF_PRINTING_PAPER("edc-no-printing-paper",""),
    CM_OUT_OF_NETWORK("edc-no-network","p4b/my-devices/cardmachine/device/%s?src=p4b&channel=p4b"),
    CM_OUT_OF_BATTERY("edc-low-battery","p4b/my-devices/cardmachine/device/%s?src=p4b&channel=p4b"),
    CM_BATTERY_10TO20("EDC-battery-below-twenty", "p4b/my-devices/cardmachine/device/%s?src=p4b&channel=p4b"),
    SB_OUT_OF_BATTERY_5("out-of-battery-soundbox", "p4b/my-devices/soundbox/all?src=p4b&channel=p4b"),
    SB_OUT_OF_BATTERY_10("10%-Out-of-Battery-Soundbox", "p4b/my-devices/soundbox/all?src=p4b&channel=p4b"),
    LIMIT_UPGRADE("PN_Limit_upgrade_warning",""),
    LIMIT_UPGRADE_85("PN_Limit_upgrade_error_total",""),
    LIMIT_UPGRADE_INSTRUMENT_85("PN_Limit_upgrade_error_paymode",""),
    LIMIT_UPGRADE_WHATSAPP("wa_limit_upgrade_warnings",""),
    LIMIT_UPGRADE_85_WHATSAPP("wa_limit_upgrade_errors",""),
    LIMIT_UPGRADE_INSTRUMENT_85_WHATSAPP("wa_limit_upgrade_error_paymode",""),
    SB_CHARGER_DISCONNECTED_MULTIPLE("charger-issue-soundbox","p4b/my-devices/soundbox/all?src=p4b&channel=p4b"),
    SB_CHARGER_CONNECTED_AND_NOT_CHARGING("charger-issue-soundbox","p4b/my-devices/soundbox/all?src=p4b&channel=p4b"),
    SB_MANUAL_SWITCH_OFF("manual-switch-off-soundbox","p4b/my-devices/soundbox/all?src=p4b&channel=p4b"),
    SB_LOW_BATTERY_20("20%-out-of-battery-soundbox","p4b/my-devices/soundbox/all?src=p4b&channel=p4b");

    private String templateName;
    private String deeplink;

    CardtypeNotification(String templateName, String deeplink) {
        this.templateName = templateName;
        this.deeplink = deeplink;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }
}
