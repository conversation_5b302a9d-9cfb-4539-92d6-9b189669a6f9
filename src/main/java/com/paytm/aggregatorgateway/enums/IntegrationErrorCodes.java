package com.paytm.aggregatorgateway.enums;

public enum IntegrationErrorCodes {
    CREATE_TICKET_FAILURE("BFF-420", "Ticket creation on FD failed"),
    CREATE_BEAT_FAILURE("BFF-421", "Beat creation on FSM failed"),
    DONSTREAM_INVALID_RESPONSE("BFF-422", "Invalid Response from Downstream"),
    CST_TAG_FAILURE("BFF-423", "CST Tag Api Failed"),
    MERCHANT_INFO_DATA_MISSING("BFF-424", "Mid or phone no. is missing"),
    GET_TICKET_FAILURE("BFF-425", "Get tickets from FD failed"),
    UPDATE_TICKET_FAILURE("BFF-426", "Ticket update on FD failed"),
    IVR_CALL_ME_FAILURE("BFF-427", "IVR CallMe API on MGW failed"),
    IVR_CALL_DETAIL_FAILURE("BFF-428", "IVR GetCallDetail API on MGW failed"),
    UPS_ENTITY_PREFERENCES_FAILURE("BFF-429", "UPS Entity Preferences API failed");

    private String errorCode;
    private String errorMsg;

    IntegrationErrorCodes(String errorCode, String errorMsg){
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }
}
