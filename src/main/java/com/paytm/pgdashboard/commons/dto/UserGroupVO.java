package com.paytm.pgdashboard.commons.dto;

import java.util.List;

public class UserGroupVO {

	private Long groupId;
	private String groupName;
	private List<ViewVO> views;
	private String groupType;
	public UserGroupVO() {}
	
	public UserGroupVO(Long groupId, String groupName, List<ViewVO> screens,String type) {
		super();
		this.groupId = groupId;
		this.groupName = groupName;
		this.views = screens;
		this.groupType = type;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public List<ViewVO> getViews() {
		return views;
	}

	public void setViews(List<ViewVO> views) {
		this.views = views;
	}

	@Override
	public String toString() {
		return "UserGroupVO [groupId=" + groupId + ", groupName=" + groupName + ", views=" + views + "]";
	}

	public String getGroupType() {
		return groupType;
	}

	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}

	


	
	
}
