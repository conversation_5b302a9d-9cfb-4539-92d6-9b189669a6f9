package com.paytm.pgdashboard.commons.dto;


import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;


/**
 * 
 * <AUTHOR>
 *
 */
public class User implements Serializable,Cloneable {

	private static final long serialVersionUID = 1L;
	private String id;
	private String username;
	private String firstName;
	private String middleName;
	private String lastName;
	private Acl acl;
	private String status;
	private String type;
	private String email;
	private String mobile;
	private int isMerchant;
	private String uid;
	private String eid;
	private String guid;
	private Long currentMerchant;
	private int isEditable;
	private Long aggregatorId;
	private Boolean accountPrimary;
	private String paytmSSOToken;
	private String walletSSOToken;
	private String accessToken;
	private String updateTimestamp;
	private String fullName;
	private String currentLocale;
	private boolean isDemoUser=false;

	private List<Merchant> merchants = new ArrayList<Merchant>(0);
	private List<Merchant> ppslMerchants;
	// adding variable for quick edit
	private Boolean quickUpdate;
	private String phone;
	private String displayName;	
	private String countryCode;

	private String userToken;
	
	public User() {}

	public User(User user) {
		this.id = user.getId();
		this.username = user.getUsername();
		this.firstName = user.getFirstName();
		this.middleName = user.getMiddleName();
		this.lastName = user.getLastName();
		this.acl = user.getAcl();
		this.status = user.getStatus();
		this.type = user.getType();
		this.email = user.getEmail();
		this.mobile = user.getMobile();
		this.isMerchant = user.getIsMerchant();
		this.eid = user.getEid();
		this.uid = user.getUid();
		this.guid = user.getGuid();
		this.currentMerchant = user.getCurrentMerchant();
		this.isEditable = user.getIsEditable();
		this.aggregatorId = user.getAggregatorId();
		this.accountPrimary = user.getAccountPrimary();
		this.paytmSSOToken = user.getPaytmSSOToken();
		this.walletSSOToken = user.getWalletSSOToken();
		this.accessToken = user.getAccessToken();
		this.updateTimestamp = user.getUpdateTimestamp();
		this.fullName = user.getFullName();
		this.merchants = user.getMerchants();
		this.quickUpdate = user.getQuickUpdate();
		this.phone = user.getPhone();
		this.displayName = user.getDisplayName();		
		this.countryCode = user.getCountryCode();
		this.isDemoUser= user.getIsDemoUser();
		this.ppslMerchants = user.getPpslMerchants();

	}

	public List<Merchant> getPpslMerchants() {
		return ppslMerchants;
	}
	public void setPpslMerchants(List<Merchant> ppslMerchants) {
		this.ppslMerchants = ppslMerchants;
	}
	public String getUserToken() {
		return userToken;
	}

	public void setUserToken(String userToken) {
		this.userToken = userToken;
	}

	public Boolean getQuickUpdate() {
		return quickUpdate;
	}

	public void setQuickUpdate(Boolean quickUpdate) {
		this.quickUpdate = quickUpdate;
	}

	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}


	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Acl getAcl() {
		return acl;
	}

	public void setAcl(Acl acl) {
		this.acl = acl;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	/**
	 * This is not updated when context is updated, use CurrentMerchant.getIsMerchant()
	 * 
	 * @return
	 */
	public int getIsMerchant() {
		return isMerchant;
	}

	public void setIsMerchant(int isMerchant) {
		this.isMerchant = isMerchant;
	}

	public String getEid() {
		return eid;
	}

	public void setEid(String eid) {
		this.eid = eid;
	}

	public String getPaytmSSOToken() {
		return paytmSSOToken;
	}

	public void setPaytmSSOToken(String paytmSSOToken) {
		this.paytmSSOToken = paytmSSOToken;
	}

	public String getWalletSSOToken() {
		return walletSSOToken;
	}

	public void setWalletSSOToken(String walletSSOToken) {
		this.walletSSOToken = walletSSOToken;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}


	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getUpdateTimestamp() {
		return updateTimestamp;
	}

	public void setUpdateTimestamp(String updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}

	public List<Merchant> getMerchants() {
		return merchants;
	}

	public void setMerchants(List<Merchant> merchants) {
		this.merchants = merchants;
	}

	public Long getCurrentMerchant() {
		return currentMerchant;
	}

	public void setCurrentMerchant(Long currentMerchant) {
		this.currentMerchant = currentMerchant;
	}

	public int getIsEditable() {
		return isEditable;
	}

	public void setIsEditable(int isEditable) {
		this.isEditable = isEditable;
	}


	public Long getAggregatorId() {
		return aggregatorId;
	}

	public void setAggregatorId(Long aggregatorId) {
		this.aggregatorId = aggregatorId;
	}

	public Boolean getAccountPrimary() {
		return accountPrimary;
	}

	public void setAccountPrimary(Boolean accountPrimary) {
		this.accountPrimary = accountPrimary;
	}

	public void addMerchant(Merchant merchant) {
		merchants.add(merchant);
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	
	public String getCurrentLocale() {
		return currentLocale;
	}
	
	public void setCurrentLocale(String currentLocale) {
		this.currentLocale = currentLocale;
	}		
	
	public String getDisplayName() {
		return displayName;
	}
	
	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}
	
	public String getPhone() {
		return phone;
	}
	
	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public String getCountryCode() {
		return countryCode;
	}
	
	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	/*@Override
	public User clone() {
		User clone = null; 
		try{ 
			clone = (User) super.clone(); 
			List<Merchant> merchantList = new ArrayList<>(this.getMerchants().size());
	      
			Iterator<Merchant> iterator = this.getMerchants().iterator();
			while(iterator.hasNext()){
				merchantList.add(iterator.next().clone());
			}
			clone.merchants = merchantList;
			
			}catch(CloneNotSupportedException e){ 
				throw new RuntimeException(e);
		}
		return clone;
	}*/

	public Boolean getIsDemoUser() {
		return isDemoUser;
	}

	public void setIsDemoUser(Boolean isDemoUser) {
		this.isDemoUser = isDemoUser;
	}

	@Override
	public String toString() {
		return "User [id=" + id + ", username=" + username + ", firstName=" + firstName + ", middleName=" + middleName + ", lastName=" + lastName
				+ ", acl=" + acl + ", status=" + status + ", type=" + type + ", email=" + email + ", mobile=" + mobile + ", isMerchant=" + isMerchant
				+ ", eid=" + eid + ", guid=" + guid + ", currentMerchant=" + currentMerchant + ", isEditable=" + isEditable + ", aggregatorId="
				+ aggregatorId + ", accountPrimary=" + accountPrimary + ", paytmSSOToken=" + paytmSSOToken + ", walletSSOToken=" + walletSSOToken
				+ ", accessToken=" + accessToken + ", updateTimestamp=" + updateTimestamp + ", fullName=" + fullName + ", currentLocale=" + currentLocale
				+ ", isDemoUser=" + isDemoUser + ", merchants=" + merchants + ", quickUpdate=" + quickUpdate + ", phone=" + phone 
				+ ", displayName=" + displayName + ", countryCode=" + countryCode + ", ppslMerchants=" + ppslMerchants +"]";
	}
	
	public void setValuesFromNewKeys(String uid) {
		this.id = uid;
		this.mobile = this.phone;
		this.username = this.displayName;		
	}
	


	public Optional<Merchant> getPrimaryMerchant(){
	   	if(CollectionUtils.isNotEmpty(this.merchants)){
	   		return Optional.of(CollectionUtils.get(this.merchants,0));
		}
	   	else{
	   		return Optional.empty();
		}
	}
}
