package com.paytm.pgdashboard.commons.dto;

public class ViewVO {
	
	private Long viewId;
	private String viewName;
	private String action;
	private String[] requiredViews;
	private int isEnabled;
	private String type;
	private String subType;
	
	public ViewVO() {}

	public ViewVO(Long viewId, String viewName, String action, int isEnabled, String[] requiredViews, String type, String subType) {
		super();
		this.viewId = viewId;
		this.viewName = viewName;
		this.action = action;
		this.isEnabled = isEnabled;
		this.requiredViews = requiredViews;
		this.type = type;
		this.subType = subType;
	}

	public ViewVO(Long viewId, String viewName, String action, int isEnabled, String requiredViews, String type, String subType) {
		super();
		this.viewId = viewId;
		this.viewName = viewName;
		this.action = action;
		this.isEnabled = isEnabled;
		if (requiredViews != null && !requiredViews.isEmpty())
			this.requiredViews = requiredViews.split(",");
		this.type = type;
		this.subType = subType;
	}
	public String[] getRequiredViews() {
		return requiredViews;
	}

	public void setRequiredViews(String[] requiredViews) {
		this.requiredViews = requiredViews;
	}

	public Long getViewId() {
		return viewId;
	}

	public void setViewId(Long viewId) {
		this.viewId = viewId;
	}

	public String getViewName() {
		return viewName;
	}

	public void setViewName(String viewName) {
		this.viewName = viewName;
	}

	public String getAction() {
		return action;
	}
	public void setAction(String action) {
		this.action = action;
	}
	public int getIsEnabled() {
		return isEnabled;
	}
	public void setIsEnabled(int isEnabled) {
		this.isEnabled = isEnabled;
	}
	
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((action == null) ? 0 : action.hashCode());
		result = prime * result + isEnabled;
		result = prime * result + ((viewId == null) ? 0 : viewId.hashCode());
		result = prime * result + ((viewName == null) ? 0 : viewName.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ViewVO other = (ViewVO) obj;
		if (action == null) {
			if (other.action != null)
				return false;
		} else if (!action.equals(other.action))
			return false;
		if (isEnabled != other.isEnabled)
			return false;
		if (viewId == null) {
			if (other.viewId != null)
				return false;
		} else if (!viewId.equals(other.viewId))
			return false;
		if (viewName == null) {
			if (other.viewName != null)
				return false;
		} else if (!viewName.equals(other.viewName))
			return false;
		if (type == null) {
			if (other.type != null)
				return false;
		} else if (!type.equals(other.type))
			return false;
		
		return true;
	}


	public String getSubType() {
		return subType;
	}


	public void setSubType(String subType) {
		this.subType = subType;
	}


	public String getType() {
		return type;
	}


	public void setType(String type) {
		this.type = type;
	}

	
	

}
