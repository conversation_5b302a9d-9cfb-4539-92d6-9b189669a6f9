package com.paytm.pgdashboard.commons.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * @updated_by: Md Ekramul Ansari
 * @date: 17th November, 2015
 * @version: 4.1
 * @changes: Adding both constructors.
 */
public class Merchant implements Serializable,Cloneable {
	private static final long serialVersionUID = 8233935308285368067L;
	private long id;
	private String name;
	private String guid;
	private List<Role> roles = new ArrayList<Role>();
	private Set<String> permissions = new HashSet<>();
	private Boolean aggregator = false;
	private String mid;
	private String type;
	private int isMerchant;
	private String email;
	private String mobile;
	private Boolean migrated = false;
	private Boolean pgonly = false;
	private String merchantType;
	private String accountPrimary;
	private Boolean walletOnly;
	private Boolean isActive = true;
	private Boolean pgpOnly;
	private Boolean betaAccess;
	private Boolean betaViewOnly;
	private String createdOn;
	private UserGroupVO group;
	private List<Association> roleList;
	private String status;
	private Boolean isSdMerchant;
	private String hash;
	private String adminUserId;
	private Boolean isChild;
	private String kybid;
	private Boolean forceEnabled;
	private Boolean customSettlemntEnabled;
	private Boolean dummyForceEnabled;
	private Boolean isReseller = false;
	private String resellerType;
	private String resellerId;
	private String solutionType;
	private String obChannel;
	private String categoryLabel;
	private Boolean bankEditAllowed;
	private Boolean isPosProvider;
	private Boolean isDelayedSettlement;
	private String settlementType;
	private String inactiveState;
	private String inactiveDate;
	private String entityType;
	private Boolean nLevelHierarchyEnabled;
	private Boolean dummyAggregator;
	private Boolean isBwReconEnabled;
	private Boolean eRupiEnabled;
	private Boolean isPreAuthEnabled;
	private String storeCashCloneMid;

	private Boolean ppslMigrated;

	private Boolean ppslCandidate;


	// TODO: Should use builder pattern here
	public Merchant(long id, String name, String guid, boolean aggregator, String mid, String type, String email, String mobile, boolean migrated,
			boolean pgonly, String merchantType, boolean walletOnly, String accountPrimary, boolean isActive, Boolean pgpOnly, String createdOn,
			Boolean betaAccess, Boolean betaViewOnly, Boolean forceEnabled, Boolean isSdMerchant, String hash, String adminUserIds, Boolean isChild,
			String kybid, Boolean customSettlemntEnabled, Boolean isReseller, String resellerType, Boolean bankEditAllowed, String resellerId, String solutionType,
			String obChannel,String categoryLabel,Boolean isDelayedSettlement, String settlementType,String inactiveState,String inactiveDate,String entityType,
					Boolean isPreAuthEnabled, Boolean ppslCandidate, Boolean ppslMigrated){
		super();
		this.id = id;
		this.name = name;
		this.guid = guid;
		this.aggregator = aggregator;
		this.mid = mid;
		this.type = type;
		this.email = email;
		this.mobile = mobile;
		this.migrated = migrated;
		this.pgonly = pgonly;
		this.merchantType = merchantType;
		this.accountPrimary = accountPrimary;
		this.walletOnly = walletOnly;
		this.isActive = isActive;
		this.pgpOnly = pgpOnly;
		this.betaAccess = betaAccess;
		this.createdOn = createdOn;
		this.betaViewOnly=betaViewOnly;
		this.forceEnabled=forceEnabled;
		this.isSdMerchant=isSdMerchant;
		this.hash = hash;
		this.adminUserId = adminUserIds;
		this.isChild = isChild;
		this.kybid = kybid;
		this.customSettlemntEnabled=customSettlemntEnabled;
		this.isReseller = isReseller;
		this.resellerType = resellerType;
		this.bankEditAllowed = bankEditAllowed;
		this.solutionType = solutionType;
		this.obChannel = obChannel;
		this.categoryLabel = categoryLabel;
		this.isDelayedSettlement=isDelayedSettlement;
		this.settlementType = settlementType;
		this.inactiveState=inactiveState;
		this.inactiveDate=inactiveDate;
		this.entityType=entityType;
		this.isPreAuthEnabled = isPreAuthEnabled;
		this.ppslCandidate = ppslCandidate;
		this.ppslMigrated = ppslMigrated;
	}

	public Merchant(long id, String name, String guid, boolean aggregator, String mid, String type, String email, String mobile, boolean migrated,
					boolean pgonly, String merchantType, boolean walletOnly, String accountPrimary, boolean isActive, Boolean pgpOnly, String createdOn,
					Boolean betaAccess, Boolean betaViewOnly, Boolean forceEnabled, Boolean isSdMerchant, String hash, String adminUserIds, Boolean isChild,
			String kybid, Boolean customSettlemntEnabled, Boolean isReseller, String resellerType,
			Boolean bankEditAllowed, String resellerId, Boolean isPosProvider, String solutionType,
			String obChannel, String categoryLabel, Boolean isDelayedSettlement, String settlementType,
			String inactiveState, String inactiveDate, String entityType, Boolean nLevelHierarchyEnabled,
			Boolean dummyAggregator, Boolean isBwReconEnabled, Boolean eRupiEnabled, Boolean isPreAuthEnabled,
			Boolean ppslCandidate, Boolean ppslMigrated) {

		super();
		this.id = id;
		this.name = name;
		this.guid = guid;
		this.aggregator = aggregator;
		this.mid = mid;
		this.type = type;
		this.email = email;
		this.mobile = mobile;
		this.migrated = migrated;
		this.pgonly = pgonly;
		this.merchantType = merchantType;
		this.accountPrimary = accountPrimary;
		this.walletOnly = walletOnly;
		this.isActive = isActive;
		this.pgpOnly = pgpOnly;
		this.betaAccess = betaAccess;
		this.createdOn = createdOn;
		this.betaViewOnly=betaViewOnly;
		this.forceEnabled=forceEnabled;
		this.isSdMerchant=isSdMerchant;
		this.hash = hash;
		this.adminUserId = adminUserIds;
		this.isChild = isChild;
		this.kybid = kybid;
		this.customSettlemntEnabled=customSettlemntEnabled;
		this.isReseller = isReseller;
		this.resellerType = resellerType;
		this.bankEditAllowed = bankEditAllowed;
		this.isPosProvider= isPosProvider;
		this.solutionType = solutionType;
		this.obChannel = obChannel;
		this.categoryLabel = categoryLabel;
		this.isDelayedSettlement=isDelayedSettlement;
		this.settlementType = settlementType;
		this.inactiveState=inactiveState;
		this.inactiveDate=inactiveDate;
		this.entityType=entityType;
		this.isBwReconEnabled = isBwReconEnabled;
		this.resellerId=resellerId;
		this.nLevelHierarchyEnabled = nLevelHierarchyEnabled;
		this.dummyAggregator = dummyAggregator;
		this.eRupiEnabled = eRupiEnabled;
		this.isPreAuthEnabled = isPreAuthEnabled;
		this.ppslCandidate = ppslCandidate;
		this.ppslMigrated = ppslMigrated;
	}

	public Merchant(Merchant merchant) {
		this.id = merchant.getId();
		this.name = merchant.getName();
		this.guid = merchant.getGuid();
		this.aggregator = merchant.getAggregator();
		this.mid = merchant.getMid();
		this.type = merchant.getType();
		this.email = merchant.getEmail();
		this.mobile = merchant.getMobile();
		this.migrated = merchant.isMigrated();
		this.pgonly = merchant.pgonly;
		this.merchantType = merchant.getMerchantType();
		this.accountPrimary = merchant.getAccountPrimary();
		this.walletOnly = merchant.isWalletOnly();
		this.isActive = merchant.getIsActive();
		this.pgpOnly=merchant.getPgpOnly();
		this.betaAccess = merchant.isBetaAccess();
		this.createdOn = merchant.getCreatedOn();
		this.betaViewOnly=merchant.isBetaViewOnly();
		this.forceEnabled = merchant.getForceEnabled();
		this.isSdMerchant=merchant.getIsSdMerchant();
		this.isChild=merchant.getIsChild();
		this.kybid = merchant.getKybid();
		this.customSettlemntEnabled = merchant.getCustomSettlemntEnabled();
		this.isReseller = merchant.isReseller;
		this.resellerType = merchant.resellerType;
		this.bankEditAllowed = merchant.isBankEditAllowed();
		this.isPosProvider = merchant.getIsPosProvider();
		this.solutionType = merchant.getSolutionType();
		this.obChannel = merchant.getObChannel();
		this.categoryLabel = merchant.getCategoryLabel();
		this.isDelayedSettlement=merchant.getIsDelayedSettlement();
		this.settlementType = merchant.getSettlementType();
		this.inactiveState=merchant.getInactiveState();
		this.inactiveDate=merchant.getInactiveDate();
		this.entityType=merchant.getEntityType();
		this.adminUserId=merchant.getAdminUserId();
		this.isBwReconEnabled = merchant.getIsBwReconEnabled();
		this.resellerId=merchant.getResellerId();
		this.nLevelHierarchyEnabled = merchant.getnLevelHierarchyEnabled();
		this.dummyAggregator = merchant.getDummyAggregator();
		this.eRupiEnabled = merchant.geteRupiEnabled();
		this.isPreAuthEnabled = merchant.getIsPreAuthEnabled();
		this.ppslMigrated = merchant.getPpslMigrated();
		this.ppslCandidate = merchant.getPpslCandidate();
	}

	public String getStoreCashCloneMid() {
		return storeCashCloneMid;
	}

	public void setStoreCashCloneMid(String storeCashCloneMid) {
		this.storeCashCloneMid = storeCashCloneMid;
	}

	public Boolean getPpslMigrated() {
		return ppslMigrated;
	}

	public void setPpslMigrated(Boolean ppslMigrated) {
		this.ppslMigrated = ppslMigrated;
	}

	public Boolean getPpslCandidate() {
		return ppslCandidate;
	}

	public void setPpslCandidate(Boolean ppslCandidate) {
		this.ppslCandidate = ppslCandidate;
	}

	public String getResellerId() {
		return resellerId;
	}

	public void setResellerId(String resellerId) {
		this.resellerId = resellerId;
	}

	public Boolean getIsReseller() {
		return isReseller;
	}

	public void setIsReseller(Boolean isReseller) {
		this.isReseller = isReseller;
	}

	public String getResellerType() {
		return resellerType;
	}

	public void setResellerType(String resellerType) {
		this.resellerType = resellerType;
	}

	public Merchant() {}

	public Merchant(long id, String name) {
		this.id = id;
		this.name = name;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<Role> getRoles() {
		return roles;
	}

	public void setRoles(List<Role> roles) {
		this.roles = roles;
	}


	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}

	public Boolean getAggregator() {
		return aggregator;
	}

	public void setAggregator(Boolean aggregator) {
		this.aggregator = aggregator;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}


	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public int getIsMerchant() {
		return isMerchant;
	}

	public void setIsMerchant(int isMerchant) {
		this.isMerchant = isMerchant;
	}


	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Boolean isMigrated() {
		return migrated;
	}

	public void setMigrated(Boolean migrated) {
		this.migrated = migrated;
	}

	public Boolean isPgonly() {
		return pgonly;
	}

	public void setPgonly(Boolean pgonly) {
		this.pgonly = pgonly;
	}

	public String getMerchantType() {
		return merchantType;
	}

	public void setMerchantType(String merchantType) {
		this.merchantType = merchantType;
	}

	public String getAccountPrimary() {
		return accountPrimary;
	}

	public void setAccountPrimary(String accountPrimary) {
		this.accountPrimary = accountPrimary;
	}

	public Boolean isWalletOnly() {
		return walletOnly;
	}

	public void setWalletOnly(Boolean isWalletOnly) {
		this.walletOnly = isWalletOnly;
	}

	public Boolean getIsActive() {
		return isActive;
	}

	public void setIsActive(Boolean isActive) {
		this.isActive = isActive;
	}



	public Boolean getPgpOnly() {
		return pgpOnly;
	}

	public void setPgpOnly(Boolean pgpOnly) {
		this.pgpOnly = pgpOnly;
	}
	
	public String getCreatedOn() {
		return createdOn;
	}
	
	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}
	
	public Boolean isBetaAccess() {
		return betaAccess;
	}

	public void setBetaAccess(Boolean betaAccess) {
		this.betaAccess = betaAccess;
	}

	public Boolean isBetaViewOnly() {
		return betaViewOnly;
	}

	public void setBetaViewOnly(Boolean betaViewOnly) {
		this.betaViewOnly = betaViewOnly;
	}
	
	public Boolean getIsSdMerchant() {
		return isSdMerchant;
	}
	
	public void setIsSdMerchant(Boolean isSdMerchant) {
		this.isSdMerchant = isSdMerchant;
	}
	
	public String getHash() {
		return hash;
	}
	
	public void setHash(String hash) {
		this.hash = hash;
	}
	
	public String getAdminUserId() {
		return adminUserId;
	}
	
	public void setAdminUserId(String adminUserIds) {
		this.adminUserId = adminUserIds;
	}

	public UserGroupVO getGroup() {
		return group;
	}

	public void setGroup(UserGroupVO group) {
		this.group = group;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	public Boolean getIsChild() {
		return isChild;
	}
	
	public void setIsChild(Boolean isChild) {
		this.isChild = isChild;
	}
	
	public String getKybid() {
		return kybid;
	}
	
	public void setKybid(String kybid) {
		this.kybid = kybid;
	}
	
	
	/*@Override
	protected Merchant clone() {
		Merchant clone = null; 
		try{ 
			clone = (Merchant) super.clone(); 
			List<Role> roleList = new ArrayList<>(this.getRoles().size());
	      
			Iterator<Role> iterator = this.getRoles().iterator();
			while(iterator.hasNext()){
				roleList.add(iterator.next().clone());
			}
			clone.roles = roleList;
			
			}catch(CloneNotSupportedException e){ 
				throw new RuntimeException(e);
		}
		return clone;
	}*/

	@JsonIgnore
	public Set<String> getPermissions() {
		return permissions;
	}

	public void setPermissions(Set<String> permissions) {
		this.permissions = permissions;
	}

	public Boolean getForceEnabled() {
		return forceEnabled;
	}

	public void setForceEnabled(Boolean forceEnabled) {
		this.forceEnabled = forceEnabled;
	}

	public List<Association> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<Association> roleList) {
		this.roleList = roleList;
	}
	
	public Boolean getCustomSettlemntEnabled() {
		return customSettlemntEnabled;
	}

	public void setCustomSettlemntEnabled(Boolean customSettlemntEnabled) {
		this.customSettlemntEnabled = customSettlemntEnabled;
	}
	
	public Boolean getDummyForceEnabled() {
		return dummyForceEnabled;
	}
	
	public void setDummyForceEnabled(Boolean dummyForceEnabled) {
		this.dummyForceEnabled = dummyForceEnabled;
	}
	
	public String getSolutionType() {
		return solutionType;
	}

	public void setSolutionType(String solutionType) {
		this.solutionType = solutionType;
	}
	
	public String getObChannel() {
		return obChannel;
	}

	public void setObChannel(String obChannel) {
		this.obChannel = obChannel;
	}
	
	public String getCategoryLabel() {
		return categoryLabel;
	}

	public void setCategoryLabel(String categoryLabel) {
		this.categoryLabel = categoryLabel;
	}
	public Boolean isBankEditAllowed() {
		return bankEditAllowed;
	}

	public void setBankEditAllowed(Boolean bankEditAllowed) {
		this.bankEditAllowed = bankEditAllowed;
	}

	public Boolean getIsPosProvider() {
		return isPosProvider;
	}

	public void setPosProvider(Boolean isPosProvider) {
		this.isPosProvider = isPosProvider;
	}

	public String getSettlementType() {
		return settlementType;
	}

	public void setSettlementType(String settlementType) {
		this.settlementType = settlementType;
	}


	@Override
	public String toString() {
		return "Merchant [id=" + id + ", name=" + name + ", guid=" + guid + ", roles=" + roles + ", permissions="
				+ permissions + ", aggregator=" + aggregator + ", mid=" + mid + ", type=" + type + ", isMerchant="
				+ isMerchant + ", email=" + email + ", mobile=" + mobile + ", migrated=" + migrated + ", pgonly="
				+ pgonly + ", merchantType=" + merchantType + ", accountPrimary=" + accountPrimary + ", walletOnly="
				+ walletOnly + ", isActive=" + isActive + ", pgpOnly=" + pgpOnly + ", betaAccess=" + betaAccess
				+ ", betaViewOnly=" + betaViewOnly + ", createdOn=" + createdOn + ", group=" + group + ", roleList="
				+ roleList + ", status=" + status + ", isSdMerchant=" + isSdMerchant + ", hash=" + hash
				+ ", adminUserId=" + adminUserId + ", isChild=" + isChild + ", kybid=" + kybid + ", forceEnabled="
				+ forceEnabled + ", customSettlemntEnabled=" + customSettlemntEnabled + ", dummyForceEnabled="
				+ dummyForceEnabled + ", isReseller=" + isReseller + ", resellerType=" + resellerType
				+ ", solutionType=" + solutionType + ", obChannel=" + obChannel + ", categoryLabel=" + categoryLabel
				+ ", bankEditAllowed=" + bankEditAllowed + ", isPosProvider=" + isPosProvider + ", isDelayedSettlement="
				+ isDelayedSettlement + ", settlementType=" + settlementType + ", inactiveState=" + inactiveState
				+ ", inactiveDate=" + inactiveDate + ", entityType=" + entityType + ", nLevelHierarchyEnabled="
				+ nLevelHierarchyEnabled + ", dummyAggregator" + dummyAggregator + ", isBwReconEnabled="
				+ isBwReconEnabled + ", eRupiEnabled=" + eRupiEnabled + ", isPreAuthEnabled=" + isPreAuthEnabled +"]";
	}

	public Optional<LocalDateTime> getCreatedOnConverted() {
		if(StringUtils.isBlank(createdOn))
			return Optional.ofNullable(null);
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
			return Optional.ofNullable(LocalDateTime.parse(createdOn, formatter));
		} catch (DateTimeParseException e) {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
			return Optional.ofNullable(LocalDateTime.parse(createdOn, formatter));
		}
 
	}


	public Boolean getIsDelayedSettlement() {
		return isDelayedSettlement;
	}

	public void setIsDelayedSettlement(Boolean isDelayedSettlement) {
		this.isDelayedSettlement = isDelayedSettlement;
	}

	public String getInactiveState() {
		return inactiveState;
	}

	public void setInactiveState(String inactiveState) {
		this.inactiveState = inactiveState;
	}

	public String getInactiveDate() {
		return inactiveDate;
	}

	public void setInactiveDate(String inactiveDate) {
		this.inactiveDate = inactiveDate;
	}

	public String getEntityType() {
		return entityType;
	}

	public void setEntityType(String entityType) {
		this.entityType = entityType;
	}

	public Boolean getnLevelHierarchyEnabled() {
		return nLevelHierarchyEnabled;
	}

	public void setnLevelHierarchyEnabled(Boolean nLevelHierarchyEnabled) {
		this.nLevelHierarchyEnabled = nLevelHierarchyEnabled;
	}

	public Boolean getDummyAggregator() {
		return dummyAggregator;
	}

	public void setDummyAggregator(Boolean dummyAggregator) {
		this.dummyAggregator = dummyAggregator;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Boolean getMigrated() {
		return migrated;
	}

	public Boolean getPgonly() {
		return pgonly;
	}

	public Boolean getWalletOnly() {
		return walletOnly;
	}

	public Boolean getBetaAccess() {
		return betaAccess;
	}

	public Boolean getBetaViewOnly() {
		return betaViewOnly;
	}

	public Boolean getBankEditAllowed() {
		return bankEditAllowed;
	}

	public Boolean getIsBwReconEnabled() {
		return isBwReconEnabled;
	}

	public void setIsBwReconEnabled(Boolean isBwReconEnabled) {
		this.isBwReconEnabled = isBwReconEnabled;
	}

	public Boolean geteRupiEnabled() {
		return eRupiEnabled;
	}

	public void seteRupiEnabled(Boolean eRupiEnabled) {
		this.eRupiEnabled = eRupiEnabled;
	}
	public Boolean getIsPreAuthEnabled() {
		return isPreAuthEnabled;
	}
	public void setIsPreAuthEnabled(Boolean isPreAuthEnabled) {
		this.isPreAuthEnabled = isPreAuthEnabled;
	}

}
