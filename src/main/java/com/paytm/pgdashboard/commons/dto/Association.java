package com.paytm.pgdashboard.commons.dto;

public class Association {
	
	private String typeId;
	private String typeName;
	private UserGroupVO group;
	
	public Association() {
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public UserGroupVO getGroup() {
		return group;
	}
	public void setGroup(UserGroupVO group) {
		this.group = group;
	}
	public String getTypeId() {
		return typeId;
	}
	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}
	@Override
	public String toString() {
		return "Association [typeId=" + typeId + ", typeName=" + typeName +  ", group=" + group + "]";
	}
	

}
