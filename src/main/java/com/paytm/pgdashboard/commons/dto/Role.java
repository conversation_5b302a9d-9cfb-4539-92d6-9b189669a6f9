package com.paytm.pgdashboard.commons.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Role implements Serializable,Cloneable {

	private static final long serialVersionUID = 1L;
	private String role;
	private List<String> permissions = new ArrayList<String>();

	public Role() {}
	public Role(String role) {
		this.role = role;
	}
	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public List<String> getPermissions() {
		return permissions;
	}

	public void setPermissions(List<String> permissions) {
		this.permissions = permissions;
	}

	@Override
	public String toString() {
		return "Role [role=" + role + ", permissions=" + permissions + "]";
	}

	
	/*@Override
	protected Role clone() {
		Role clone = null;
		try{
			clone = (Role) super.clone(); 
			clone.role = this.role;
			clone.setPermissions(new ArrayList<>(this.permissions));
		}catch(CloneNotSupportedException e){ 
				throw new RuntimeException(e);
		}
		return clone;
	}*/
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((role == null) ? 0 : role.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Role other = (Role) obj;
		if (role == null) {
			if (other.role != null)
				return false;
		} else if (!role.equals(other.role))
			return false;
		return true;
	}

	

}
