package com.paytm.dashboard.security;
import com.paytm.aggregatorgateway.constants.PayTmPGConstants;
import com.paytm.aggregatorgateway.secrets.AWSSecretManager;
import com.paytm.aggregatorgateway.secrets.AWSSecrets;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

@Configuration
public class DatabaseConfig {

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.master")
    public DataSource dataSource(){
       return DataSourceBuilder.create().build();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.slave")
    public DataSource dataSourceSlave(){
        return DataSourceBuilder.create().build();
    }

    @Bean
    public JdbcTemplate masterJdbcTemplate(@Qualifier("dataSource") DataSource dsMaster) {
        return new JdbcTemplate(dsMaster);
    }
    @Bean
    public JdbcTemplate slaveJdbcTemplate(@Qualifier("dataSourceSlave") DataSource dsSlave) {
        return new JdbcTemplate(dsSlave);
    }

}
