package com.paytm.dashboard.security;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/*public class ReplicationRoutingDataSource extends AbstractRoutingDataSource {

   *//* @Override
    protected Object determineCurrentLookupKey() {
        String dataSourceType = TransactionSynchronizationManager.isCurrentTransactionReadOnly() ? "read" : "write";
       // log.info("current dataSourceType : {}", dataSourceType);
        return dataSourceType;
    }*//*
}*/
