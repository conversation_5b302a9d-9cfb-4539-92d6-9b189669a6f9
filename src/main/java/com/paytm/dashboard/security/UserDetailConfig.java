package com.paytm.dashboard.security;

import com.paytm.pgdashboard.commons.dto.Merchant;
import com.paytm.pgdashboard.commons.dto.Role;
import com.paytm.pgdashboard.commons.dto.User;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class UserDetailConfig implements UserDetails {

	private static final long serialVersionUID = 1L;
	private User user;
	private boolean isSuperUser;

	public UserDetailConfig(User user, boolean isSuperUser) {
		this.user = user;
		this.isSuperUser = isSuperUser;
	}

	@Override
	public Collection<? extends GrantedAuthority> getAuthorities() {
		Set<SimpleGrantedAuthority> accessList = new HashSet<SimpleGrantedAuthority>();

		List<Merchant> merchants = user.getMerchants();
		Merchant merchantExists = null;
		if (CollectionUtils.isNotEmpty(merchants)) {
			for (Merchant merchant : merchants) {
				if (merchant.getId() == this.user.getCurrentMerchant()) {
					merchantExists = merchant;
				}
			}
		}
		if (merchantExists != null) {
			List<Role> roles = merchantExists.getRoles();
			if (CollectionUtils.isNotEmpty(roles)) {
				for (Role role : roles) {
					List<String> permissions = role.getPermissions();
					if (CollectionUtils.isNotEmpty(permissions)) {
						for (String permission : permissions) {
							accessList.add(new SimpleGrantedAuthority("ROLE_" + permission));
						}
					}
				}
			}

		}
		return accessList;
	}

	@Override
	public String getPassword() {
		return "";
	}

	@Override
	public String getUsername() {
		return this.user.getUsername();
	}

	@Override
	public boolean isAccountNonExpired() {
		return true;
	}

	@Override
	public boolean isAccountNonLocked() {
		return true;
	}

	@Override
	public boolean isCredentialsNonExpired() {
		return true;
	}

	@Override
	public boolean isEnabled() {
		return true;
	}

	public User getUser() {
		return user;
	}

	public boolean isSuperUser() {
		return isSuperUser;
	}


}
